<div *ngIf="recommendedUnits?.length > 0">
  <div class="table-responsive">
    <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
      <thead>
        <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
          <th class="w-25px ps-4 rounded-start">
            <div class="form-check form-check-sm form-check-custom form-check-solid">
              <input class="form-check-input" type="checkbox" [checked]="isAllSelected"
                (change)="toggleAllUnits($event)" />
            </div>
          </th>
          <th class="min-w-100px cursor-pointer" (click)="sortData('unit_number')">
            Unit Number
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_number') }}</span>
          </th>
          <th class="min-w-100px cursor-pointer" (click)="sortData('floor')">
            Floor
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('floor') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('property_number')">
            Property Number
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('property_number') }}</span>
          </th>
          <th class="min-w-100px cursor-pointer" (click)="sortData('area')">
            Area
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('area') }}</span>
          </th>
          <th class="min-w-50px cursor-pointer" (click)="sortData('rooms')">
            Rooms
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('rooms') }}</span>
          </th>
          <th class="min-w-50px cursor-pointer" (click)="sortData('bathrooms')">
            Bathrooms
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('bathrooms') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('view')">
            View
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('view') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('delivery_date')">
            Delivery Date
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('delivery_date') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('finishing_status')">
            Finishing Status
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('finishing_status') }}</span>
          </th>
          <th class="min-w-150px">
            Unit Plan
          </th>
          <th class="min-w-250px cursor-pointer" (click)="sortData('unit_location')">
            Unit Location in Master Plan
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_location') }}</span>
          </th>
          <th class="min-w-200px cursor-pointer" (click)="sortData('price_per_meter_cash')">
            Pricer Per Meter in Cash
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('price_per_meter_cash') }}</span>
          </th>
          <th class="min-w-250px cursor-pointer" (click)="sortData('price_per_meter_installment')">
            Pricer Per Meter in Installment
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('price_per_meter_installment') }}</span>
          </th>
          <th class="min-w-150px cursor-pointer" (click)="sortData('total_price_cash')">
            Total Price Cash
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('total_price_cash') }}</span>
          </th>
          <th class="min-w-200px cursor-pointer" (click)="sortData('total_price_installment')">
            Total Pricer Installment
            <span class="ms-1 text-primary fw-bold">{{ getSortArrow('total_price_installment') }}</span>
          </th>
          <th class="min-w-150px">Other Accessories</th>
          <th class="min-w-50px text-end rounded-end pe-4">Status</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let unit of recommendedUnits">
          <td class="ps-4">
            <div class="form-check form-check-sm form-check-custom form-check-solid">
              <input class="form-check-input widget-13-check" type="checkbox" [checked]="isUnitSelected(unit.id)"
                (change)="onUnitCheckboxChange($event, unit.id)" />
            </div>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.unitNumber }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.floor }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5"> {{ unit.buildingNumber }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.unitArea | number:'1.0-2' }} m²</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.numberOfRooms }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.numberOfBathrooms }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.view }}</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ unit.deliveryDate }}</span>
          </td>
          <td>
            <span *ngIf="unit.finishingType === 'On Brick'" class="badge badge-light-danger fs-5 p-3">
              {{ unit.finishingType }}
            </span>
            <span *ngIf="unit.finishingType === 'Semi finished'" class="badge badge-light-danger fs-5 p-3">
              {{ unit.finishingType }}
              </span>
            <span *ngIf="unit.finishingType === 'Company finished'" class="badge badge-light-success fs-5 p-3">
              {{ unit.finishingType }}
            </span>
            <span *ngIf="unit.finishingType === 'Super Lux'" class="badge badge-light-info fs-5 p-3">
              {{ unit.finishingType }}
            </span>
            <span *ngIf="unit.finishingType === 'Ultra Super Lux'" class="badge badge-light-info fs-5 p-3">
              {{ unit.finishingType }}
            </span>
            <span *ngIf="unit.finishingType === 'Standard'" class="badge badge-light-info fs-5">
              {{ unit.finishingType }}
            </span>
          </td>
          <td>
            <button class="btn btn-sm btn-light-info fs-5" (click)="showUnitPlanModal(unit.diagram)">
              <i class="fa-solid fa-file-image me-1"></i> View Plan
            </button>
          </td>
          <td>
            <button class="btn btn-sm btn-light-info fs-5" (click)="showUnitPlanModal(unit.locationInMasterPlan)">
              <i class="fa-solid fa-file-image me-1"></i> View location
            </button>
          </td>
          <td>
            <span class="badge badge-light-warning fw-bold fs-5 p-3">{{ unit.pricePerMeterInCash ?? 0 }} EGP</span>
          </td>
          <td>
            <span class="badge badge-light-primary fw-bold fs-5 p-3">{{ unit.pricePerMeterInInstallment ?? 0 }} EGP</span>
          </td>
          <td>
            <span class="badge badge-light-warning fw-bold fs-5 p-3">{{ unit.totalPriceInCash ?? 0 }} EGP</span>
          </td>
          <td>
            <span class="badge badge-light-primary fw-bold fs-5 p-3">{{ unit.totalPriceInInstallment ?? 0 }} EGP</span>
          </td>
          <td>
            <span class="text-gray-900 fw-bold fs-5">{{ formatAccessories(unit.otherAccessories) }}</span>
          </td>
          <td class="text-end pe-4">
            <span *ngIf="unit.status === 'sold'" class="badge badge-light-danger fw-bold fs-5 p-3">{{ unit.status }}</span>
            <span *ngIf="unit.status === 'available'" class="badge badge-light-warning fw-bold fs-5 p-3">{{ unit.status }}</span>
            <span *ngIf="unit.status === 'new'" class="badge badge-light-success fw-bold fs-5 p-3">{{ unit.status }}</span>
            <span *ngIf="unit.status === 'reserved'" class="badge badge-light-info fw-bold fs-5 p-3">{{ unit.status }}</span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="m-2">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.limit" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>

  <div class="text-center mt-3 mb-3">
    <a class="btn btn-md btn-dark-blue fw-bold" (click)="makeReply()" [class.disabled]="isReplied">
      <i class="fa-solid fa-reply-all text-white me-1"></i>
      Reply
    </a>
  </div>

  <!-- View Unit Plan Modal -->
  <div class="modal fade" id="viewUnitPlanModal" tabindex="-1" aria-labelledby="unitPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="unitPlanModalLabel">View</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body text-center">
          <div *ngIf="selectedUnitPlanImage; else noImage">
            <img [src]="selectedUnitPlanImage" alt="Unit Diagram" class="img-fluid rounded" style="max-height: 500px" />
            <div class="mt-3">
              <p class="text-muted">Image</p>
            </div>
          </div>
          <ng-template #noImage>
            <div class="alert alert-warning">No available</div>
          </ng-template>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="recommendedUnits?.length == 0">
  <div class="row mb-5">
    <div class="col-md-5">
      <div class="d-flex align-items-center bg-light-dark-blue rounded p-5" role="alert" aria-live="polite">
        <span class="svg-icon text-info me-5" aria-hidden="true">
            <i class="fas fa-exclamation-circle ms-1 fs-5 text-dark-blue"></i>
        </span>
        <div class="flex-grow-1 me-2">
          <span class="fw-bolder text-dark-blue fs-6">
            No Matching Units Available
          </span>
          <span class="text-muted fw-bold d-block">
            Based on your private properties and available units from contracted developers, no recommendations could be made.
          </span>
        </div>
      </div>
    </div>
    <div class="col-md-7"></div>
  </div>
</div>
