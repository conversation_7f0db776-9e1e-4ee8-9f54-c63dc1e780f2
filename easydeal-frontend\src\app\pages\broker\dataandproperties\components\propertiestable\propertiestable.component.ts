import { ChangeDetectorRef, Component, Input, SimpleChanges } from '@angular/core';
import { Modal } from 'bootstrap';
import { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';
import { UnitService } from '../../../services/unit.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';


@Component({
  selector: 'app-propertiestable',
  templateUrl: './propertiestable.component.html',
  styleUrl: './propertiestable.component.scss',
})
export class PropertiestableComponent extends BaseGridComponent {

  //session
  brokerId: number;

  @Input() appliedFilters: any;
  selectedImage: string | null = null;
  selectedLocation: SafeResourceUrl | null = null;

  constructor(
    protected cd: ChangeDetectorRef,
    protected unitService: UnitService,
    private sanitizer: DomSanitizer,
    private router: Router
  ) {
    super(cd);
    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.brokerId = user?.brokerId;
    this.setService(unitService);
    this.orderBy = 'id';
    this.orderDir = 'desc';
    this.page.filters = { brokerId: this.brokerId };
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.appliedFilters && !changes.appliedFilters.firstChange) {
      this.page.filters = { brokerId: this.brokerId, ...this.appliedFilters}
      this.reloadTable(this.page);
    }
  }

  // Get columns to show based on applied filters
  getColumnsToShow(): string[] {
    const compoundType = this.appliedFilters?.compoundType;
    const type = this.appliedFilters?.type;

    // Base columns that always show
    const baseColumns = ['unit', 'city', 'area', 'location', 'status', 'actions'];

    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {
      return [...baseColumns, 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryDate', 'legalStatus', 'otherAccessories'];
    }
    else if (compoundType === 'outside_compound' && (type === 'villas' || type === 'full_buildings')) {
      return [...baseColumns, 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories'];
    }
    else if (compoundType === 'outside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {
      return [...baseColumns, 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition', 'deliveryDate', 'activity', 'financialStatus', 'otherAccessories'];
    }
    else if (compoundType === 'outside_compound' && (type === 'warehouses' || type === 'factories')) {
      return [...baseColumns, 'buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories'];
    }
    else if (compoundType === 'outside_compound' && (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' || type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands')) {
      return [...baseColumns, 'unitNumber', 'groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'legalStatus', 'deliveryDate', 'financialStatus', 'otherAccessories'];
    }
    else if (compoundType === 'inside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'i_villa')) {
      return [...baseColumns, 'compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryDate', 'financialStatus', 'otherAccessories'];
    }
    else if (compoundType === 'inside_compound' && (type === 'standalone_villas' || type === 'twin_houses' || type === 'town_houses')) {
      return [...baseColumns, 'compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryDate', 'financialStatus', 'otherAccessories'];
    }
    else if (compoundType === 'inside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {
      return [...baseColumns, 'compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'deliveryDate', 'fitOutCondition', 'financialStatus', 'otherAccessories'];
    }
    else if (compoundType === 'village' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'basement' || type === 'roofs' || type === 'i_villa' || type === 'villas' || type === 'standalone_villas' || type === 'full_buildings' || type === 'twin_houses' || type === 'town_houses' || type === 'chalets')) {
      return [...baseColumns, 'buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'dailyRent', 'monthlyRent'];
    }
    else if (compoundType === 'village' && (type === 'warehouses' || type === 'factories' || type === 'administrative_units' || type === 'medical_clinics' || type === 'commercial_stores' || type === 'pharmacies')) {
      return [...baseColumns, 'buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'activity', 'dailyRent', 'monthlyRent'];
    }

    // Default columns when no specific filter is applied
    return [...baseColumns, 'buildingNumber', 'view', 'floor', 'deliveryDate', 'finishingType', 'unitPlan', 'otherAccessories'];
  }

  // Check if a specific column should be shown
  shouldShowColumn(columnName: string): boolean {
    return this.getColumnsToShow().includes(columnName);
  }

  showImageModal(location: string) {
    if (!location || location.trim() === '') {
      Swal.fire({
        title: 'Warning',
        text: 'No location available',
        icon: 'warning',
        confirmButtonText: 'OK',
      });
      return;
    }
    if (
      location.includes('maps.google.com') ||
      location.includes('maps.app.goo.gl')
    ) {
      window.open(location, '_blank');
      return;
    }

    const mapUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
      location
    )}`;
    window.open(mapUrl, '_blank');
  }

  //****************************** */

   selectedUnitPlanImage: string | null = null;

  showUnitPlanModal(imgPath: string) {
    this.selectedUnitPlanImage = imgPath;

    const modalElement = document.getElementById('viewUnitPlanModal');
    if (modalElement) {
      const modal = new Modal(modalElement);
      modal.show();
    }
  }

  viewProperty(unitService: any) {
    this.router.navigate(['/developer/projects/models/units/details'], {
      queryParams: { unitId: unitService.id }
    });

  }
//************************************** */

  //  sortData(column: string) {
  //    if (this.orderBy === column) {
  //     this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';
  //   } else {
  //     this.orderBy = column;
  //     this.orderDir = 'asc';
  //   }

  //    this.page.orderBy = this.orderBy;
  //   this.page.orderDir = this.orderDir;
  //   this.page.pageNumber = 0;
  //   this.reloadTable(this.page);
  // }

  //  getSortArrow(column: string): string {
  //   if (this.orderBy !== column) {
  //     return '';
  //   }
  //   return this.orderDir === 'asc' ? '↑' : '↓';
  // }
}
