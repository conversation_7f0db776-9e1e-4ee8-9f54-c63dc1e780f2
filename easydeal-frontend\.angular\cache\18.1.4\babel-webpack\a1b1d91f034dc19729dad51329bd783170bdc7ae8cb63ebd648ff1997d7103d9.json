{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthenticationService {\n  http;\n  apiUrl = `${environment.apiUrl}/auth`;\n  currentUserSubject = new BehaviorSubject(null);\n  currentUser$ = this.currentUserSubject.asObservable();\n  constructor(http) {\n    this.http = http;\n  }\n  login(credentials) {\n    return this.http.post(`${this.apiUrl}/login`, credentials);\n  }\n  register(userData) {\n    return this.http.post(`${this.apiUrl}/register`, userData);\n  }\n  sendOtp(credentials) {\n    return this.http.post(`${this.apiUrl}/send-otp`, credentials);\n  }\n  checkOtp(credentials) {\n    return this.http.post(`${this.apiUrl}/check-otp`, credentials);\n  }\n  resetPassword(credentials) {\n    return this.http.post(`${this.apiUrl}/reset-password`, credentials);\n  }\n  logout() {\n    return this.http.post(`${this.apiUrl}/logout`, {});\n  }\n  setCurrentUser(user) {\n    this.currentUserSubject.next(user);\n    localStorage.setItem('currentUser', JSON.stringify(user));\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  getSessionUser() {\n    const user = localStorage.getItem('currentUser');\n    return user ? JSON.parse(user) : null;\n  }\n  static ɵfac = function AuthenticationService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthenticationService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthenticationService,\n    factory: AuthenticationService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "environment", "AuthenticationService", "http", "apiUrl", "currentUserSubject", "currentUser$", "asObservable", "constructor", "login", "credentials", "post", "register", "userData", "sendOtp", "checkOtp", "resetPassword", "logout", "setCurrentUser", "user", "next", "localStorage", "setItem", "JSON", "stringify", "getCurrentUser", "value", "getSessionUser", "getItem", "parse", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\services\\authentication.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable, BehaviorSubject } from 'rxjs';\r\nimport { map, tap } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment';\r\n\r\nexport interface LoginRequest {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport interface RegisterRequest {\r\n  firstName: string;\r\n  lastName: string;\r\n  email: string;\r\n  password: string;\r\n  phone: string;\r\n}\r\n\r\nexport interface AuthResponse {\r\n  success: boolean;\r\n  message: string;\r\n  data?: any;\r\n  token?: string;\r\n}\r\n\r\nexport interface User {\r\n  id: number;\r\n  firstName: string;\r\n  lastName: string;\r\n  email: string;\r\n  phone: string;\r\n  role: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthenticationService {\r\n  private apiUrl = `${environment.apiUrl}/auth` ;\r\n\r\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  login(credentials: LoginRequest): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.apiUrl}/login`, credentials);\r\n  }\r\n\r\n  register(userData: any): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.apiUrl}/register`, userData);\r\n  }\r\n\r\n  sendOtp(credentials: any): Observable<any> {\r\n    return this.http.post(`${this.apiUrl}/send-otp`, credentials);\r\n  }\r\n\r\n  checkOtp(credentials: any): Observable<any> {\r\n    return this.http.post(`${this.apiUrl}/check-otp`, credentials);\r\n  }\r\n\r\n  resetPassword(credentials: any): Observable<any> {\r\n    return this.http.post(`${this.apiUrl}/reset-password`, credentials);\r\n  }\r\n\r\n  logout(): Observable<AuthResponse> {\r\n    return this.http.post<AuthResponse>(`${this.apiUrl}/logout`, {});\r\n  }\r\n\r\n  setCurrentUser(user: any): void {\r\n    this.currentUserSubject.next(user);\r\n    localStorage.setItem('currentUser', JSON.stringify(user));\r\n  }\r\n\r\n  getCurrentUser(): any {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n  getSessionUser(): any {\r\n    const user = localStorage.getItem('currentUser');\r\n    return user ? JSON.parse(user) : null;\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAAqBA,eAAe,QAAQ,MAAM;AAElD,SAASC,WAAW,QAAQ,8BAA8B;;;AAkC1D,OAAM,MAAOC,qBAAqB;EAMZC,IAAA;EALZC,MAAM,GAAG,GAAGH,WAAW,CAACG,MAAM,OAAO;EAErCC,kBAAkB,GAAG,IAAIL,eAAe,CAAc,IAAI,CAAC;EAC5DM,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;EAE5DC,YAAoBL,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvCM,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAe,GAAG,IAAI,CAACP,MAAM,QAAQ,EAAEM,WAAW,CAAC;EAC1E;EAEAE,QAAQA,CAACC,QAAa;IACpB,OAAO,IAAI,CAACV,IAAI,CAACQ,IAAI,CAAe,GAAG,IAAI,CAACP,MAAM,WAAW,EAAES,QAAQ,CAAC;EAC1E;EAEAC,OAAOA,CAACJ,WAAgB;IACtB,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACP,MAAM,WAAW,EAAEM,WAAW,CAAC;EAC/D;EAEAK,QAAQA,CAACL,WAAgB;IACvB,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACP,MAAM,YAAY,EAAEM,WAAW,CAAC;EAChE;EAEAM,aAAaA,CAACN,WAAgB;IAC5B,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACP,MAAM,iBAAiB,EAAEM,WAAW,CAAC;EACrE;EAEAO,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACd,IAAI,CAACQ,IAAI,CAAe,GAAG,IAAI,CAACP,MAAM,SAAS,EAAE,EAAE,CAAC;EAClE;EAEAc,cAAcA,CAACC,IAAS;IACtB,IAAI,CAACd,kBAAkB,CAACe,IAAI,CAACD,IAAI,CAAC;IAClCE,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC,CAAC;EAC3D;EAEAM,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACpB,kBAAkB,CAACqB,KAAK;EACtC;EAEAC,cAAcA,CAAA;IACZ,MAAMR,IAAI,GAAGE,YAAY,CAACO,OAAO,CAAC,aAAa,CAAC;IAChD,OAAOT,IAAI,GAAGI,IAAI,CAACM,KAAK,CAACV,IAAI,CAAC,GAAG,IAAI;EACvC;;qCA5CWjB,qBAAqB,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;;WAArB/B,qBAAqB;IAAAgC,OAAA,EAArBhC,qBAAqB,CAAAiC,IAAA;IAAAC,UAAA,EAFpB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}