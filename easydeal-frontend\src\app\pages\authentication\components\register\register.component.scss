:host {
  display: block;
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-image: url("/angular/assets/media/login/EaseDealPage.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  text-align: right;
  position: relative;
}

.register-card {
  width: 100%;
  max-width: 450px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: slideUp 0.6s ease-out;
  position: relative;
  z-index: 9999;
  margin-right: 100px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);
  padding: 40px;
  direction: rtl;
  text-align: right;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// User Type Selection Styles
.user-type-selection {
  text-align: center;

  .form-title {
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
  }

  .form-subtitle {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 30px;
  }

  .user-type-options {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-bottom: 30px;

    .user-type-option {
      flex: 1;
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 15px;
      padding: 10px 15px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;

      &:hover {
        border-color: #667eea;
        background: #f0f2ff;
      }

      &.selected {
        border-color: #667eea;
        background: #2a44b6;
        color: white;

        .option-icon i {
          color: white;
        }
      }

      .option-icon {
        margin: 0;

        i {
          color: #2a44b6;
          transition: color 0.3s ease;
          font-size: 1.2rem;
        }
      }

      .option-label {
        font-weight: 500;
        font-size: 0.9rem;
        margin: 0;
      }
    }
  }

  .continue-btn {
    width: 100%;
    padding: 15px;
    background: #1e35a6;
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-bottom: 20px;

    &:hover:not(:disabled) {
      background: #667eea;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .login-link-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;

    .login-text {
      color: #333;
      font-size: 0.9rem;
    }

    .login-link {
      color: #667eea;
      text-decoration: none;

      i {
        font-size: 1rem;
      }

      &:hover {
        color: #5a6fd8;
      }
    }
  }

  .support-link {
    text-align: center;

    .support-text {
      color: #28a745;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Registration Stepper Step Styles
.registration-stepper-step {
  width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .register-container {
    padding: 10px;
    justify-content: center;
  }

  .register-card {
    max-width: 100%;
    border-radius: 10px;
    margin-right: 0;
    padding: 30px 20px;
  }

  .welcome-header {
    .welcome-title {
      font-size: 2rem;
    }
  }

  .user-type-selection {
    .user-type-options {
      flex-direction: column;
      gap: 10px;

      .user-type-option {
        padding: 15px;
      }
    }
  }
}

@media (max-width: 480px) {
  .register-card {
    margin: 0 10px;
    max-width: calc(100% - 20px);
    padding: 20px 15px;
  }

  .welcome-header {
    .welcome-title {
      font-size: 1.5rem;
    }
  }

  .user-type-selection {
    .form-title {
      font-size: 1.2rem;
    }

    .form-subtitle {
      font-size: 0.8rem;
    }
  }
}

.register-container {
  @media (max-width: 1200px) {
    background-position: center left;
  }

  @media (max-width: 768px) {
    background-position: center;
  }
}
