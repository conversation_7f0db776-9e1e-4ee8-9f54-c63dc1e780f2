{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AuthenticationLayoutComponent {\n  constructor() {}\n  ngOnInit() {}\n  static ɵfac = function AuthenticationLayoutComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthenticationLayoutComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AuthenticationLayoutComponent,\n    selectors: [[\"app-authentication-layout\"]],\n    decls: 3,\n    vars: 0,\n    consts: [[1, \"auth-layout\"], [1, \"auth-container\"]],\n    template: function AuthenticationLayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"router-outlet\");\n        i0.ɵɵelementEnd()();\n      }\n    },\n    dependencies: [i1.RouterOutlet],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: 100vh;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  z-index: 9999;\\n}\\n\\n.auth-layout[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  background-image: url(\\\"/angular/assets/media/login/EaseDealPage.png\\\");\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n  padding: 20px;\\n  font-family: \\\"Segoe UI\\\", Tahoma, Geneva, Verdana, sans-serif;\\n  direction: rtl;\\n  text-align: right;\\n  position: relative;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .auth-layout[_ngcontent-%COMP%] {\\n    background-position: center left;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .auth-layout[_ngcontent-%COMP%] {\\n    padding: 10px;\\n    justify-content: center;\\n    background-position: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["AuthenticationLayoutComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "consts", "template", "AuthenticationLayoutComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\authentication-layout.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\authentication-layout.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-authentication-layout',\r\n  templateUrl: './authentication-layout.component.html',\r\n  styleUrls: ['./authentication-layout.component.scss']\r\n})\r\nexport class AuthenticationLayoutComponent implements OnInit {\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n}\r\n", "<!-- Authentication Layout without Sidebar -->\r\n<div class=\"auth-layout\">\r\n  <div class=\"auth-container\">\r\n    <router-outlet></router-outlet>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,6BAA6B;EAExCC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;;qCALWF,6BAA6B;EAAA;;UAA7BA,6BAA6B;IAAAG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCLxCE,EADF,CAAAC,cAAA,aAAyB,aACK;QAC1BD,EAAA,CAAAE,SAAA,oBAA+B;QAEnCF,EADE,CAAAG,YAAA,EAAM,EACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}