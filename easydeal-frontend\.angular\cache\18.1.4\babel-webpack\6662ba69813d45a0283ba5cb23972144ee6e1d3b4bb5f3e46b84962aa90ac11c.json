{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/taskes/New folder/easydeal-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from 'src/app/_metronic/shared/shared.module';\nimport { AccountTypeMapper } from 'src/app/pages/broker/account-type-mapper';\nimport Swal from 'sweetalert2';\nimport { BaseLoading } from '../../../base-loading/base-loading';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/profile.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../../../../_metronic/shared/keenicon/keenicon.component\";\nfunction ProfileHeaderComponent_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 22);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.user.image, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileHeaderComponent_label_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 23)(1, \"i\", 24);\n    i0.ɵɵelement(2, \"span\", 25)(3, \"span\", 26);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileHeaderComponent_input_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 27);\n    i0.ɵɵlistener(\"change\", function ProfileHeaderComponent_input_8_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onProfileImageChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileHeaderComponent_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getAccountTypeBadge(ctx_r0.user.accountType));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.capitalizeWords(ctx_r0.user == null ? null : ctx_r0.user.accountType), \" \");\n  }\n}\nfunction ProfileHeaderComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"a\", 30);\n    i0.ɵɵtext(2, \" Upgrade Plan \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileHeaderComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33)(3, \"div\", 34)(4, \"div\", 35)(5, \"div\", 36);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 37);\n    i0.ɵɵtext(8, \"Specializations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 34)(10, \"div\", 35)(11, \"div\", 36);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 37);\n    i0.ɵɵtext(14, \"Locations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 34)(16, \"div\", 35)(17, \"div\", 36);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 37);\n    i0.ɵɵtext(20, \"Operations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 34)(22, \"div\", 35)(23, \"div\", 36);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 37);\n    i0.ɵɵtext(26, \"Advertisements\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.user.specializationsCount, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.user.areasCount);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.user.operationCount);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.user.specializationAdvertisementCount, \" \");\n  }\n}\nexport class ProfileHeaderComponent extends BaseLoading {\n  cd;\n  ProfileService;\n  user = {};\n  constructor(cd, ProfileService) {\n    super();\n    this.cd = cd;\n    this.ProfileService = ProfileService;\n  }\n  capitalizeWords(text) {\n    if (!text) return '';\n    return text.replace(/\\b\\w/g, char => char.toUpperCase());\n  }\n  getAccountTypeBadge(type) {\n    return AccountTypeMapper.getAccountTypeBadge(type);\n  }\n  onProfileImageChange(event) {\n    var _this = this;\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      const image = input.files[0];\n      console.log('Selected image:', image);\n      this.startLoading();\n      const formData = new FormData();\n      formData.append('image', image);\n      this.ProfileService.updateProfileImage(this.user.id, formData).subscribe({\n        next: function () {\n          var _ref = _asyncToGenerator(function* (response) {\n            localStorage.setItem('currentUser', JSON.stringify(response.data));\n            _this.stopLoading();\n            _this.cd.detectChanges();\n            yield Swal.fire('تم تحديث صورة الملف الشخصي بنجاح', '', 'success');\n            window.location.reload();\n          });\n          return function next(_x) {\n            return _ref.apply(this, arguments);\n          };\n        }(),\n        error: error => {\n          this.stopLoading();\n          console.error('Error updating profile image:', error);\n          Swal.fire('فشل تحديث صورة الملف الشخصي', '', 'error');\n        }\n      });\n    }\n  }\n  static ɵfac = function ProfileHeaderComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProfileHeaderComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ProfileService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProfileHeaderComponent,\n    selectors: [[\"app-profile-header\"]],\n    inputs: {\n      user: \"user\"\n    },\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n    decls: 28,\n    vars: 10,\n    consts: [[1, \"card\", \"mb-5\", \"mb-xl-10\"], [1, \"card-body\", \"pt-9\", \"pb-0\"], [1, \"d-flex\", \"flex-wrap\", \"flex-sm-nowrap\", \"mb-3\"], [1, \"me-7\", \"mb-4\"], [1, \"symbol\", \"symbol-100px\", \"symbol-lg-160px\", \"symbol-fixed\", \"position-relative\"], [\"alt\", \"Profile Image\", 3, \"src\", 4, \"ngIf\"], [1, \"position-absolute\", \"bottom-0\", \"start-50\", \"translate-middle-x\", \"mb-2\"], [\"class\", \"btn btn-sm btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow\", \"for\", \"profile-image-input\", \"data-bs-toggle\", \"tooltip\", \"title\", \"Change Profile Picture\", 4, \"ngIf\"], [\"type\", \"file\", \"id\", \"profile-image-input\", \"class\", \"d-none\", \"accept\", \".png, .jpg, .jpeg\", 3, \"change\", 4, \"ngIf\"], [1, \"flex-grow-1\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"flex-wrap\", \"mb-2\"], [1, \"d-flex\", \"flex-column\"], [1, \"d-flex\", \"align-items-center\", \"mb-2\"], [1, \"text-gray-800\", \"fs-2\", \"fw-bolder\", \"me-1\", \"cursor-pointer\"], [\"class\", \"badge fw-bolder ms-2 fs-6 py-1 px-3\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-wrap\", \"fw-bold\", \"fs-6\", \"mb-4\", \"pe-2\"], [1, \"d-flex\", \"align-items-center\", \"text-gray-500\", \"text-hover-primary\", \"me-5\", \"mb-2\", \"cursor-pointer\"], [\"name\", \"phone\", 1, \"fs-4\", \"me-1\", \"text-mid-blue\"], [\"name\", \"sms\", 1, \"fs-4\", \"me-1\", \"text-mid-blue\"], [\"name\", \"user\", 1, \"fs-4\", \"me-1\", \"text-mid-blue\"], [\"class\", \"d-flex my-4\", 4, \"ngIf\"], [\"class\", \"d-flex flex-wrap flex-stack\", 4, \"ngIf\"], [\"alt\", \"Profile Image\", 3, \"src\"], [\"for\", \"profile-image-input\", \"data-bs-toggle\", \"tooltip\", \"title\", \"Change Profile Picture\", 1, \"btn\", \"btn-sm\", \"btn-icon\", \"btn-circle\", \"btn-active-color-primary\", \"w-25px\", \"h-25px\", \"bg-body\", \"shadow\"], [1, \"bi\", \"bi-camera-fill\", \"fs-7\", \"text-primary\"], [1, \"path1\"], [1, \"path2\"], [\"type\", \"file\", \"id\", \"profile-image-input\", \"accept\", \".png, .jpg, .jpeg\", 1, \"d-none\", 3, \"change\"], [1, \"badge\", \"fw-bolder\", \"ms-2\", \"fs-6\", \"py-1\", \"px-3\", 3, \"ngClass\"], [1, \"d-flex\", \"my-4\"], [\"data-bs-toggle\", \"modal\", \"data-bs-target\", \"#kt_modal_offer_a_deal\", 1, \"btn\", \"btn-md\", \"btn-dark-blue\", \"btn-active-light-dark-blue\", \"me-3\", \"cursor-pointer\"], [1, \"d-flex\", \"flex-wrap\", \"flex-stack\"], [1, \"d-flex\", \"flex-column\", \"flex-grow-1\", \"pe-8\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"border\", \"border-gray-300\", \"border-dashed\", \"rounded\", \"min-w-125px\", \"py-3\", \"px-4\", \"me-6\", \"mb-3\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fs-2\", \"fw-bolder\"], [1, \"fw-bold\", \"fs-6\", \"text-gray-500\"]],\n    template: function ProfileHeaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵtemplate(5, ProfileHeaderComponent_img_5_Template, 1, 1, \"img\", 5);\n        i0.ɵɵelementStart(6, \"div\", 6);\n        i0.ɵɵtemplate(7, ProfileHeaderComponent_label_7_Template, 4, 0, \"label\", 7)(8, ProfileHeaderComponent_input_8_Template, 1, 0, \"input\", 8);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"span\", 13);\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(15, ProfileHeaderComponent_span_15_Template, 2, 2, \"span\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"div\", 15)(17, \"a\", 16);\n        i0.ɵɵelement(18, \"app-keenicon\", 17);\n        i0.ɵɵtext(19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"a\", 16);\n        i0.ɵɵelement(21, \"app-keenicon\", 18);\n        i0.ɵɵtext(22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"a\", 16);\n        i0.ɵɵelement(24, \"app-keenicon\", 19);\n        i0.ɵɵtext(25);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(26, ProfileHeaderComponent_div_26_Template, 3, 0, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(27, ProfileHeaderComponent_div_27_Template, 27, 4, \"div\", 21);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.user.image);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.user.role === \"broker\" || ctx.user.role === \"client\" || ctx.user.role === \"developer\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.user.role === \"broker\" || ctx.user.role === \"client\" || ctx.user.role === \"developer\");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\"\", ctx.capitalizeWords(ctx.user.fullName), \" \");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.user.role === \"broker\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.phone, \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.email, \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.capitalizeWords(ctx.user.role), \" \");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.user.role === \"broker\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.user.role === \"broker\");\n      }\n    },\n    dependencies: [CommonModule, i2.NgClass, i2.NgIf, SharedModule, i3.KeeniconComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "SharedModule", "AccountTypeMapper", "<PERSON><PERSON>", "BaseLoading", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "user", "image", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵlistener", "ProfileHeaderComponent_input_8_Template_input_change_0_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onProfileImageChange", "ɵɵtext", "getAccountTypeBadge", "accountType", "ɵɵadvance", "ɵɵtextInterpolate1", "capitalizeWords", "specializationsCount", "ɵɵtextInterpolate", "areasCount", "operationCount", "specializationAdvertisementCount", "ProfileHeaderComponent", "cd", "ProfileService", "constructor", "text", "replace", "char", "toUpperCase", "type", "event", "_this", "input", "target", "files", "length", "console", "log", "startLoading", "formData", "FormData", "append", "updateProfileImage", "id", "subscribe", "next", "_ref", "_asyncToGenerator", "response", "localStorage", "setItem", "JSON", "stringify", "data", "stopLoading", "detectChanges", "fire", "window", "location", "reload", "_x", "apply", "arguments", "error", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "selectors", "inputs", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProfileHeaderComponent_Template", "rf", "ctx", "ɵɵtemplate", "ProfileHeaderComponent_img_5_Template", "ProfileHeaderComponent_label_7_Template", "ProfileHeaderComponent_input_8_Template", "ProfileHeaderComponent_span_15_Template", "ProfileHeaderComponent_div_26_Template", "ProfileHeaderComponent_div_27_Template", "role", "fullName", "phone", "email", "i2", "Ng<PERSON><PERSON>", "NgIf", "i3", "KeeniconComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\shared\\profile\\components\\profile-header\\profile-header.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\shared\\profile\\components\\profile-header\\profile-header.component.html"], "sourcesContent": ["import { environment } from './../../../../../../environments/environment.prod';\r\nimport { ChangeDetectorRef, Component, Input } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedModule } from 'src/app/_metronic/shared/shared.module';\r\nimport { AccountTypeMapper } from 'src/app/pages/broker/account-type-mapper';\r\nimport { ProfileService } from '../../services/profile.service';\r\nimport Swal from 'sweetalert2';\r\nimport { BaseLoading } from '../../../base-loading/base-loading';\r\n\r\n@Component({\r\n  selector: 'app-profile-header',\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule],\r\n  templateUrl: './profile-header.component.html',\r\n  styleUrl: './profile-header.component.scss',\r\n})\r\nexport class ProfileHeaderComponent extends BaseLoading {\r\n  @Input() user: any = {};\r\n\r\n  constructor(\r\n    private cd: ChangeDetectorRef,\r\n    private ProfileService: ProfileService\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  capitalizeWords(text: string | null): string {\r\n    if (!text) return '';\r\n    return text.replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n  }\r\n\r\n  getAccountTypeBadge(type: string): string {\r\n    return AccountTypeMapper.getAccountTypeBadge(type);\r\n  }\r\n\r\n  onProfileImageChange(event: Event) {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      const image = input.files[0];\r\n      console.log('Selected image:', image);\r\n\r\n      this.startLoading();\r\n\r\n      const formData = new FormData();\r\n      formData.append('image', image);\r\n\r\n      this.ProfileService.updateProfileImage(this.user.id, formData).subscribe({\r\n        next:async (response: any) => {\r\n          localStorage.setItem('currentUser', JSON.stringify(response.data));\r\n          this.stopLoading();\r\n          this.cd.detectChanges();\r\n          await Swal.fire('تم تحديث صورة الملف الشخصي بنجاح', '', 'success');\r\n          window.location.reload();\r\n        },\r\n        error: (error: any) => {\r\n          this.stopLoading();\r\n          console.error('Error updating profile image:', error);\r\n          Swal.fire('فشل تحديث صورة الملف الشخصي', '', 'error');\r\n        },\r\n      });\r\n    }\r\n  }\r\n}\r\n", "<!-- header -->\r\n<div class=\"card mb-5 mb-xl-10\">\r\n  <div class=\"card-body pt-9 pb-0\">\r\n    <div class=\"d-flex flex-wrap flex-sm-nowrap mb-3\">\r\n      <div class=\"me-7 mb-4\">\r\n        <div\r\n          class=\"symbol symbol-100px symbol-lg-160px symbol-fixed position-relative\"\r\n        >\r\n          <img [src]=\"user.image\" alt=\"Profile Image\" *ngIf=\"user.image\" />\r\n\r\n          <div\r\n            class=\"position-absolute bottom-0 start-50 translate-middle-x mb-2\"\r\n          >\r\n            <label\r\n              class=\"btn btn-sm btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow\"\r\n              for=\"profile-image-input\"\r\n              data-bs-toggle=\"tooltip\"\r\n              title=\"Change Profile Picture\"\r\n              *ngIf=\"\r\n                user.role === 'broker' ||\r\n                user.role === 'client' ||\r\n                user.role === 'developer'\r\n              \"\r\n            >\r\n              <i class=\"bi bi-camera-fill fs-7 text-primary\">\r\n                <span class=\"path1\"></span>\r\n                <span class=\"path2\"></span>\r\n              </i>\r\n            </label>\r\n            <input\r\n              type=\"file\"\r\n              id=\"profile-image-input\"\r\n              class=\"d-none\"\r\n              accept=\".png, .jpg, .jpeg\"\r\n              (change)=\"onProfileImageChange($event)\"\r\n              *ngIf=\"\r\n                user.role === 'broker' ||\r\n                user.role === 'client' ||\r\n                user.role === 'developer'\r\n              \"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"flex-grow-1\">\r\n        <div\r\n          class=\"d-flex justify-content-between align-items-start flex-wrap mb-2\"\r\n        >\r\n          <div class=\"d-flex flex-column\">\r\n            <div class=\"d-flex align-items-center mb-2\">\r\n              <span class=\"text-gray-800 fs-2 fw-bolder me-1 cursor-pointer\"\r\n                >{{ capitalizeWords(user.fullName) }}\r\n              </span>\r\n              <span\r\n                class=\"badge fw-bolder ms-2 fs-6 py-1 px-3\"\r\n                *ngIf=\"user.role === 'broker'\"\r\n                [ngClass]=\"getAccountTypeBadge(user.accountType)\"\r\n              >\r\n                {{ capitalizeWords(user?.accountType) }}\r\n              </span>\r\n            </div>\r\n\r\n            <div class=\"d-flex flex-wrap fw-bold fs-6 mb-4 pe-2\">\r\n              <a class=\"d-flex align-items-center text-gray-500 text-hover-primary me-5 mb-2 cursor-pointer\">\r\n                <app-keenicon name=\"phone\" class=\"fs-4 me-1 text-mid-blue\"></app-keenicon>\r\n                {{ user.phone }}\r\n              </a>\r\n              <a class=\"d-flex align-items-center text-gray-500 text-hover-primary me-5 mb-2 cursor-pointer\">\r\n                <app-keenicon name=\"sms\" class=\"fs-4 me-1 text-mid-blue\"></app-keenicon>\r\n                {{ user.email }}\r\n              </a>\r\n              <a class=\"d-flex align-items-center text-gray-500 text-hover-primary me-5 mb-2 cursor-pointer\">\r\n                <app-keenicon name=\"user\" class=\"fs-4 me-1 text-mid-blue\"></app-keenicon>\r\n                {{ capitalizeWords(user.role) }}\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"d-flex my-4\" *ngIf=\"user.role === 'broker'\">\r\n            <a\r\n              class=\"btn btn-md btn-dark-blue btn-active-light-dark-blue me-3 cursor-pointer\"\r\n              data-bs-toggle=\"modal\"\r\n              data-bs-target=\"#kt_modal_offer_a_deal\"\r\n            >\r\n              Upgrade Plan\r\n            </a>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"d-flex flex-wrap flex-stack\" *ngIf=\"user.role === 'broker'\">\r\n          <div class=\"d-flex flex-column flex-grow-1 pe-8\">\r\n            <div class=\"d-flex flex-wrap\">\r\n              <div\r\n                class=\"border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3\"\r\n              >\r\n                <div class=\"d-flex align-items-center\">\r\n                  <div class=\"fs-2 fw-bolder\">\r\n                    {{ user.specializationsCount }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"fw-bold fs-6 text-gray-500\">Specializations</div>\r\n              </div>\r\n\r\n              <div\r\n                class=\"border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3\"\r\n              >\r\n                <div class=\"d-flex align-items-center\">\r\n                  <div class=\"fs-2 fw-bolder\">{{ user.areasCount }}</div>\r\n                </div>\r\n                <div class=\"fw-bold fs-6 text-gray-500\">Locations</div>\r\n              </div>\r\n\r\n              <div\r\n                class=\"border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3\"\r\n              >\r\n                <div class=\"d-flex align-items-center\">\r\n                  <div class=\"fs-2 fw-bolder\">{{ user.operationCount }}</div>\r\n                </div>\r\n                <div class=\"fw-bold fs-6 text-gray-500\">Operations</div>\r\n              </div>\r\n\r\n              <div\r\n                class=\"border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3\"\r\n              >\r\n                <div class=\"d-flex align-items-center\">\r\n                  <div class=\"fs-2 fw-bolder\">\r\n                    {{ user.specializationAdvertisementCount }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"fw-bold fs-6 text-gray-500\">Advertisements</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,iBAAiB,QAAQ,0CAA0C;AAE5E,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,WAAW,QAAQ,oCAAoC;;;;;;;ICCtDC,EAAA,CAAAC,SAAA,cAAiE;;;;IAA5DD,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,EAAAL,EAAA,CAAAM,aAAA,CAAkB;;;;;IAgBnBN,EAXF,CAAAO,cAAA,gBAUC,YACgD;IAE7CP,EADA,CAAAC,SAAA,eAA2B,eACA;IAE/BD,EADE,CAAAQ,YAAA,EAAI,EACE;;;;;;IACRR,EAAA,CAAAO,cAAA,gBAWE;IANAP,EAAA,CAAAS,UAAA,oBAAAC,gEAAAC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAH,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAUZ,MAAA,CAAAa,oBAAA,CAAAL,MAAA,CAA4B;IAAA,EAAC;IALzCX,EAAA,CAAAQ,YAAA,EAWE;;;;;IAcAR,EAAA,CAAAO,cAAA,eAIC;IACCP,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAQ,YAAA,EAAO;;;;IAHLR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAe,mBAAA,CAAAf,MAAA,CAAAC,IAAA,CAAAe,WAAA,EAAiD;IAEjDnB,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAqB,kBAAA,MAAAlB,MAAA,CAAAmB,eAAA,CAAAnB,MAAA,CAAAC,IAAA,kBAAAD,MAAA,CAAAC,IAAA,CAAAe,WAAA,OACF;;;;;IAoBFnB,EADF,CAAAO,cAAA,cAAwD,YAKrD;IACCP,EAAA,CAAAiB,MAAA,qBACF;IACFjB,EADE,CAAAQ,YAAA,EAAI,EACA;;;;;IAUER,EAPV,CAAAO,cAAA,cAAwE,cACrB,cACjB,cAG3B,cACwC,cACT;IAC1BP,EAAA,CAAAiB,MAAA,GACF;IACFjB,EADE,CAAAQ,YAAA,EAAM,EACF;IACNR,EAAA,CAAAO,cAAA,cAAwC;IAAAP,EAAA,CAAAiB,MAAA,sBAAe;IACzDjB,EADyD,CAAAQ,YAAA,EAAM,EACzD;IAMFR,EAJJ,CAAAO,cAAA,cAEC,eACwC,eACT;IAAAP,EAAA,CAAAiB,MAAA,IAAqB;IACnDjB,EADmD,CAAAQ,YAAA,EAAM,EACnD;IACNR,EAAA,CAAAO,cAAA,eAAwC;IAAAP,EAAA,CAAAiB,MAAA,iBAAS;IACnDjB,EADmD,CAAAQ,YAAA,EAAM,EACnD;IAMFR,EAJJ,CAAAO,cAAA,eAEC,eACwC,eACT;IAAAP,EAAA,CAAAiB,MAAA,IAAyB;IACvDjB,EADuD,CAAAQ,YAAA,EAAM,EACvD;IACNR,EAAA,CAAAO,cAAA,eAAwC;IAAAP,EAAA,CAAAiB,MAAA,kBAAU;IACpDjB,EADoD,CAAAQ,YAAA,EAAM,EACpD;IAMFR,EAJJ,CAAAO,cAAA,eAEC,eACwC,eACT;IAC1BP,EAAA,CAAAiB,MAAA,IACF;IACFjB,EADE,CAAAQ,YAAA,EAAM,EACF;IACNR,EAAA,CAAAO,cAAA,eAAwC;IAAAP,EAAA,CAAAiB,MAAA,sBAAc;IAI9DjB,EAJ8D,CAAAQ,YAAA,EAAM,EACxD,EACF,EACF,EACF;;;;IApCMR,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAqB,kBAAA,MAAAlB,MAAA,CAAAC,IAAA,CAAAmB,oBAAA,MACF;IAS4BvB,EAAA,CAAAoB,SAAA,GAAqB;IAArBpB,EAAA,CAAAwB,iBAAA,CAAArB,MAAA,CAAAC,IAAA,CAAAqB,UAAA,CAAqB;IASrBzB,EAAA,CAAAoB,SAAA,GAAyB;IAAzBpB,EAAA,CAAAwB,iBAAA,CAAArB,MAAA,CAAAC,IAAA,CAAAsB,cAAA,CAAyB;IAUnD1B,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAqB,kBAAA,MAAAlB,MAAA,CAAAC,IAAA,CAAAuB,gCAAA,MACF;;;ADhHlB,OAAM,MAAOC,sBAAuB,SAAQ7B,WAAW;EAI3C8B,EAAA;EACAC,cAAA;EAJD1B,IAAI,GAAQ,EAAE;EAEvB2B,YACUF,EAAqB,EACrBC,cAA8B;IAEtC,KAAK,EAAE;IAHC,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;EAGxB;EAEAR,eAAeA,CAACU,IAAmB;IACjC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,OAAOA,IAAI,CAACC,OAAO,CAAC,OAAO,EAAGC,IAAI,IAAKA,IAAI,CAACC,WAAW,EAAE,CAAC;EAC5D;EAEAjB,mBAAmBA,CAACkB,IAAY;IAC9B,OAAOvC,iBAAiB,CAACqB,mBAAmB,CAACkB,IAAI,CAAC;EACpD;EAEApB,oBAAoBA,CAACqB,KAAY;IAAA,IAAAC,KAAA;IAC/B,MAAMC,KAAK,GAAGF,KAAK,CAACG,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMrC,KAAK,GAAGkC,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAC5BE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEvC,KAAK,CAAC;MAErC,IAAI,CAACwC,YAAY,EAAE;MAEnB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE3C,KAAK,CAAC;MAE/B,IAAI,CAACyB,cAAc,CAACmB,kBAAkB,CAAC,IAAI,CAAC7C,IAAI,CAAC8C,EAAE,EAAEJ,QAAQ,CAAC,CAACK,SAAS,CAAC;QACvEC,IAAI;UAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAC,WAAOC,QAAa,EAAI;YAC3BC,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAACK,IAAI,CAAC,CAAC;YAClEtB,KAAI,CAACuB,WAAW,EAAE;YAClBvB,KAAI,CAACT,EAAE,CAACiC,aAAa,EAAE;YACvB,MAAMhE,IAAI,CAACiE,IAAI,CAAC,kCAAkC,EAAE,EAAE,EAAE,SAAS,CAAC;YAClEC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;UAC1B,CAAC;UAAA,gBANDd,IAAIA,CAAAe,EAAA;YAAA,OAAAd,IAAA,CAAAe,KAAA,OAAAC,SAAA;UAAA;QAAA,GAMH;QACDC,KAAK,EAAGA,KAAU,IAAI;UACpB,IAAI,CAACT,WAAW,EAAE;UAClBlB,OAAO,CAAC2B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrDxE,IAAI,CAACiE,IAAI,CAAC,6BAA6B,EAAE,EAAE,EAAE,OAAO,CAAC;QACvD;OACD,CAAC;IACJ;EACF;;qCA7CWnC,sBAAsB,EAAA5B,EAAA,CAAAuE,iBAAA,CAAAvE,EAAA,CAAAwE,iBAAA,GAAAxE,EAAA,CAAAuE,iBAAA,CAAAE,EAAA,CAAA3C,cAAA;EAAA;;UAAtBF,sBAAsB;IAAA8C,SAAA;IAAAC,MAAA;MAAAvE,IAAA;IAAA;IAAAwE,UAAA;IAAAC,QAAA,GAAA7E,EAAA,CAAA8E,0BAAA,EAAA9E,EAAA,CAAA+E,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCX3BrF,EAJR,CAAAO,cAAA,aAAgC,aACG,aACmB,aACzB,aAGpB;QACCP,EAAA,CAAAuF,UAAA,IAAAC,qCAAA,iBAAiE;QAEjExF,EAAA,CAAAO,cAAA,aAEC;QAiBCP,EAhBA,CAAAuF,UAAA,IAAAE,uCAAA,mBAUC,IAAAC,uCAAA,mBAiBC;QAGR1F,EAFI,CAAAQ,YAAA,EAAM,EACF,EACF;QAQER,EANR,CAAAO,cAAA,aAAyB,eAGtB,eACiC,eACc,gBAEvC;QAAAP,EAAA,CAAAiB,MAAA,IACH;QAAAjB,EAAA,CAAAQ,YAAA,EAAO;QACPR,EAAA,CAAAuF,UAAA,KAAAI,uCAAA,mBAIC;QAGH3F,EAAA,CAAAQ,YAAA,EAAM;QAGJR,EADF,CAAAO,cAAA,eAAqD,aAC4C;QAC7FP,EAAA,CAAAC,SAAA,wBAA0E;QAC1ED,EAAA,CAAAiB,MAAA,IACF;QAAAjB,EAAA,CAAAQ,YAAA,EAAI;QACJR,EAAA,CAAAO,cAAA,aAA+F;QAC7FP,EAAA,CAAAC,SAAA,wBAAwE;QACxED,EAAA,CAAAiB,MAAA,IACF;QAAAjB,EAAA,CAAAQ,YAAA,EAAI;QACJR,EAAA,CAAAO,cAAA,aAA+F;QAC7FP,EAAA,CAAAC,SAAA,wBAAyE;QACzED,EAAA,CAAAiB,MAAA,IACF;QAEJjB,EAFI,CAAAQ,YAAA,EAAI,EACA,EACF;QAENR,EAAA,CAAAuF,UAAA,KAAAK,sCAAA,kBAAwD;QAS1D5F,EAAA,CAAAQ,YAAA,EAAM;QAENR,EAAA,CAAAuF,UAAA,KAAAM,sCAAA,mBAAwE;QAgDhF7F,EAHM,CAAAQ,YAAA,EAAM,EACF,EACF,EACF;;;QAlIiDR,EAAA,CAAAoB,SAAA,GAAgB;QAAhBpB,EAAA,CAAAE,UAAA,SAAAoF,GAAA,CAAAlF,IAAA,CAAAC,KAAA,CAAgB;QAUxDL,EAAA,CAAAoB,SAAA,GAIF;QAJEpB,EAAA,CAAAE,UAAA,SAAAoF,GAAA,CAAAlF,IAAA,CAAA0F,IAAA,iBAAAR,GAAA,CAAAlF,IAAA,CAAA0F,IAAA,iBAAAR,GAAA,CAAAlF,IAAA,CAAA0F,IAAA,iBAIF;QAaE9F,EAAA,CAAAoB,SAAA,EAIF;QAJEpB,EAAA,CAAAE,UAAA,SAAAoF,GAAA,CAAAlF,IAAA,CAAA0F,IAAA,iBAAAR,GAAA,CAAAlF,IAAA,CAAA0F,IAAA,iBAAAR,GAAA,CAAAlF,IAAA,CAAA0F,IAAA,iBAIF;QAaI9F,EAAA,CAAAoB,SAAA,GACH;QADGpB,EAAA,CAAAqB,kBAAA,KAAAiE,GAAA,CAAAhE,eAAA,CAAAgE,GAAA,CAAAlF,IAAA,CAAA2F,QAAA,OACH;QAGG/F,EAAA,CAAAoB,SAAA,EAA4B;QAA5BpB,EAAA,CAAAE,UAAA,SAAAoF,GAAA,CAAAlF,IAAA,CAAA0F,IAAA,cAA4B;QAU7B9F,EAAA,CAAAoB,SAAA,GACF;QADEpB,EAAA,CAAAqB,kBAAA,MAAAiE,GAAA,CAAAlF,IAAA,CAAA4F,KAAA,MACF;QAGEhG,EAAA,CAAAoB,SAAA,GACF;QADEpB,EAAA,CAAAqB,kBAAA,MAAAiE,GAAA,CAAAlF,IAAA,CAAA6F,KAAA,MACF;QAGEjG,EAAA,CAAAoB,SAAA,GACF;QADEpB,EAAA,CAAAqB,kBAAA,MAAAiE,GAAA,CAAAhE,eAAA,CAAAgE,GAAA,CAAAlF,IAAA,CAAA0F,IAAA,OACF;QAIsB9F,EAAA,CAAAoB,SAAA,EAA4B;QAA5BpB,EAAA,CAAAE,UAAA,SAAAoF,GAAA,CAAAlF,IAAA,CAAA0F,IAAA,cAA4B;QAWd9F,EAAA,CAAAoB,SAAA,EAA4B;QAA5BpB,EAAA,CAAAE,UAAA,SAAAoF,GAAA,CAAAlF,IAAA,CAAA0F,IAAA,cAA4B;;;mBD9ElEnG,YAAY,EAAAuG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAExG,YAAY,EAAAyG,EAAA,CAAAC,iBAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}