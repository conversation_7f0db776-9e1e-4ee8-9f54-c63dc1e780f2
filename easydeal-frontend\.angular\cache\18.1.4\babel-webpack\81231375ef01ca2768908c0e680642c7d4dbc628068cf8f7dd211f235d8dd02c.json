{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/layout.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../shared/keenicon/keenicon.component\";\nfunction SidebarLogoComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SidebarLogoComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 4)(2, \"img\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SidebarLogoComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6);\n    i0.ɵɵelement(2, \"app-keenicon\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.toggleButtonClass);\n    i0.ɵɵattribute(\"data-kt-toggle-state\", ctx_r0.toggleState)(\"data-kt-toggle-name\", ctx_r0.toggleAttr);\n  }\n}\nexport class SidebarLogoComponent {\n  layout;\n  unsubscribe = [];\n  toggleButtonClass = '';\n  toggleEnabled;\n  toggleType = '';\n  toggleState = '';\n  currentLayoutType;\n  toggleAttr;\n  constructor(layout) {\n    this.layout = layout;\n  }\n  ngOnInit() {\n    this.toggleAttr = `app-sidebar-${this.toggleType}`;\n    const layoutSubscr = this.layout.currentLayoutTypeSubject.asObservable().subscribe(layout => {\n      this.currentLayoutType = layout;\n    });\n    this.unsubscribe.push(layoutSubscr);\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach(sb => sb.unsubscribe());\n  }\n  static ɵfac = function SidebarLogoComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SidebarLogoComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SidebarLogoComponent,\n    selectors: [[\"app-sidebar-logo\"]],\n    inputs: {\n      toggleButtonClass: \"toggleButtonClass\",\n      toggleEnabled: \"toggleEnabled\",\n      toggleType: \"toggleType\",\n      toggleState: \"toggleState\"\n    },\n    decls: 5,\n    vars: 3,\n    consts: [[\"routerLink\", \"/dashboard\"], [4, \"ngIf\"], [\"alt\", \"Logo\", \"src\", \"./assets/media/easydeallogos/home-logo.png\", 1, \"h-30px\", \"app-sidebar-logo-minimize\"], [\"alt\", \"Logo\", \"src\", \"./assets/media/easydeallogos/loading-logo.png\", 1, \"h-40px\", \"app-sidebar-logo-default\"], [\"alt\", \"Logo\", \"src\", \"./assets/media/logos/default.svg\", 1, \"h-25px\", \"app-sidebar-logo-default\", \"theme-light-show\"], [\"alt\", \"Logo\", \"src\", \"./assets/media/logos/default-dark.svg\", 1, \"h-25px\", \"app-sidebar-logo-default\", \"theme-dark-show\"], [\"id\", \"kt_app_sidebar_toggle\", \"data-kt-toggle\", \"true\", \"data-kt-toggle-target\", \"body\", 1, \"app-sidebar-toggle\", \"btn\", \"btn-icon\", \"btn-shadow\", \"btn-sm\", \"btn-color-muted\", \"btn-active-color-primary\", \"body-bg\", \"h-30px\", \"w-30px\", \"position-absolute\", \"top-50\", \"start-100\", \"translate-middle\", \"rotate\", 3, \"ngClass\"], [\"name\", \"double-left\", 1, \"fs-2\", \"rotate-180\", \"text-success\"]],\n    template: function SidebarLogoComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"a\", 0);\n        i0.ɵɵtemplate(1, SidebarLogoComponent_ng_container_1_Template, 2, 0, \"ng-container\", 1)(2, SidebarLogoComponent_ng_container_2_Template, 3, 0, \"ng-container\", 1);\n        i0.ɵɵelement(3, \"img\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, SidebarLogoComponent_ng_container_4_Template, 3, 3, \"ng-container\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentLayoutType === \"dark-sidebar\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentLayoutType !== \"dark-sidebar\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.toggleEnabled);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i3.RouterLink, i4.KeeniconComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "toggleButtonClass", "SidebarLogoComponent", "layout", "unsubscribe", "toggle<PERSON>nabled", "toggleType", "toggleState", "currentLayoutType", "toggleAttr", "constructor", "ngOnInit", "layoutSubscr", "currentLayoutTypeSubject", "asObservable", "subscribe", "push", "ngOnDestroy", "for<PERSON>ach", "sb", "ɵɵdirectiveInject", "i1", "LayoutService", "selectors", "inputs", "decls", "vars", "consts", "template", "SidebarLogoComponent_Template", "rf", "ctx", "ɵɵtemplate", "SidebarLogoComponent_ng_container_1_Template", "SidebarLogoComponent_ng_container_2_Template", "SidebarLogoComponent_ng_container_4_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\_metronic\\layout\\components\\sidebar\\sidebar-logo\\sidebar-logo.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\_metronic\\layout\\components\\sidebar\\sidebar-logo\\sidebar-logo.component.html"], "sourcesContent": ["import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';\r\nimport { Subscription } from 'rxjs';\r\nimport { LayoutType } from '../../../core/configs/config';\r\nimport { LayoutService } from '../../../core/layout.service';\r\n\r\n@Component({\r\n  selector: 'app-sidebar-logo',\r\n  templateUrl: './sidebar-logo.component.html',\r\n  styleUrls: ['./sidebar-logo.component.scss'],\r\n})\r\nexport class SidebarLogoComponent implements OnInit, OnDestroy {\r\n  private unsubscribe: Subscription[] = [];\r\n  @Input() toggleButtonClass: string = '';\r\n  @Input() toggleEnabled: boolean;\r\n  @Input() toggleType: string = '';\r\n  @Input() toggleState: string = '';\r\n  currentLayoutType: LayoutType | null;\r\n\r\n  toggleAttr: string;\r\n\r\n  constructor(private layout: LayoutService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.toggleAttr = `app-sidebar-${this.toggleType}`;\r\n    const layoutSubscr = this.layout.currentLayoutTypeSubject\r\n      .asObservable()\r\n      .subscribe((layout) => {\r\n        this.currentLayoutType = layout;\r\n      });\r\n    this.unsubscribe.push(layoutSubscr);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.unsubscribe.forEach((sb) => sb.unsubscribe());\r\n  }\r\n}\r\n", "<!--begin::Logo image-->\r\n<a routerLink=\"/dashboard\">\r\n  <ng-container *ngIf=\"currentLayoutType === 'dark-sidebar'\">\r\n    <img alt=\"Logo\" src=\"./assets/media/easydeallogos/loading-logo.png\" class=\"h-40px app-sidebar-logo-default\" />\r\n  </ng-container>\r\n  <ng-container *ngIf=\"currentLayoutType !== 'dark-sidebar'\">\r\n    <img alt=\"Logo\" src=\"./assets/media/logos/default.svg\" class=\"h-25px app-sidebar-logo-default theme-light-show\" />\r\n    <img alt=\"Logo\" src=\"./assets/media/logos/default-dark.svg\"\r\n      class=\"h-25px app-sidebar-logo-default theme-dark-show\" />\r\n  </ng-container>\r\n\r\n  <img alt=\"Logo\" src=\"./assets/media/easydeallogos/home-logo.png\" class=\"h-30px app-sidebar-logo-minimize\" />\r\n</a>\r\n<!--end::Logo image-->\r\n\r\n\r\n<ng-container *ngIf=\"toggleEnabled\">\r\n  <!--begin::Sidebar toggle-->\r\n  <div id=\"kt_app_sidebar_toggle\"\r\n    class=\"app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary body-bg h-30px w-30px position-absolute top-50 start-100 translate-middle rotate\"\r\n    [ngClass]=\"toggleButtonClass\" data-kt-toggle=\"true\" [attr.data-kt-toggle-state]=\"toggleState\"\r\n    data-kt-toggle-target=\"body\" [attr.data-kt-toggle-name]=\"toggleAttr\">\r\n    <app-keenicon name=\"double-left\" class=\"fs-2 rotate-180 text-success\"></app-keenicon>\r\n  </div>\r\n  <!--end::Sidebar toggle-->\r\n</ng-container>\r\n"], "mappings": ";;;;;;;ICEEA,EAAA,CAAAC,uBAAA,GAA2D;IACzDD,EAAA,CAAAE,SAAA,aAA8G;;;;;;IAEhHF,EAAA,CAAAC,uBAAA,GAA2D;IAEzDD,EADA,CAAAE,SAAA,aAAkH,aAEtD;;;;;;IAQhEF,EAAA,CAAAC,uBAAA,GAAoC;IAElCD,EAAA,CAAAG,cAAA,aAGuE;IACrEH,EAAA,CAAAE,SAAA,sBAAqF;IACvFF,EAAA,CAAAI,YAAA,EAAM;;;;;IAHJJ,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAM,UAAA,YAAAC,MAAA,CAAAC,iBAAA,CAA6B;;;;ADVjC,OAAM,MAAOC,oBAAoB;EAUXC,MAAA;EATZC,WAAW,GAAmB,EAAE;EAC/BH,iBAAiB,GAAW,EAAE;EAC9BI,aAAa;EACbC,UAAU,GAAW,EAAE;EACvBC,WAAW,GAAW,EAAE;EACjCC,iBAAiB;EAEjBC,UAAU;EAEVC,YAAoBP,MAAqB;IAArB,KAAAA,MAAM,GAANA,MAAM;EAAkB;EAE5CQ,QAAQA,CAAA;IACN,IAAI,CAACF,UAAU,GAAG,eAAe,IAAI,CAACH,UAAU,EAAE;IAClD,MAAMM,YAAY,GAAG,IAAI,CAACT,MAAM,CAACU,wBAAwB,CACtDC,YAAY,EAAE,CACdC,SAAS,CAAEZ,MAAM,IAAI;MACpB,IAAI,CAACK,iBAAiB,GAAGL,MAAM;IACjC,CAAC,CAAC;IACJ,IAAI,CAACC,WAAW,CAACY,IAAI,CAACJ,YAAY,CAAC;EACrC;EAEAK,WAAWA,CAAA;IACT,IAAI,CAACb,WAAW,CAACc,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACf,WAAW,EAAE,CAAC;EACpD;;qCAxBWF,oBAAoB,EAAAT,EAAA,CAAA2B,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;;UAApBpB,oBAAoB;IAAAqB,SAAA;IAAAC,MAAA;MAAAvB,iBAAA;MAAAI,aAAA;MAAAC,UAAA;MAAAC,WAAA;IAAA;IAAAkB,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTjCrC,EAAA,CAAAG,cAAA,WAA2B;QAIzBH,EAHA,CAAAuC,UAAA,IAAAC,4CAAA,0BAA2D,IAAAC,4CAAA,0BAGA;QAM3DzC,EAAA,CAAAE,SAAA,aAA4G;QAC9GF,EAAA,CAAAI,YAAA,EAAI;QAIJJ,EAAA,CAAAuC,UAAA,IAAAG,4CAAA,0BAAoC;;;QAdnB1C,EAAA,CAAAK,SAAA,EAA0C;QAA1CL,EAAA,CAAAM,UAAA,SAAAgC,GAAA,CAAAvB,iBAAA,oBAA0C;QAG1Cf,EAAA,CAAAK,SAAA,EAA0C;QAA1CL,EAAA,CAAAM,UAAA,SAAAgC,GAAA,CAAAvB,iBAAA,oBAA0C;QAW5Cf,EAAA,CAAAK,SAAA,GAAmB;QAAnBL,EAAA,CAAAM,UAAA,SAAAgC,GAAA,CAAA1B,aAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}