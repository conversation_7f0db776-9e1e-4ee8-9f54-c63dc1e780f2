{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AuthInterceptor {\n  intercept(request, next) {\n    // Get token from localStorage or a service\n    const authToken = localStorage.getItem('authToken');\n    console.log(localStorage.getItem('currentUser'));\n    const publicEndpoints = ['/login', '/register', '/send-otp', '/check-otp', '/reset-password'];\n    const isPublic = publicEndpoints.some(url => request.url.includes(url));\n    // const headersConfig: { [key: string]: string } = {\n    //   Accept: 'application/json',\n    //   'Content-Type': 'application/json',\n    // };\n    if (authToken && !isPublic) {\n      request = request.clone({\n        setHeaders: {\n          Authorization: `Bearer ${authToken}`\n        }\n      });\n    }\n    // request = request.clone({ setHeaders: headersConfig });\n    return next.handle(request);\n  }\n  static ɵfac = function AuthInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthInterceptor)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["AuthInterceptor", "intercept", "request", "next", "authToken", "localStorage", "getItem", "console", "log", "publicEndpoints", "isPublic", "some", "url", "includes", "clone", "setHeaders", "Authorization", "handle", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { <PERSON>ttpRe<PERSON>, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n\r\n  intercept(request: HttpRequest<any>, next: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>): Observable<HttpEvent<any>> {\r\n    // Get token from localStorage or a service\r\n    const authToken = localStorage.getItem('authToken');\r\n    console.log(localStorage.getItem('currentUser'));\r\n    const publicEndpoints = ['/login', '/register', '/send-otp', '/check-otp', '/reset-password'];\r\n    const isPublic = publicEndpoints.some(url => request.url.includes(url));\r\n\r\n    // const headersConfig: { [key: string]: string } = {\r\n    //   Accept: 'application/json',\r\n    //   'Content-Type': 'application/json',\r\n    // };\r\n\r\n    if (authToken && !isPublic) {\r\n      request = request.clone({\r\n        setHeaders: {\r\n          Authorization: `Bearer ${authToken}`\r\n        }\r\n      });\r\n    }\r\n\r\n    // request = request.clone({ setHeaders: headersConfig });\r\n\r\n    return next.handle(request);\r\n  }\r\n}\r\n"], "mappings": ";AAKA,OAAM,MAAOA,eAAe;EAE1BC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD;IACA,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnDC,OAAO,CAACC,GAAG,CAACH,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;IAChD,MAAMG,eAAe,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,CAAC;IAC7F,MAAMC,QAAQ,GAAGD,eAAe,CAACE,IAAI,CAACC,GAAG,IAAIV,OAAO,CAACU,GAAG,CAACC,QAAQ,CAACD,GAAG,CAAC,CAAC;IAEvE;IACA;IACA;IACA;IAEA,IAAIR,SAAS,IAAI,CAACM,QAAQ,EAAE;MAC1BR,OAAO,GAAGA,OAAO,CAACY,KAAK,CAAC;QACtBC,UAAU,EAAE;UACVC,aAAa,EAAE,UAAUZ,SAAS;;OAErC,CAAC;IACJ;IAEA;IAEA,OAAOD,IAAI,CAACc,MAAM,CAACf,OAAO,CAAC;EAC7B;;qCAzBWF,eAAe;EAAA;;WAAfA,eAAe;IAAAkB,OAAA,EAAflB,eAAe,CAAAmB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}