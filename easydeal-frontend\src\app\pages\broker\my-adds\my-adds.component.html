<div class="d-flex align-items-center justify-content-between py-4">
  <h1 class="fw-bold text-dark-blue mb-0">
    My Advertisements
    <span class="fs-6 text-dark-blue fw-semibold ms-0">({{ this.page.totalElements }})</span>
  </h1>
</div>

<div class="tab-pane fade show active" id="kt_tab_pane_1">
  <div class="row g-4">
    <!-- Advertisement card for each post -->
    <div class="col-md-3 col-sm-6 col-12" *ngFor="let post of rows; let i = index">
      <div
        class="card h-100 border-0 shadow-sm overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
        (click)="viewPropertyDetails(post)"
        role="button"
        tabindex="0"
      >
        <!-- Image slider section -->
        <div class="position-relative">
          <div
            class="media-container cursor-pointer"
            (click)="openMediaModal(mediaModal, post, $event)"
            id="mediaContainer_{{ post.id }}"
          >
            <img
              [src]="getImageFromGallery(post)"
              class="img-fluid rounded-top"
              style="height: 180px; object-fit: cover; width: 100%; transition: transform 0.3s ease;"
              alt="{{ post.type }} image"
            />

            <!-- Video play icon overlay -->
            <div
              *ngIf="isCurrentItemVideo(post)"
              class="video-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-black bg-opacity-30"
            >
              <i class="bi bi-play-circle-fill text-white fs-2"></i>
            </div>

            <!-- Gallery icon overlay -->
            <div
              *ngIf="!isCurrentItemVideo(post)"
              class="gallery-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-black bg-opacity-20"
            >
              <i class="bi bi-images text-white fs-3"></i>
            </div>
          </div>

          <!-- Image navigation controls -->
          <div
            *ngIf="hasMultipleImages(post)"
            class="image-navigation position-absolute top-50 start-0 w-100 d-flex justify-content-between px-2"
            (click)="$event.stopPropagation()"
          >
            <button
              class="btn btn-icon btn-sm btn-white shadow-sm rounded-circle bg-light-dark-blue"
              (click)="prevImage(post, $event)"
              aria-label="Previous image"
            >
              <i class="bi bi-chevron-left fs-6"></i>
            </button>
            <button
              class="btn btn-icon btn-sm btn-white shadow-sm rounded-circle bg-light-dark-blue"
              (click)="nextImage(post, $event)"
              aria-label="Next image"
            >
              <i class="bi bi-chevron-right fs-6"></i>
            </button>
          </div>

          <!-- Image counter -->
          <div
            *ngIf="hasMultipleImages(post)"
            class="image-counter position-absolute bottom-0 end-0 bg-dark bg-opacity-75 text-white px-2 py-1 rounded-top-start fs-7"
            (click)="$event.stopPropagation()"
          >
            {{ getCurrentImageIndex(post) + 1 }}/{{ getAllImagesFromGallery(post).length }}
          </div>
        </div>

        <!-- Card content -->
        <div class="card-body p-4">
          <h5 class="fs-5 fw-bold text-dark mb-3 text-truncate cursor-pointer text-hover-dark-blue">
            {{ post.type }}
          </h5>

          <!-- Property details -->
          <div class="d-flex flex-wrap gap-3 mb-4">
            <div class="border border-gray-200 rounded p-3 flex-grow-1">
              <div class="fs-6 fw-semibold text-dark">{{ post.area.name_en }}</div>
              <div class="fs-7 text-muted">Area</div>
            </div>
            <div class="border border-gray-200 rounded p-3 flex-grow-1">
              <div class="fs-6 fw-semibold text-dark">{{ post.city.name_en }}</div>
              <div class="fs-7 text-muted">City</div>
            </div>
          </div>

          <!-- View button -->
          <a
            class="btn btn-sm btn-light-dark-blue fw-semibold w-100"
            (click)="viewPropertyDetails(post)"
            role="button"
            aria-label="View property details"
          >
            <i class="fa fa-eye me-2"></i>View
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="mt-5">
    <app-pagination
      [totalItems]="page.totalElements"
      [itemsPerPage]="page.limit"
      [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>
</div>

<!-- Media Modal -->
<ng-template #mediaModal let-modal >
  <div class="modal-header">
    <h4 class="modal-title">
      {{ selectedMediaType === "video" ? "Video" : "Image" }}
      <span *ngIf="hasMultipleModalMedia()" class="text-muted ms-2 fs-6">
        ({{ currentModalIndex + 1 }}/{{ modalMediaItems.length }})
      </span>
    </h4>
    <button type="button" class="btn-close" (click)="modal.dismiss()"></button>
  </div>
  <div class="modal-body position-relative">
    <div class="text-center">
      <!-- Video player -->
      <div
        *ngIf="selectedMediaType === 'video' && selectedMediaUrl"
        class="video-container"
      >
        <!-- Native video player -->
        <video
          [src]="selectedMediaUrl"
          controls
          autoplay
          class="w-100"
          style="max-height: 70vh"
          controlsList="nodownload"
          preload="auto"
        ></video>

        <!-- Fallback message if video doesn't play -->
        <div class="mt-3 text-muted small">
          <a [href]="selectedMediaUrl" target="_blank">
            <i class="bi bi-box-arrow-up-right me-1"></i>
            Open in new tab
          </a>
        </div>
      </div>

      <!-- Image viewer -->
      <img
        *ngIf="selectedMediaType === 'image' && selectedMediaUrl"
        [src]="selectedMediaUrl"
        class="img-fluid"
        style="max-height: 70vh"
      />
    </div>

    <!-- Navigation buttons for multiple media items -->
    <div *ngIf="hasMultipleModalMedia()" class="modal-navigation">
      <!-- Previous button -->
      <button
        class="btn btn-primary btn-icon position-absolute top-50 start-0 translate-middle-y ms-3"
        (click)="prevModalMedia()"
        style="z-index: 1050"
      >
        <i class="bi bi-chevron-left"></i>
      </button>

      <!-- Next button -->
      <button
        class="btn btn-primary btn-icon position-absolute top-50 end-0 translate-middle-y me-3"
        (click)="nextModalMedia()"
        style="z-index: 1050"
      >
        <i class="bi bi-chevron-right"></i>
      </button>
    </div>
  </div>

  <!-- Modal footer with navigation dots -->
  <div
    *ngIf="hasMultipleModalMedia()"
    class="modal-footer justify-content-center"
  >
    <div class="d-flex gap-2">
      <button
        *ngFor="let item of modalMediaItems; let i = index"
        class="btn btn-sm rounded-circle p-1"
        [class.btn-primary]="i === currentModalIndex"
        [class.btn-light]="i !== currentModalIndex"
        (click)="currentModalIndex = i; updateModalMedia()"
        style="width: 12px; height: 12px"
      ></button>
    </div>
  </div>
</ng-template>
