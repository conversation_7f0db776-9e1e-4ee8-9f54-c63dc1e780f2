{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/taskes/New folder/easydeal-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nlet AddPropertyComponent = class AddPropertyComponent {\n  fb;\n  router;\n  propertyService;\n  cdr;\n  totalSteps = 6;\n  currentStep = 0;\n  selectedCityId;\n  selectedCityName;\n  selectedAreaName;\n  selectedUnitType;\n  cities = [];\n  unitTypes = [];\n  areas = [];\n  isLoadingCities = false;\n  otherAccessoriesList = [{\n    key: 'GARAGE',\n    value: 'garage'\n  }, {\n    key: 'CLUBHOUSE',\n    value: 'clubhouse'\n  }, {\n    key: 'CLUB',\n    value: 'club'\n  }, {\n    key: 'STORAGE',\n    value: 'storage'\n  }, {\n    key: 'ELEVATOR',\n    value: 'elevator'\n  }, {\n    key: 'SWIMMING POOL',\n    value: 'swimming_pool'\n  }, {\n    key: 'ALL THE ABOVE',\n    value: 'all_the_above_are_suitable'\n  }];\n  //get brokerId from session\n  brokerId;\n  finishingTypes = [{\n    key: 'On Brick',\n    value: 'on_brick'\n  }, {\n    key: 'Semi Finished',\n    value: 'semi_finished'\n  }, {\n    key: 'Company Finished',\n    value: 'company_finished'\n  }, {\n    key: 'Super Lux',\n    value: 'super_lux'\n  }, {\n    key: 'Ultra Super Lux',\n    value: 'ultra_super_lux'\n  }];\n  floorTypes = [{\n    key: 'Ground',\n    value: 'ground'\n  }, {\n    key: 'Last Floor',\n    value: 'last_floor'\n  }, {\n    key: 'Repeated',\n    value: 'repeated'\n  }, {\n    key: 'All Of The Above',\n    value: 'all_the_above_are_suitable'\n  }];\n  viewTypes = [{\n    key: 'Water View',\n    value: 'water_view'\n  }, {\n    key: 'Gardens And Landscape',\n    value: 'gardens_and_landscape'\n  }, {\n    key: 'Street',\n    value: 'street'\n  }, {\n    key: 'Entertainment Area',\n    value: 'entertainment_area'\n  }, {\n    key: 'Garden  ',\n    value: 'garden'\n  }, {\n    key: 'Main Street',\n    value: ' main_street'\n  }, {\n    key: 'Square',\n    value: 'square'\n  }, {\n    key: 'Side Street',\n    value: 'side_street'\n  }, {\n    key: 'Rear View',\n    value: 'rear_view'\n  }];\n  deliveryTypes = [{\n    key: 'Immediate Delivery',\n    value: 'immediate_delivery'\n  }, {\n    key: 'Under Construction',\n    value: 'under_construction'\n  }];\n  activityTypes = [{\n    key: 'Administrative Only',\n    value: 'administrative_only'\n  }, {\n    key: 'Commercial Only',\n    value: 'commercial_only'\n  }, {\n    key: 'Medical Only',\n    value: 'medical_only'\n  }, {\n    key: 'Administrative And Commercial',\n    value: 'administrative_and_commercial'\n  }, {\n    key: 'Administrative Commercial And Medical',\n    value: 'administrative_commercial_and_medical'\n  }];\n  fitOutConditionTypes = [{\n    key: 'Unfitted',\n    value: 'unfitted'\n  }, {\n    key: 'Fully Fitted',\n    value: 'fully_fitted'\n  }, {\n    key: 'All The Above Are Suitable',\n    value: 'all_the_above_are_suitable'\n  }];\n  furnishingStatusTypes = [{\n    key: 'Unfurnished',\n    value: 'unfurnished'\n  }, {\n    key: 'Furnished With Air Conditioners',\n    value: 'furnished_with_air_conditioners'\n  }, {\n    key: 'Furnished Without Air Conditioners',\n    value: 'furnished_without_air_conditioners'\n  }];\n  groundLayoutStatusTypes = [{\n    key: 'Vacant Land',\n    value: 'vacant_land'\n  }, {\n    key: 'Under Construction',\n    value: 'under_construction'\n  }, {\n    key: 'Fully Built',\n    value: 'fully_built'\n  }, {\n    key: 'All Acceptable',\n    value: 'all_acceptable'\n  }];\n  unitDesignTypes = [{\n    key: 'Custom Design',\n    value: 'custom_design'\n  }, {\n    key: 'One Apartment Per Floor',\n    value: 'one_apartment_per_floor'\n  }, {\n    key: 'Two Apartments Per Floor',\n    value: 'two_apartments_per_floor'\n  }, {\n    key: 'More Than Two Apartments Per Floor',\n    value: 'more_than_two_apartments_per_floor'\n  }, {\n    key: 'All Acceptable',\n    value: 'all_acceptable'\n  }];\n  otherAccessoriesTypes = [{\n    key: 'Garage',\n    value: 'garage'\n  }, {\n    key: 'Clubhouse',\n    value: 'clubhouse'\n  }, {\n    key: 'Club',\n    value: 'club'\n  }, {\n    key: 'Storage',\n    value: 'storage'\n  }, {\n    key: 'Elevator',\n    value: 'elevator'\n  }, {\n    key: 'Swimming Pool',\n    value: 'swimming_pool'\n  }];\n  selectedAccessories = [];\n  paymentTypes = [{\n    key: 'Cash',\n    value: 'cash'\n  }, {\n    key: 'Installment',\n    value: 'installment'\n  }, {\n    key: 'All Of The Above Are Suitable ',\n    value: 'all_of_the_above_are_suitable'\n  }];\n  legalTypes = [{\n    key: 'Licensed',\n    value: 'licensed'\n  }, {\n    key: 'Reconciled',\n    value: 'reconciled'\n  }, {\n    key: 'Reconciliation Required',\n    value: 'reconciliation_required'\n  }];\n  // Step 0 options\n  compoundOptions = [{\n    key: 'Outside Compound',\n    value: 'outside_compound'\n  }, {\n    key: 'Inside Compound',\n    value: 'inside_compound'\n  }];\n  // All unit types for filtering\n  allUnitTypes = [];\n  // Unit types for outside compound\n  outsideCompoundUnitTypes = [\n  // Residential\n  {\n    key: 'Apartments',\n    value: 'apartments'\n  }, {\n    key: 'Duplexes',\n    value: 'duplexes'\n  }, {\n    key: 'Studios',\n    value: 'studios'\n  }, {\n    key: 'Penthouses',\n    value: 'penthouses'\n  }, {\n    key: 'Basement',\n    value: 'basement'\n  }, {\n    key: 'Roofs',\n    value: 'roofs'\n  }, {\n    key: 'Standalone Villas',\n    value: 'standalone_villas'\n  }, {\n    key: 'Residential Buildings',\n    value: 'residential_buildings'\n  },\n  // Commercial/Administrative\n  {\n    key: 'Commercial Administrative Buildings',\n    value: 'commercial_administrative_buildings'\n  }, {\n    key: 'Administrative Units',\n    value: 'administrative_units'\n  }, {\n    key: 'Medical Clinics',\n    value: 'medical_clinics'\n  }, {\n    key: 'Pharmacies',\n    value: 'pharmacies'\n  }, {\n    key: 'Commercial Stores',\n    value: 'commercial_stores'\n  },\n  // Industrial\n  {\n    key: 'Warehouses',\n    value: 'warehouses'\n  }, {\n    key: 'Factories',\n    value: 'factories'\n  },\n  // Lands\n  {\n    key: 'Residential Villa Lands',\n    value: 'residential_villa_lands'\n  }, {\n    key: 'Residential Buildings Lands',\n    value: 'residential_buildings_lands'\n  }, {\n    key: 'Administrative Lands',\n    value: 'administrative_lands'\n  }, {\n    key: 'Commercial Lands',\n    value: 'commercial_lands'\n  }, {\n    key: 'Medical Lands',\n    value: 'medical_lands'\n  }, {\n    key: 'Mixed Lands',\n    value: 'mixed_lands'\n  }, {\n    key: 'Warehouses Land',\n    value: 'warehouses_land'\n  }, {\n    key: 'Factory Lands',\n    value: 'factory_lands'\n  }];\n  // Unit types for inside compound (will be loaded from API)\n  insideCompoundUnitTypes = [];\n  // Filtered unit types based on compound selection\n  filteredUnitTypes = [];\n  step0Form;\n  step1Form;\n  step2Form;\n  step3Form;\n  step4Form;\n  step5Form;\n  constructor(fb, router, propertyService, cdr) {\n    this.fb = fb;\n    this.router = router;\n    this.propertyService = propertyService;\n    this.cdr = cdr;\n  }\n  ngOnInit() {\n    this.brokerId = 5;\n    this.initForms();\n    this.loadUnitTypes();\n    this.loadCities();\n    this.loadAreas();\n    // Initialize filtered unit types as empty\n    this.filteredUnitTypes = [];\n  }\n  initForms() {\n    // Step 0: Property Category Selection\n    this.step0Form = this.fb.group({\n      compoundType: ['', [Validators.required]],\n      type: ['', [Validators.required]] // Unit Type moved to step 0\n    });\n    // Step 1: Basic Property Settings\n    this.step1Form = this.fb.group({\n      cityId: ['', [Validators.required]],\n      areaId: ['', [Validators.required]],\n      detailedAddress: ['', [Validators.required, Validators.maxLength(255)]],\n      location: ['', [Validators.required, Validators.pattern('https?://.+')]],\n      ownerName: ['', Validators.required],\n      ownerPhone: ['', [Validators.required, Validators.pattern('^01[0-2,5]{1}[0-9]{8}$')]]\n    });\n    // Step 2: Unit Information\n    this.step2Form = this.fb.group({\n      buildingNumber: ['', [Validators.maxLength(50)]],\n      unitNumber: ['', [Validators.maxLength(50)]],\n      floor: ['', [Validators.required]],\n      unitArea: ['', [Validators.required, Validators.min(1), Validators.pattern('^[0-9]*$')]],\n      buildingArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],\n      groundArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],\n      numberOfRooms: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]*$')]],\n      numberOfBathrooms: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]*$')]],\n      numberOfFloors: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]*$')]],\n      unitFacing: [''],\n      view: ['', [Validators.required]],\n      finishingType: ['', [Validators.required]],\n      fitOutCondition: [''],\n      furnishingStatus: [''],\n      groundLayoutStatus: [''],\n      unitDesign: [''],\n      activity: [''],\n      deliveryStatus: ['', [Validators.required]],\n      deliveryDate: [''],\n      otherAccessories: [''],\n      // Optional field - no validators to avoid blocking navigation\n      legalStatus: [''] // Moved from step5Form\n    });\n    // Step 3: Financial Information\n    this.step3Form = this.fb.group({\n      paymentSystem: ['', Validators.required],\n      pricePerMeterInInstallment: ['', [Validators.min(0)]],\n      totalPriceInInstallment: ['', [Validators.min(0)]],\n      pricePerMeterInCash: ['', [Validators.min(0)]],\n      totalPriceInCash: ['', [Validators.min(0)]]\n    });\n    // Step 4: Project Documents\n    this.step4Form = this.fb.group({\n      diagram: [[]],\n      layout: [[]],\n      videos: [[]],\n      locationInMasterPlan: [[]]\n    });\n    // Step 5: Owner Information\n    this.step5Form = this.fb.group({\n      // legalStatus moved to step2Form\n    });\n  }\n  // Get current form based on step\n  getCurrentForm() {\n    switch (this.currentStep) {\n      case 0:\n        return this.step0Form;\n      case 1:\n        return this.step1Form;\n      case 2:\n        return this.step2Form;\n      case 3:\n        return this.step3Form;\n      case 4:\n        return this.step4Form;\n      case 5:\n        return this.step5Form;\n      default:\n        return this.step0Form;\n    }\n  }\n  loadUnitTypes() {\n    this.propertyService.getUnitTypes().subscribe({\n      next: response => {\n        this.allUnitTypes = Object.entries(response.data).map(([key, value]) => ({\n          key,\n          value: value\n        }));\n        this.insideCompoundUnitTypes = this.allUnitTypes; // API data for inside compound\n        console.log('Raw API Response:', this.allUnitTypes);\n      },\n      error: err => {\n        console.error('Error loading unitTypes:', err);\n      },\n      complete: () => {\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  // Filter unit types based on compound selection\n  filterUnitTypes() {\n    const compoundType = this.step0Form.get('compoundType')?.value;\n    if (compoundType === 'outside_compound') {\n      this.filteredUnitTypes = this.outsideCompoundUnitTypes;\n    } else if (compoundType === 'inside_compound') {\n      this.filteredUnitTypes = this.insideCompoundUnitTypes;\n    } else {\n      this.filteredUnitTypes = [];\n    }\n    // Clear unit type selection when compound type changes\n    this.step0Form.patchValue({\n      type: ''\n    });\n    this.selectedUnitType = '';\n    // Trigger change detection to update button state\n    this.cdr.detectChanges();\n  }\n  loadCities() {\n    this.isLoadingCities = true;\n    this.propertyService.getCities().subscribe({\n      next: response => {\n        if (response && response.data) {\n          this.cities = response.data;\n        } else {\n          console.warn('No cities data in response');\n          this.cities = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading cities:', err);\n      },\n      complete: () => {\n        this.isLoadingCities = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  loadAreas(cityId) {\n    this.propertyService.getAreas(cityId).subscribe({\n      next: response => {\n        if (response && response.data) {\n          this.areas = response.data;\n        } else {\n          console.warn('No areas data in response');\n          this.areas = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading areas:', err);\n        this.areas = [];\n      },\n      complete: () => {\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  //**************************************************************** */\n  // STEP 2\n  getFieldsToShow() {\n    const compoundType = this.step0Form.get('compoundType')?.value;\n    const type = this.step0Form.get('type')?.value;\n    // For outside compound apartments\n    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {\n      return ['buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryStatus', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    }\n    return [];\n  }\n  // Check if a specific field should be shown\n  shouldShowField(fieldName) {\n    return this.getFieldsToShow().includes(fieldName);\n  }\n  // /*/***************************** */\n  selectCity(cityId, cityName) {\n    this.selectedCityId = cityId;\n    this.selectedCityName = cityName;\n    this.step1Form.patchValue({\n      cityId: cityId\n    });\n    this.loadAreas(cityId);\n  }\n  selectUnitType(UnitValue) {\n    this.selectedUnitType = UnitValue;\n    this.step0Form.patchValue({\n      type: UnitValue\n    });\n    // Clear unitFacing field if unit type doesn't require it\n    const unitTypesWithFacing = ['apartments', 'duplexes', 'studios', 'penthouses'];\n    if (!unitTypesWithFacing.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        unitFacing: null\n      });\n    }\n    // Clear buildingArea and groundArea fields if unit type doesn't require them\n    const unitTypesWithAreaFields = ['standalone_villas', 'factory_lands', 'commercial_administrative_buildings', 'residential_buildings', 'warehouses'];\n    if (!unitTypesWithAreaFields.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        buildingArea: null,\n        groundArea: null\n      });\n    }\n    // Clear activity field if unit type doesn't require it\n    const unitTypesWithActivity = ['commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings'];\n    if (!unitTypesWithActivity.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        activity: null\n      });\n    }\n    // Clear groundLayoutStatus field if unit type doesn't require it\n    const unitTypesWithGroundLayout = ['factory_lands', 'warehouses', 'residential_buildings', 'commercial_administrative_buildings'];\n    if (!unitTypesWithGroundLayout.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        groundLayoutStatus: null\n      });\n    }\n    // Clear unitDesign field if unit type doesn't require it\n    const unitTypesWithUnitDesign = ['standalone_villas'];\n    if (!unitTypesWithUnitDesign.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        unitDesign: null\n      });\n    }\n    // Clear fitOutCondition field if unit type doesn't require it\n    const unitTypesWithFitOutCondition = ['pharmacies', 'factory_lands', 'warehouses', 'commercial_stores', 'commercial_administrative_buildings'];\n    if (!unitTypesWithFitOutCondition.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        fitOutCondition: null\n      });\n    }\n    // Clear furnishingStatus field if unit type doesn't require it\n    const unitTypesToHideFurnishing = ['pharmacies', 'commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings', 'administrative_units'];\n    if (unitTypesToHideFurnishing.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        furnishingStatus: null\n      });\n    }\n    // Clear legalStatus field if unit type doesn't require it\n    const unitTypesWithLegalStatus = ['duplexes', 'penthouses', 'basement', 'roofs'];\n    if (!unitTypesWithLegalStatus.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        legalStatus: null\n      });\n    }\n  }\n  selectArea(areaId, areaName) {\n    this.selectedAreaName = areaName;\n    this.step1Form.patchValue({\n      areaId: areaId\n    });\n  }\n  // dropdown values for step 2\n  selectStep2Value(fieldName, value) {\n    this.step2Form.patchValue({\n      [fieldName]: value\n    });\n  }\n  //dropdown values for step 3\n  selectStep3Value(fieldName, value) {\n    this.step3Form.patchValue({\n      [fieldName]: value\n    });\n    // Clear price fields when payment system changes\n    if (fieldName === 'paymentSystem') {\n      this.clearPriceFields();\n    }\n  }\n  // Clear all price fields when payment system changes\n  clearPriceFields() {\n    this.step3Form.patchValue({\n      pricePerMeterInCash: null,\n      totalPriceInCash: null,\n      pricePerMeterInInstallment: null,\n      totalPriceInInstallment: null\n    });\n  }\n  // Check if cash price fields should be displayed\n  shouldShowCashFields() {\n    const paymentSystem = this.step3Form.get('paymentSystem')?.value;\n    return paymentSystem === 'cash' || paymentSystem === 'all_of_the_above_are_suitable';\n  }\n  // Check if installment price fields should be displayed\n  shouldShowInstallmentFields() {\n    const paymentSystem = this.step3Form.get('paymentSystem')?.value;\n    return paymentSystem === 'installment' || paymentSystem === 'all_of_the_above_are_suitable';\n  }\n  // Check if unitFacing field should be displayed\n  shouldShowUnitFacingField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithFacing = ['apartments', 'duplexes', 'studios', 'penthouses'];\n    return unitTypesWithFacing.includes(unitType);\n  }\n  // Check if buildingArea and groundArea fields should be displayed\n  shouldShowAreaFields() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithAreaFields = ['standalone_villas', 'factory_lands', 'commercial_administrative_buildings', 'residential_buildings', 'warehouses'];\n    return unitTypesWithAreaFields.includes(unitType);\n  }\n  // Check if groundLayoutStatus field should be displayed\n  shouldShowGroundLayoutStatusField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithGroundLayout = ['factory_lands', 'warehouses', 'residential_buildings', 'commercial_administrative_buildings'];\n    return unitTypesWithGroundLayout.includes(unitType);\n  }\n  // Check if activity field should be displayed\n  shouldShowActivityField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithActivity = ['commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings'];\n    return unitTypesWithActivity.includes(unitType);\n  }\n  // Check if unitDesign field should be displayed\n  shouldShowUnitDesignField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithUnitDesign = ['standalone_villas'];\n    return unitTypesWithUnitDesign.includes(unitType);\n  }\n  // Check if legalStatus field should be displayed\n  shouldShowLegalStatusField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithLegalStatus = ['duplexes', 'penthouses', 'basement', 'roofs'];\n    return unitTypesWithLegalStatus.includes(unitType);\n  }\n  // Check if fitOutCondition field should be displayed\n  shouldShowFitOutConditionField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithFitOutCondition = ['pharmacies', 'factory_lands', 'warehouses', 'commercial_stores', 'commercial_administrative_buildings'];\n    return unitTypesWithFitOutCondition.includes(unitType);\n  }\n  // Check if furnishingStatus field should be displayed\n  shouldShowFurnishingStatusField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesToHideFurnishing = ['pharmacies', 'commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings', 'administrative_units'];\n    return !unitTypesToHideFurnishing.includes(unitType);\n  }\n  // dropdown values for step 0\n  selectStep0Value(fieldName, value) {\n    console.log('selectStep0Value called:', {\n      fieldName,\n      value\n    });\n    this.step0Form.patchValue({\n      [fieldName]: value\n    });\n    // Filter unit types when compound type changes\n    if (fieldName === 'compoundType') {\n      this.filterUnitTypes();\n    }\n    // Handle unit type selection\n    if (fieldName === 'type') {\n      this.selectedUnitType = value;\n      this.selectUnitType(value); // Call existing logic for unit type selection\n    }\n    console.log('Step0 form after update:', this.step0Form.value);\n    // Trigger change detection to update button state\n    this.cdr.detectChanges();\n  }\n  //dropdown values for step 5\n  selectStep5Value(fieldName, value) {\n    this.step5Form.patchValue({\n      [fieldName]: value\n    });\n  }\n  submitForm(checkAd) {\n    var _this = this;\n    if (this.isCurrentFormValid()) {\n      const formData = {\n        ...this.step0Form.value,\n        ...this.step1Form.value,\n        ...this.step2Form.value,\n        ...this.step3Form.value,\n        ...this.step5Form.value\n      };\n      // console.log(formData);\n      const httpFormData = new FormData();\n      // Add step0 form data\n      Object.keys(this.step0Form.value).forEach(key => {\n        httpFormData.append(key, this.step0Form.value[key]);\n      });\n      // Add step1 form data\n      Object.keys(this.step1Form.value).forEach(key => {\n        httpFormData.append(key, this.step1Form.value[key]);\n      });\n      // Fields to be included in additionalDetails array\n      const additionalDetailsFields = ['numberOfFloors'];\n      // Get unit type for conditional field inclusion\n      const unitType = this.step0Form.get('type')?.value;\n      // Add buildingArea and groundArea to additionalDetails only for specific unit types\n      const unitTypesWithAreaFields = ['standalone_villas', 'factory_lands', 'commercial_administrative_buildings', 'residential_buildings', 'warehouses'];\n      if (unitTypesWithAreaFields.includes(unitType)) {\n        additionalDetailsFields.push('groundArea');\n        additionalDetailsFields.push('buildingArea');\n      }\n      // Add activity to additionalDetails only for specific unit types\n      const unitTypesWithActivity = ['commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings'];\n      if (unitTypesWithActivity.includes(unitType)) {\n        additionalDetailsFields.push('activity');\n      }\n      // Add groundLayoutStatus to additionalDetails only for specific unit types\n      const unitTypesWithGroundLayout = ['factory_lands', 'warehouses', 'residential_buildings', 'commercial_administrative_buildings'];\n      if (unitTypesWithGroundLayout.includes(unitType)) {\n        additionalDetailsFields.push('groundLayoutStatus');\n      }\n      // Add unitDesign to additionalDetails only for specific unit types\n      const unitTypesWithUnitDesign = ['standalone_villas'];\n      if (unitTypesWithUnitDesign.includes(unitType)) {\n        additionalDetailsFields.push('unitDesign');\n      }\n      // Add fitOutCondition to additionalDetails only for specific unit types\n      const unitTypesWithFitOutCondition = ['pharmacies', 'factory_lands', 'warehouses', 'commercial_stores', 'commercial_administrative_buildings'];\n      if (unitTypesWithFitOutCondition.includes(unitType)) {\n        additionalDetailsFields.push('fitOutCondition');\n      }\n      // Add furnishingStatus to additionalDetails only for specific unit types\n      const unitTypesToHideFurnishing = ['pharmacies', 'commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings', 'administrative_units'];\n      if (!unitTypesToHideFurnishing.includes(unitType)) {\n        additionalDetailsFields.push('furnishingStatus');\n      }\n      // Add unitFacing to additionalDetails only for specific unit types\n      const unitTypesWithFacing = ['apartments', 'duplexes', 'studios', 'penthouses'];\n      if (unitTypesWithFacing.includes(unitType)) {\n        additionalDetailsFields.push('unitFacing');\n      }\n      // Add step2 form data (excluding fields that go to additionalDetails and otherAccessories)\n      Object.keys(this.step2Form.value).forEach(key => {\n        if (key !== 'otherAccessories' && !additionalDetailsFields.includes(key)) {\n          httpFormData.append(key, this.step2Form.value[key]);\n        }\n      });\n      // Add step3 form data (conditionally based on payment system)\n      const paymentSystem = this.step3Form.get('paymentSystem')?.value;\n      // Always add payment system\n      httpFormData.append('paymentSystem', paymentSystem);\n      // Conditionally add price fields based on payment system\n      if (paymentSystem === 'cash') {\n        // Only send cash price fields\n        const pricePerMeterInCash = this.step3Form.get('pricePerMeterInCash')?.value;\n        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;\n        if (pricePerMeterInCash) {\n          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);\n        }\n        if (totalPriceInCash) {\n          httpFormData.append('totalPriceInCash', totalPriceInCash);\n        }\n      } else if (paymentSystem === 'installment') {\n        // Only send installment price fields\n        const pricePerMeterInInstallment = this.step3Form.get('pricePerMeterInInstallment')?.value;\n        const totalPriceInInstallment = this.step3Form.get('totalPriceInInstallment')?.value;\n        if (pricePerMeterInInstallment) {\n          httpFormData.append('pricePerMeterInInstallment', pricePerMeterInInstallment);\n        }\n        if (totalPriceInInstallment) {\n          httpFormData.append('totalPriceInInstallment', totalPriceInInstallment);\n        }\n      } else if (paymentSystem === 'all_of_the_above_are_suitable') {\n        // Send all price fields\n        const pricePerMeterInCash = this.step3Form.get('pricePerMeterInCash')?.value;\n        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;\n        const pricePerMeterInInstallment = this.step3Form.get('pricePerMeterInInstallment')?.value;\n        const totalPriceInInstallment = this.step3Form.get('totalPriceInInstallment')?.value;\n        if (pricePerMeterInCash) {\n          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);\n        }\n        if (totalPriceInCash) {\n          httpFormData.append('totalPriceInCash', totalPriceInCash);\n        }\n        if (pricePerMeterInInstallment) {\n          httpFormData.append('pricePerMeterInInstallment', pricePerMeterInInstallment);\n        }\n        if (totalPriceInInstallment) {\n          httpFormData.append('totalPriceInInstallment', totalPriceInInstallment);\n        }\n      }\n      // Add step5 form data (excluding legalStatus which goes to additionalDetails)\n      Object.keys(this.step5Form.value).forEach(key => {\n        if (key !== 'legalStatus') {\n          httpFormData.append(key, this.step5Form.value[key]);\n        }\n      });\n      // Create additionalDetails object\n      const additionalDetails = {};\n      // Add fields from step2Form\n      additionalDetailsFields.forEach(field => {\n        const value = this.step2Form.get(field)?.value;\n        if (value) {\n          additionalDetails[field] = value;\n        }\n      });\n      // Add legalStatus from step5Form only for specific unit types\n      const unitTypesWithLegalStatus = ['duplexes', 'penthouses', 'basement', 'roofs'];\n      if (unitTypesWithLegalStatus.includes(unitType)) {\n        const legalStatus = this.step5Form.get('legalStatus')?.value;\n        if (legalStatus) {\n          additionalDetails['legalStatus'] = legalStatus;\n        }\n      }\n      // Send additionalDetails as individual form fields (not JSON)\n      Object.keys(additionalDetails).forEach(key => {\n        httpFormData.append(`additionalDetails[${key}]`, additionalDetails[key]);\n      });\n      //add files\n      const fileFields = ['diagram', 'layout', 'videos', 'locationInMasterPlan'];\n      fileFields.forEach(field => {\n        const files = this.step4Form.get(field)?.value;\n        if (files && files.length) {\n          const isMultiple = ['layout', 'videos'].includes(field);\n          if (isMultiple) {\n            files.forEach(file => {\n              httpFormData.append(`${field}[]`, file);\n            });\n          } else {\n            httpFormData.append(field, files[0]);\n          }\n        }\n      });\n      // Handle otherAccessories as array\n      const accessoriesRaw = this.step2Form.get('otherAccessories')?.value;\n      const accessoriesArray = Array.isArray(accessoriesRaw) ? accessoriesRaw : [];\n      // Send otherAccessories as individual array elements\n      accessoriesArray.forEach((accessory, index) => {\n        httpFormData.append(`otherAccessories[${index}]`, accessory);\n      });\n      httpFormData.append('brokerId', this.brokerId.toString());\n      // Set as advertisement\n      if (checkAd) {\n        httpFormData.append('isAdvertisement', '1');\n      }\n      // Show loading state\n      const button = document.querySelector('.btn-primary');\n      if (button) {\n        button.classList.add('btn-loading');\n      }\n      this.propertyService.createProperty(httpFormData).subscribe({\n        next: function () {\n          var _ref = _asyncToGenerator(function* (response) {\n            console.log('Property data submitted:', response);\n            yield Swal.fire('Property data submitted:', '', response.status);\n            _this.router.navigate(['/broker/dataandproperties'], {\n              queryParams: {\n                success: 'add'\n              }\n            });\n          });\n          return function next(_x) {\n            return _ref.apply(this, arguments);\n          };\n        }(),\n        error: err => {\n          console.error('Error loading unitTypes:', err);\n          Swal.fire(err.message, '', err.status);\n          // Remove loading state\n          if (button) {\n            button.classList.remove('btn-loading');\n          }\n        },\n        complete: () => {\n          this.cdr.detectChanges();\n          // Remove loading state\n          if (button) {\n            button.classList.remove('btn-loading');\n          }\n        }\n      });\n    }\n  }\n  cancel() {\n    this.router.navigate(['/broker/dataandproperties']);\n  }\n  onFileChange(event, fieldName) {\n    if (event.target.files && event.target.files.length) {\n      const files = Array.from(event.target.files);\n      this.step4Form.patchValue({\n        [fieldName]: files\n      });\n      console.log(`${fieldName}: ${files.length} files selected`);\n    }\n  }\n  getFileCount(fieldName) {\n    const files = this.step4Form.get(fieldName)?.value;\n    return files && Array.isArray(files) ? files.length : 0;\n  }\n  // Check if current form is valid\n  isCurrentFormValid() {\n    const currentForm = this.getCurrentForm();\n    // For step 0, only check if unit type is selected\n    if (this.currentStep === 0) {\n      const compoundType = this.step0Form.get('compoundType')?.value;\n      const unitType = this.step0Form.get('type')?.value;\n      const isValid = !!(compoundType && unitType);\n      console.log('Step 0 validation:', {\n        compoundType,\n        unitType,\n        isValid\n      });\n      return isValid;\n    }\n    // For step 2, check only visible/required fields\n    if (this.currentStep === 2) {\n      return this.isStep2FormValid();\n    }\n    return currentForm.valid;\n  }\n  // Custom validation for Step 2 - only check visible fields\n  isStep2FormValid() {\n    const form = this.step2Form;\n    const fieldsToShow = this.getFieldsToShow();\n    // Required fields that must always be valid if they're shown\n    const requiredFields = ['unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus'];\n    // Note: otherAccessories, deliveryDate, buildingNumber, unitNumber are optional and don't block navigation\n    for (const fieldName of requiredFields) {\n      if (fieldsToShow.includes(fieldName)) {\n        const control = form.get(fieldName);\n        if (!control || control.invalid) {\n          console.log(`Step 2 validation failed for field: ${fieldName}`, control?.errors);\n          return false;\n        }\n      }\n    }\n    // Check conditional fields only if they're shown and required for the specific unit type\n    const conditionalFields = ['unitFacing', 'legalStatus', 'fitOutCondition', 'furnishingStatus', 'groundLayoutStatus', 'unitDesign', 'activity'];\n    for (const fieldName of conditionalFields) {\n      if (fieldsToShow.includes(fieldName) && this.isFieldRequiredForUnitType(fieldName)) {\n        const control = form.get(fieldName);\n        if (!control || control.invalid) {\n          console.log(`Step 2 validation failed for conditional field: ${fieldName}`, control?.errors);\n          return false;\n        }\n      }\n    }\n    console.log('Step 2 validation passed');\n    return true;\n  }\n  // Check if a field is required for the current unit type\n  isFieldRequiredForUnitType(fieldName) {\n    switch (fieldName) {\n      case 'unitFacing':\n        return this.shouldShowUnitFacingField();\n      case 'legalStatus':\n        return this.shouldShowLegalStatusField();\n      case 'fitOutCondition':\n        return this.shouldShowFitOutConditionField();\n      case 'furnishingStatus':\n        return this.shouldShowFurnishingStatusField();\n      case 'groundLayoutStatus':\n        return this.shouldShowGroundLayoutStatusField();\n      case 'unitDesign':\n        return this.shouldShowUnitDesignField();\n      case 'activity':\n        return this.shouldShowActivityField();\n      default:\n        return false;\n    }\n  }\n  // Navigate to next step\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n  // Navigate to previous step\n  prevStep() {\n    if (this.currentStep > 0) {\n      this.currentStep--;\n    }\n  }\n  formatUnitTypeKey(key) {\n    if (!key || typeof key !== 'string') return '';\n    return key.split('_').map(word => word.trim() ? word[0].toUpperCase() + word.slice(1).toLowerCase() : '').join(' ');\n  }\n  toggleAccessory(value) {\n    const index = this.selectedAccessories.indexOf(value);\n    if (index > -1) {\n      this.selectedAccessories.splice(index, 1);\n    } else {\n      this.selectedAccessories.push(value);\n    }\n    // Update form control\n    this.step2Form.patchValue({\n      otherAccessories: [...this.selectedAccessories]\n    });\n  }\n  // Handle \"All The Above Are Suitable\" checkbox\n  onAllAccessoriesChange(event) {\n    if (event.target.checked) {\n      // Select all accessories\n      this.selectedAccessories = this.otherAccessoriesTypes.map(a => a.value);\n    } else {\n      // Unselect all accessories\n      this.selectedAccessories = [];\n    }\n    this.step2Form.patchValue({\n      otherAccessories: [...this.selectedAccessories]\n    });\n  }\n  isAccessorySelected(value) {\n    return this.selectedAccessories.includes(value);\n  }\n  getSelectedAccessoriesText() {\n    if (this.selectedAccessories.length === 0) {\n      return '';\n    }\n    if (this.selectedAccessories.length === 1) {\n      const accessory = this.otherAccessoriesTypes.find(a => a.value === this.selectedAccessories[0]);\n      return accessory ? accessory.key : '';\n    }\n    return `${this.selectedAccessories.length} accessories selected`;\n  }\n  // Get compound type text for display\n  getCompoundTypeText(value) {\n    if (!value) return '';\n    const option = this.compoundOptions.find(opt => opt.value === value);\n    return option ? option.key : '';\n  }\n  // Get unit type text for display\n  getUnitTypeText(value) {\n    if (!value) return '';\n    const unitType = this.filteredUnitTypes.find(unit => unit.value === value);\n    return unitType ? unitType.key : '';\n  }\n};\nAddPropertyComponent = __decorate([Component({\n  selector: 'app-add-property',\n  templateUrl: './add-property.component.html',\n  styleUrl: './add-property.component.scss'\n})], AddPropertyComponent);\nexport { AddPropertyComponent };", "map": {"version": 3, "names": ["Component", "Validators", "<PERSON><PERSON>", "AddPropertyComponent", "fb", "router", "propertyService", "cdr", "totalSteps", "currentStep", "selectedCityId", "selectedCityName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedUnitType", "cities", "unitTypes", "areas", "isLoadingCities", "otherAccessoriesList", "key", "value", "brokerId", "finishingTypes", "floorTypes", "viewTypes", "deliveryTypes", "activityTypes", "fitOutConditionTypes", "furnishingStatusTypes", "groundLayoutStatusTypes", "unitDesignTypes", "otherAccessoriesTypes", "selectedAccessories", "paymentTypes", "legalTypes", "compoundOptions", "allUnitTypes", "outsideCompoundUnitTypes", "insideCompoundUnitTypes", "filteredUnitTypes", "step0Form", "step1Form", "step2Form", "step3Form", "step4Form", "step5Form", "constructor", "ngOnInit", "initForms", "loadUnitTypes", "loadCities", "loadAreas", "group", "compoundType", "required", "type", "cityId", "areaId", "detailed<PERSON>ddress", "max<PERSON><PERSON><PERSON>", "location", "pattern", "ownerName", "ownerPhone", "buildingNumber", "unitNumber", "floor", "unitArea", "min", "buildingArea", "groundArea", "numberOfRooms", "numberOfBathrooms", "numberOfFloors", "unitFacing", "view", "finishingType", "fitOutCondition", "furnishingStatus", "groundLayoutStatus", "unitDesign", "activity", "deliveryStatus", "deliveryDate", "otherAccessories", "legalStatus", "paymentSystem", "pricePerMeterInInstallment", "totalPriceInInstallment", "pricePerMeterInCash", "totalPriceInCash", "diagram", "layout", "videos", "locationInMasterPlan", "getCurrentForm", "getUnitTypes", "subscribe", "next", "response", "Object", "entries", "data", "map", "console", "log", "error", "err", "complete", "detectChanges", "filterUnitTypes", "get", "patchValue", "getCities", "warn", "<PERSON><PERSON><PERSON><PERSON>", "getFieldsToShow", "shouldShowField", "fieldName", "includes", "selectCity", "cityName", "selectUnitType", "UnitValue", "unitTypesWithFacing", "unitTypesWithAreaFields", "unitTypesWithActivity", "unitTypesWithGroundLayout", "unitTypesWithUnitDesign", "unitTypesWithFitOutCondition", "unitTypesToHideFurnishing", "unitTypesWithLegalStatus", "selectArea", "areaName", "selectStep2Value", "selectStep3Value", "clear<PERSON>rice<PERSON><PERSON>s", "shouldShow<PERSON>ash<PERSON>ields", "shouldShowInstallmentFields", "shouldShowUnitFacingField", "unitType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "shouldShowGroundLayoutStatusField", "shouldShowActivityField", "shouldShowUnitDesignField", "shouldShowLegalStatusField", "shouldShowFitOutConditionField", "shouldShowFurnishingStatusField", "selectStep0Value", "selectStep5Value", "submitForm", "checkAd", "_this", "isCurrentFormValid", "formData", "httpFormData", "FormData", "keys", "for<PERSON>ach", "append", "additionalDetailsFields", "push", "additionalDetails", "field", "fileFields", "files", "length", "isMultiple", "file", "accessoriesRaw", "accessoriesArray", "Array", "isArray", "accessory", "index", "toString", "button", "document", "querySelector", "classList", "add", "createProperty", "_ref", "_asyncToGenerator", "fire", "status", "navigate", "queryParams", "success", "_x", "apply", "arguments", "message", "remove", "cancel", "onFileChange", "event", "target", "from", "getFileCount", "currentForm", "<PERSON><PERSON><PERSON><PERSON>", "isStep2FormValid", "valid", "form", "fieldsToShow", "requiredFields", "control", "invalid", "errors", "conditionalFields", "isFieldRequiredForUnitType", "nextStep", "prevStep", "formatUnitTypeKey", "split", "word", "trim", "toUpperCase", "slice", "toLowerCase", "join", "toggleAccessory", "indexOf", "splice", "onAllAccessoriesChange", "checked", "a", "isAccessorySelected", "getSelectedAccessoriesText", "find", "getCompoundTypeText", "option", "opt", "getUnitTypeText", "unit", "__decorate", "selector", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\add-property\\add-property.component.ts"], "sourcesContent": ["import { Component, OnInit, ChangeDetectorRef } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { PropertyData } from 'src/app/models/property.model';\r\nimport { PropertyService } from '../services/property.service';\r\nimport Swal from 'sweetalert2';\r\n@Component({\r\n  selector: 'app-add-property',\r\n  templateUrl: './add-property.component.html',\r\n  styleUrl: './add-property.component.scss',\r\n})\r\nexport class AddPropertyComponent implements OnInit {\r\n  totalSteps = 6;\r\n  currentStep = 0;\r\n  selectedCityId: any;\r\n  selectedCityName: string;\r\n  selectedAreaName: string;\r\n  selectedUnitType: string;\r\n  cities: any[] = [];\r\n  unitTypes: { key: string; value: string }[] = [];\r\n  areas: any[] = [];\r\n  isLoadingCities = false;\r\n\r\n  otherAccessoriesList = [\r\n  { key: 'GARAGE', value: 'garage' },\r\n  { key: 'CLUBHOUSE', value: 'clubhouse' },\r\n  { key: 'CLUB', value: 'club' },\r\n  { key: 'STORAGE', value: 'storage' },\r\n  { key: 'ELEVATOR', value: 'elevator' },\r\n  { key: 'SWIMMING POOL', value: 'swimming_pool' },\r\n  { key: 'ALL THE ABOVE', value: 'all_the_above_are_suitable' }\r\n];\r\n\r\n\r\n  //get brokerId from session\r\n  brokerId: number;\r\n\r\n  finishingTypes: { key: string; value: string }[] = [\r\n    { key: 'On Brick', value: 'on_brick' },\r\n    { key: 'Semi Finished', value: 'semi_finished' },\r\n    { key: 'Company Finished', value: 'company_finished' },\r\n    { key: 'Super Lux', value: 'super_lux' },\r\n    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },\r\n  ];\r\n\r\n  floorTypes: { key: string; value: string }[] = [\r\n    { key: 'Ground', value: 'ground' },\r\n    { key: 'Last Floor', value: 'last_floor' },\r\n    { key: 'Repeated', value: 'repeated' },\r\n    { key: 'All Of The Above', value: 'all_the_above_are_suitable' },\r\n  ];\r\n\r\n  viewTypes: { key: string; value: string }[] = [\r\n    { key: 'Water View', value: 'water_view' },\r\n    { key: 'Gardens And Landscape', value: 'gardens_and_landscape' },\r\n    { key: 'Street', value: 'street' },\r\n    { key: 'Entertainment Area', value: 'entertainment_area' },\r\n    { key: 'Garden  ', value: 'garden' },\r\n    { key: 'Main Street', value: ' main_street' },\r\n    { key: 'Square', value: 'square' },\r\n    { key: 'Side Street', value: 'side_street' },\r\n    { key: 'Rear View', value: 'rear_view' },\r\n  ];\r\n\r\n  deliveryTypes: { key: string; value: string }[] = [\r\n    { key: 'Immediate Delivery', value: 'immediate_delivery' },\r\n    { key: 'Under Construction', value: 'under_construction' },\r\n  ];\r\n\r\n  activityTypes: { key: string; value: string }[] = [\r\n    { key: 'Administrative Only', value: 'administrative_only' },\r\n    { key: 'Commercial Only', value: 'commercial_only' },\r\n    { key: 'Medical Only', value: 'medical_only' },\r\n    {\r\n      key: 'Administrative And Commercial',\r\n      value: 'administrative_and_commercial',\r\n    },\r\n    {\r\n      key: 'Administrative Commercial And Medical',\r\n      value: 'administrative_commercial_and_medical',\r\n    },\r\n  ];\r\n\r\n  fitOutConditionTypes: { key: string; value: string }[] = [\r\n    { key: 'Unfitted', value: 'unfitted' },\r\n    { key: 'Fully Fitted', value: 'fully_fitted' },\r\n    { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },\r\n  ];\r\n\r\n  furnishingStatusTypes: { key: string; value: string }[] = [\r\n    { key: 'Unfurnished', value: 'unfurnished' },\r\n    {\r\n      key: 'Furnished With Air Conditioners',\r\n      value: 'furnished_with_air_conditioners',\r\n    },\r\n    {\r\n      key: 'Furnished Without Air Conditioners',\r\n      value: 'furnished_without_air_conditioners',\r\n    },\r\n  ];\r\n\r\n  groundLayoutStatusTypes: { key: string; value: string }[] = [\r\n    { key: 'Vacant Land', value: 'vacant_land' },\r\n    { key: 'Under Construction', value: 'under_construction' },\r\n    { key: 'Fully Built', value: 'fully_built' },\r\n    { key: 'All Acceptable', value: 'all_acceptable' },\r\n  ];\r\n\r\n  unitDesignTypes: { key: string; value: string }[] = [\r\n    { key: 'Custom Design', value: 'custom_design' },\r\n    { key: 'One Apartment Per Floor', value: 'one_apartment_per_floor' },\r\n    { key: 'Two Apartments Per Floor', value: 'two_apartments_per_floor' },\r\n    {\r\n      key: 'More Than Two Apartments Per Floor',\r\n      value: 'more_than_two_apartments_per_floor',\r\n    },\r\n    { key: 'All Acceptable', value: 'all_acceptable' },\r\n  ];\r\n\r\n  otherAccessoriesTypes: { key: string; value: string }[] = [\r\n    { key: 'Garage', value: 'garage' },\r\n    { key: 'Clubhouse', value: 'clubhouse' },\r\n    { key: 'Club', value: 'club' },\r\n    { key: 'Storage', value: 'storage' },\r\n    { key: 'Elevator', value: 'elevator' },\r\n    { key: 'Swimming Pool', value: 'swimming_pool' },\r\n  ];\r\n\r\n  selectedAccessories: string[] = [];\r\n\r\n  paymentTypes: { key: string; value: string }[] = [\r\n    { key: 'Cash', value: 'cash' },\r\n    { key: 'Installment', value: 'installment' },\r\n    {\r\n      key: 'All Of The Above Are Suitable ',\r\n      value: 'all_of_the_above_are_suitable',\r\n    },\r\n  ];\r\n\r\n  legalTypes: { key: string; value: string }[] = [\r\n    { key: 'Licensed', value: 'licensed' },\r\n    { key: 'Reconciled', value: 'reconciled' },\r\n    { key: 'Reconciliation Required', value: 'reconciliation_required' },\r\n  ];\r\n\r\n  // Step 0 options\r\n  compoundOptions: { key: string; value: string }[] = [\r\n    { key: 'Outside Compound', value: 'outside_compound' },\r\n    { key: 'Inside Compound', value: 'inside_compound' },\r\n  ];\r\n\r\n\r\n  // All unit types for filtering\r\n  allUnitTypes: { key: string; value: string }[] = [];\r\n\r\n  // Unit types for outside compound\r\n  outsideCompoundUnitTypes: { key: string; value: string }[] = [\r\n    // Residential\r\n    { key: 'Apartments', value: 'apartments' },\r\n    { key: 'Duplexes', value: 'duplexes' },\r\n    { key: 'Studios', value: 'studios' },\r\n    { key: 'Penthouses', value: 'penthouses' },\r\n    { key: 'Basement', value: 'basement' },\r\n    { key: 'Roofs', value: 'roofs' },\r\n    { key: 'Standalone Villas', value: 'standalone_villas' },\r\n    { key: 'Residential Buildings', value: 'residential_buildings' },\r\n\r\n    // Commercial/Administrative\r\n    { key: 'Commercial Administrative Buildings', value: 'commercial_administrative_buildings' },\r\n    { key: 'Administrative Units', value: 'administrative_units' },\r\n    { key: 'Medical Clinics', value: 'medical_clinics' },\r\n    { key: 'Pharmacies', value: 'pharmacies' },\r\n    { key: 'Commercial Stores', value: 'commercial_stores' },\r\n\r\n    // Industrial\r\n    { key: 'Warehouses', value: 'warehouses' },\r\n    { key: 'Factories', value: 'factories' },\r\n\r\n    // Lands\r\n    { key: 'Residential Villa Lands', value: 'residential_villa_lands' },\r\n    { key: 'Residential Buildings Lands', value: 'residential_buildings_lands' },\r\n    { key: 'Administrative Lands', value: 'administrative_lands' },\r\n    { key: 'Commercial Lands', value: 'commercial_lands' },\r\n    { key: 'Medical Lands', value: 'medical_lands' },\r\n    { key: 'Mixed Lands', value: 'mixed_lands' },\r\n    { key: 'Warehouses Land', value: 'warehouses_land' },\r\n    { key: 'Factory Lands', value: 'factory_lands' },\r\n  ];\r\n\r\n  // Unit types for inside compound (will be loaded from API)\r\n  insideCompoundUnitTypes: { key: string; value: string }[] = [];\r\n\r\n  // Filtered unit types based on compound selection\r\n  filteredUnitTypes: { key: string; value: string }[] = [];\r\n\r\n  step0Form: FormGroup;\r\n  step1Form: FormGroup;\r\n  step2Form: FormGroup;\r\n  step3Form: FormGroup;\r\n  step4Form: FormGroup;\r\n  step5Form: FormGroup;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private propertyService: PropertyService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.brokerId = 5;\r\n    this.initForms();\r\n    this.loadUnitTypes();\r\n    this.loadCities();\r\n    this.loadAreas();\r\n\r\n    // Initialize filtered unit types as empty\r\n    this.filteredUnitTypes = [];\r\n  }\r\n\r\n  initForms() {\r\n    // Step 0: Property Category Selection\r\n    this.step0Form = this.fb.group({\r\n      compoundType: ['', [Validators.required]],\r\n\r\n      type: ['', [Validators.required]], // Unit Type moved to step 0\r\n    });\r\n\r\n    // Step 1: Basic Property Settings\r\n    this.step1Form = this.fb.group({\r\n      cityId: ['', [Validators.required]],\r\n      areaId: ['', [Validators.required]],\r\n      detailedAddress: ['', [Validators.required, Validators.maxLength(255)]],\r\n      location: ['', [Validators.required, Validators.pattern('https?://.+')]],\r\n      ownerName: ['', Validators.required],\r\n      ownerPhone: [\r\n        '',\r\n        [Validators.required, Validators.pattern('^01[0-2,5]{1}[0-9]{8}$')],\r\n      ],\r\n    });\r\n\r\n    // Step 2: Unit Information\r\n    this.step2Form = this.fb.group({\r\n      buildingNumber: ['', [Validators.maxLength(50)]],\r\n      unitNumber: ['', [Validators.maxLength(50)]],\r\n      floor: ['', [Validators.required]],\r\n      unitArea: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(1),\r\n          Validators.pattern('^[0-9]*$'),\r\n        ],\r\n      ],\r\n      buildingArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],\r\n      groundArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],\r\n      numberOfRooms: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]*$'),\r\n        ],\r\n      ],\r\n      numberOfBathrooms: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]*$'),\r\n        ],\r\n      ],\r\n      numberOfFloors: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]*$'),\r\n        ],\r\n      ],\r\n      unitFacing: [''],\r\n      view: ['', [Validators.required]],\r\n      finishingType: ['', [Validators.required]],\r\n      fitOutCondition: [''],\r\n      furnishingStatus: [''],\r\n      groundLayoutStatus: [''],\r\n      unitDesign: [''],\r\n      activity: [''],\r\n      deliveryStatus: ['', [Validators.required]],\r\n      deliveryDate: [''],\r\n      otherAccessories: [''], // Optional field - no validators to avoid blocking navigation\r\n      legalStatus: [''], // Moved from step5Form\r\n    });\r\n\r\n    // Step 3: Financial Information\r\n    this.step3Form = this.fb.group({\r\n      paymentSystem: ['', Validators.required],\r\n      pricePerMeterInInstallment: ['', [Validators.min(0)]],\r\n      totalPriceInInstallment: ['', [Validators.min(0)]],\r\n      pricePerMeterInCash: ['', [Validators.min(0)]],\r\n      totalPriceInCash: ['', [Validators.min(0)]],\r\n    });\r\n\r\n    // Step 4: Project Documents\r\n    this.step4Form = this.fb.group({\r\n      diagram: [[]],\r\n      layout: [[]],\r\n      videos: [[]],\r\n      locationInMasterPlan: [[]],\r\n    });\r\n\r\n    // Step 5: Owner Information\r\n    this.step5Form = this.fb.group({\r\n      // legalStatus moved to step2Form\r\n    });\r\n  }\r\n\r\n  // Get current form based on step\r\n  getCurrentForm(): FormGroup {\r\n    switch (this.currentStep) {\r\n      case 0:\r\n        return this.step0Form;\r\n      case 1:\r\n        return this.step1Form;\r\n      case 2:\r\n        return this.step2Form;\r\n      case 3:\r\n        return this.step3Form;\r\n      case 4:\r\n        return this.step4Form;\r\n      case 5:\r\n        return this.step5Form;\r\n      default:\r\n        return this.step0Form;\r\n    }\r\n  }\r\n\r\n  loadUnitTypes(): void {\r\n    this.propertyService.getUnitTypes().subscribe({\r\n      next: (response) => {\r\n        this.allUnitTypes = Object.entries(response.data).map(([key, value]) => ({\r\n          key,\r\n          value: value as string,\r\n        }));\r\n        this.insideCompoundUnitTypes = this.allUnitTypes; // API data for inside compound\r\n        console.log('Raw API Response:', this.allUnitTypes);\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading unitTypes:', err);\r\n      },\r\n      complete: () => {\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  // Filter unit types based on compound selection\r\n  filterUnitTypes(): void {\r\n    const compoundType = this.step0Form.get('compoundType')?.value;\r\n\r\n    if (compoundType === 'outside_compound') {\r\n      this.filteredUnitTypes = this.outsideCompoundUnitTypes;\r\n    } else if (compoundType === 'inside_compound') {\r\n      this.filteredUnitTypes = this.insideCompoundUnitTypes;\r\n    } else {\r\n      this.filteredUnitTypes = [];\r\n    }\r\n\r\n    // Clear unit type selection when compound type changes\r\n    this.step0Form.patchValue({ type: '' });\r\n    this.selectedUnitType = '';\r\n\r\n    // Trigger change detection to update button state\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  loadCities(): void {\r\n    this.isLoadingCities = true;\r\n    this.propertyService.getCities().subscribe({\r\n      next: (response) => {\r\n        if (response && response.data) {\r\n          this.cities = response.data;\r\n        } else {\r\n          console.warn('No cities data in response');\r\n          this.cities = [];\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading cities:', err);\r\n      },\r\n      complete: () => {\r\n        this.isLoadingCities = false;\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  loadAreas(cityId?: number): void {\r\n    this.propertyService.getAreas(cityId).subscribe({\r\n      next: (response) => {\r\n        if (response && response.data) {\r\n          this.areas = response.data;\r\n        } else {\r\n          console.warn('No areas data in response');\r\n          this.areas = [];\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading areas:', err);\r\n        this.areas = [];\r\n      },\r\n      complete: () => {\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  //**************************************************************** */\r\n\r\n// STEP 2\r\ngetFieldsToShow(): any[] {\r\n  const compoundType = this.step0Form.get('compoundType')?.value;\r\n  const type = this.step0Form.get('type')?.value;\r\n\r\n  // For outside compound apartments\r\n  if (compoundType === 'outside_compound' &&  (type === 'apartments' ||type === 'duplexes' || type === 'studios' || type === 'penthouses'|| type === 'roofs'|| type === 'basement')) {\r\n    return ['buildingNumber', 'unitNumber', 'floor', 'unitArea' ,'numberOfRooms' , 'numberOfBathrooms',  'unitFacing', 'view', 'finishingType','deliveryStatus', 'legalStatus', 'otherAccessories' , 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];\r\n  }\r\n\r\n  return [ ];\r\n}\r\n\r\n// Check if a specific field should be shown\r\nshouldShowField(fieldName: string): boolean {\r\n  return this.getFieldsToShow().includes(fieldName);\r\n}\r\n\r\n\r\n  // /*/***************************** */\r\n  selectCity(cityId: number, cityName: string) {\r\n    this.selectedCityId = cityId;\r\n    this.selectedCityName = cityName;\r\n    this.step1Form.patchValue({\r\n      cityId: cityId,\r\n    });\r\n    this.loadAreas(cityId);\r\n  }\r\n\r\n  selectUnitType(UnitValue: string) {\r\n    this.selectedUnitType = UnitValue;\r\n    this.step0Form.patchValue({\r\n      type: UnitValue,\r\n    });\r\n\r\n    // Clear unitFacing field if unit type doesn't require it\r\n    const unitTypesWithFacing = [\r\n      'apartments',\r\n      'duplexes',\r\n      'studios',\r\n      'penthouses',\r\n    ];\r\n    if (!unitTypesWithFacing.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        unitFacing: null,\r\n      });\r\n    }\r\n\r\n    // Clear buildingArea and groundArea fields if unit type doesn't require them\r\n    const unitTypesWithAreaFields = [\r\n      'standalone_villas',\r\n      'factory_lands',\r\n      'commercial_administrative_buildings',\r\n      'residential_buildings',\r\n      'warehouses',\r\n\r\n    ];\r\n    if (!unitTypesWithAreaFields.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        buildingArea: null,\r\n        groundArea: null,\r\n      });\r\n    }\r\n\r\n    // Clear activity field if unit type doesn't require it\r\n    const unitTypesWithActivity = [\r\n      'commercial_stores',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    if (!unitTypesWithActivity.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        activity: null,\r\n      });\r\n    }\r\n\r\n    // Clear groundLayoutStatus field if unit type doesn't require it\r\n    const unitTypesWithGroundLayout = [\r\n      'factory_lands',\r\n      'warehouses',\r\n      'residential_buildings',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    if (!unitTypesWithGroundLayout.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        groundLayoutStatus: null,\r\n      });\r\n    }\r\n\r\n    // Clear unitDesign field if unit type doesn't require it\r\n    const unitTypesWithUnitDesign = ['standalone_villas'];\r\n    if (!unitTypesWithUnitDesign.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        unitDesign: null,\r\n      });\r\n    }\r\n\r\n    // Clear fitOutCondition field if unit type doesn't require it\r\n    const unitTypesWithFitOutCondition = [\r\n      'pharmacies',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_stores',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    if (!unitTypesWithFitOutCondition.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        fitOutCondition: null,\r\n      });\r\n    }\r\n\r\n    // Clear furnishingStatus field if unit type doesn't require it\r\n    const unitTypesToHideFurnishing = [\r\n      'pharmacies',\r\n      'commercial_stores',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_administrative_buildings',\r\n      'administrative_units',\r\n    ];\r\n    if (unitTypesToHideFurnishing.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        furnishingStatus: null,\r\n      });\r\n    }\r\n\r\n    // Clear legalStatus field if unit type doesn't require it\r\n    const unitTypesWithLegalStatus = [\r\n      'duplexes',\r\n      'penthouses',\r\n      'basement',\r\n      'roofs',\r\n    ];\r\n    if (!unitTypesWithLegalStatus.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        legalStatus: null,\r\n      });\r\n    }\r\n  }\r\n\r\n  selectArea(areaId: number, areaName: string) {\r\n    this.selectedAreaName = areaName;\r\n    this.step1Form.patchValue({\r\n      areaId: areaId,\r\n    });\r\n  }\r\n\r\n  // dropdown values for step 2\r\n  selectStep2Value(fieldName: string, value: string) {\r\n    this.step2Form.patchValue({\r\n      [fieldName]: value,\r\n    });\r\n  }\r\n\r\n  //dropdown values for step 3\r\n  selectStep3Value(fieldName: string, value: string) {\r\n    this.step3Form.patchValue({\r\n      [fieldName]: value,\r\n    });\r\n\r\n    // Clear price fields when payment system changes\r\n    if (fieldName === 'paymentSystem') {\r\n      this.clearPriceFields();\r\n    }\r\n  }\r\n\r\n  // Clear all price fields when payment system changes\r\n  clearPriceFields() {\r\n    this.step3Form.patchValue({\r\n      pricePerMeterInCash: null,\r\n      totalPriceInCash: null,\r\n      pricePerMeterInInstallment: null,\r\n      totalPriceInInstallment: null,\r\n    });\r\n  }\r\n\r\n  // Check if cash price fields should be displayed\r\n  shouldShowCashFields(): boolean {\r\n    const paymentSystem = this.step3Form.get('paymentSystem')?.value;\r\n    return (\r\n      paymentSystem === 'cash' ||\r\n      paymentSystem === 'all_of_the_above_are_suitable'\r\n    );\r\n  }\r\n\r\n  // Check if installment price fields should be displayed\r\n  shouldShowInstallmentFields(): boolean {\r\n    const paymentSystem = this.step3Form.get('paymentSystem')?.value;\r\n    return (\r\n      paymentSystem === 'installment' ||\r\n      paymentSystem === 'all_of_the_above_are_suitable'\r\n    );\r\n  }\r\n\r\n  // Check if unitFacing field should be displayed\r\n  shouldShowUnitFacingField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithFacing = [\r\n      'apartments',\r\n      'duplexes',\r\n      'studios',\r\n      'penthouses',\r\n    ];\r\n    return unitTypesWithFacing.includes(unitType);\r\n  }\r\n\r\n  // Check if buildingArea and groundArea fields should be displayed\r\n  shouldShowAreaFields(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithAreaFields = [\r\n      'standalone_villas',\r\n      'factory_lands',\r\n      'commercial_administrative_buildings',\r\n      'residential_buildings',\r\n      'warehouses',\r\n    ];\r\n    return unitTypesWithAreaFields.includes(unitType);\r\n  }\r\n\r\n  // Check if groundLayoutStatus field should be displayed\r\n  shouldShowGroundLayoutStatusField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithGroundLayout = [\r\n      'factory_lands',\r\n      'warehouses',\r\n      'residential_buildings',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    return unitTypesWithGroundLayout.includes(unitType);\r\n  }\r\n\r\n  // Check if activity field should be displayed\r\n  shouldShowActivityField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithActivity = [\r\n      'commercial_stores',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    return unitTypesWithActivity.includes(unitType);\r\n  }\r\n\r\n  // Check if unitDesign field should be displayed\r\n  shouldShowUnitDesignField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithUnitDesign = ['standalone_villas'];\r\n    return unitTypesWithUnitDesign.includes(unitType);\r\n  }\r\n\r\n  // Check if legalStatus field should be displayed\r\n  shouldShowLegalStatusField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithLegalStatus = [\r\n      'duplexes',\r\n      'penthouses',\r\n      'basement',\r\n      'roofs',\r\n    ];\r\n    return unitTypesWithLegalStatus.includes(unitType);\r\n  }\r\n\r\n  // Check if fitOutCondition field should be displayed\r\n  shouldShowFitOutConditionField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithFitOutCondition = [\r\n      'pharmacies',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_stores',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    return unitTypesWithFitOutCondition.includes(unitType);\r\n  }\r\n\r\n  // Check if furnishingStatus field should be displayed\r\n  shouldShowFurnishingStatusField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesToHideFurnishing = [\r\n      'pharmacies',\r\n      'commercial_stores',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_administrative_buildings',\r\n      'administrative_units',\r\n    ];\r\n    return !unitTypesToHideFurnishing.includes(unitType);\r\n  }\r\n\r\n  // dropdown values for step 0\r\n  selectStep0Value(fieldName: string, value: string) {\r\n    console.log('selectStep0Value called:', { fieldName, value });\r\n\r\n    this.step0Form.patchValue({\r\n      [fieldName]: value,\r\n    });\r\n\r\n    // Filter unit types when compound type changes\r\n    if (fieldName === 'compoundType') {\r\n      this.filterUnitTypes();\r\n    }\r\n\r\n    // Handle unit type selection\r\n    if (fieldName === 'type') {\r\n      this.selectedUnitType = value;\r\n      this.selectUnitType(value); // Call existing logic for unit type selection\r\n    }\r\n\r\n    console.log('Step0 form after update:', this.step0Form.value);\r\n\r\n    // Trigger change detection to update button state\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  //dropdown values for step 5\r\n  selectStep5Value(fieldName: string, value: string) {\r\n    this.step5Form.patchValue({\r\n      [fieldName]: value,\r\n    });\r\n  }\r\n\r\n  submitForm(checkAd : boolean) {\r\n    if (this.isCurrentFormValid()) {\r\n      const formData: PropertyData = {\r\n        ...this.step0Form.value,\r\n        ...this.step1Form.value,\r\n        ...this.step2Form.value,\r\n        ...this.step3Form.value,\r\n        ...this.step5Form.value,\r\n      };\r\n      // console.log(formData);\r\n      const httpFormData = new FormData();\r\n\r\n      // Add step0 form data\r\n      Object.keys(this.step0Form.value).forEach((key) => {\r\n        httpFormData.append(key, this.step0Form.value[key]);\r\n      });\r\n\r\n      // Add step1 form data\r\n      Object.keys(this.step1Form.value).forEach((key) => {\r\n        httpFormData.append(key, this.step1Form.value[key]);\r\n      });\r\n\r\n      // Fields to be included in additionalDetails array\r\n      const additionalDetailsFields = ['numberOfFloors'];\r\n\r\n      // Get unit type for conditional field inclusion\r\n      const unitType = this.step0Form.get('type')?.value;\r\n\r\n      // Add buildingArea and groundArea to additionalDetails only for specific unit types\r\n      const unitTypesWithAreaFields = [\r\n        'standalone_villas',\r\n        'factory_lands',\r\n        'commercial_administrative_buildings',\r\n        'residential_buildings',\r\n        'warehouses',\r\n      ];\r\n      if (unitTypesWithAreaFields.includes(unitType)) {\r\n        additionalDetailsFields.push('groundArea');\r\n        additionalDetailsFields.push('buildingArea');\r\n      }\r\n\r\n      // Add activity to additionalDetails only for specific unit types\r\n      const unitTypesWithActivity = [\r\n        'commercial_stores',\r\n        'factory_lands',\r\n        'warehouses',\r\n        'commercial_administrative_buildings',\r\n      ];\r\n      if (unitTypesWithActivity.includes(unitType)) {\r\n        additionalDetailsFields.push('activity');\r\n      }\r\n\r\n      // Add groundLayoutStatus to additionalDetails only for specific unit types\r\n      const unitTypesWithGroundLayout = [\r\n        'factory_lands',\r\n        'warehouses',\r\n        'residential_buildings',\r\n        'commercial_administrative_buildings',\r\n      ];\r\n      if (unitTypesWithGroundLayout.includes(unitType)) {\r\n        additionalDetailsFields.push('groundLayoutStatus');\r\n      }\r\n\r\n      // Add unitDesign to additionalDetails only for specific unit types\r\n      const unitTypesWithUnitDesign = ['standalone_villas'];\r\n      if (unitTypesWithUnitDesign.includes(unitType)) {\r\n        additionalDetailsFields.push('unitDesign');\r\n      }\r\n\r\n      // Add fitOutCondition to additionalDetails only for specific unit types\r\n      const unitTypesWithFitOutCondition = [\r\n        'pharmacies',\r\n        'factory_lands',\r\n        'warehouses',\r\n        'commercial_stores',\r\n        'commercial_administrative_buildings',\r\n      ];\r\n      if (unitTypesWithFitOutCondition.includes(unitType)) {\r\n        additionalDetailsFields.push('fitOutCondition');\r\n      }\r\n\r\n      // Add furnishingStatus to additionalDetails only for specific unit types\r\n      const unitTypesToHideFurnishing = [\r\n        'pharmacies',\r\n        'commercial_stores',\r\n        'factory_lands',\r\n        'warehouses',\r\n        'commercial_administrative_buildings',\r\n        'administrative_units',\r\n      ];\r\n      if (!unitTypesToHideFurnishing.includes(unitType)) {\r\n        additionalDetailsFields.push('furnishingStatus');\r\n      }\r\n\r\n      // Add unitFacing to additionalDetails only for specific unit types\r\n      const unitTypesWithFacing = [\r\n        'apartments',\r\n        'duplexes',\r\n        'studios',\r\n        'penthouses',\r\n      ];\r\n      if (unitTypesWithFacing.includes(unitType)) {\r\n        additionalDetailsFields.push('unitFacing');\r\n      }\r\n\r\n      // Add step2 form data (excluding fields that go to additionalDetails and otherAccessories)\r\n      Object.keys(this.step2Form.value).forEach((key) => {\r\n        if (\r\n          key !== 'otherAccessories' &&\r\n          !additionalDetailsFields.includes(key)\r\n        ) {\r\n          httpFormData.append(key, this.step2Form.value[key]);\r\n        }\r\n      });\r\n\r\n      // Add step3 form data (conditionally based on payment system)\r\n      const paymentSystem = this.step3Form.get('paymentSystem')?.value;\r\n\r\n      // Always add payment system\r\n      httpFormData.append('paymentSystem', paymentSystem);\r\n\r\n      // Conditionally add price fields based on payment system\r\n      if (paymentSystem === 'cash') {\r\n        // Only send cash price fields\r\n        const pricePerMeterInCash = this.step3Form.get(\r\n          'pricePerMeterInCash'\r\n        )?.value;\r\n        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;\r\n\r\n        if (pricePerMeterInCash) {\r\n          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);\r\n        }\r\n        if (totalPriceInCash) {\r\n          httpFormData.append('totalPriceInCash', totalPriceInCash);\r\n        }\r\n      } else if (paymentSystem === 'installment') {\r\n        // Only send installment price fields\r\n        const pricePerMeterInInstallment = this.step3Form.get(\r\n          'pricePerMeterInInstallment'\r\n        )?.value;\r\n        const totalPriceInInstallment = this.step3Form.get(\r\n          'totalPriceInInstallment'\r\n        )?.value;\r\n\r\n        if (pricePerMeterInInstallment) {\r\n          httpFormData.append(\r\n            'pricePerMeterInInstallment',\r\n            pricePerMeterInInstallment\r\n          );\r\n        }\r\n        if (totalPriceInInstallment) {\r\n          httpFormData.append(\r\n            'totalPriceInInstallment',\r\n            totalPriceInInstallment\r\n          );\r\n        }\r\n      } else if (paymentSystem === 'all_of_the_above_are_suitable') {\r\n        // Send all price fields\r\n        const pricePerMeterInCash = this.step3Form.get(\r\n          'pricePerMeterInCash'\r\n        )?.value;\r\n        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;\r\n        const pricePerMeterInInstallment = this.step3Form.get(\r\n          'pricePerMeterInInstallment'\r\n        )?.value;\r\n        const totalPriceInInstallment = this.step3Form.get(\r\n          'totalPriceInInstallment'\r\n        )?.value;\r\n\r\n        if (pricePerMeterInCash) {\r\n          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);\r\n        }\r\n        if (totalPriceInCash) {\r\n          httpFormData.append('totalPriceInCash', totalPriceInCash);\r\n        }\r\n        if (pricePerMeterInInstallment) {\r\n          httpFormData.append(\r\n            'pricePerMeterInInstallment',\r\n            pricePerMeterInInstallment\r\n          );\r\n        }\r\n        if (totalPriceInInstallment) {\r\n          httpFormData.append(\r\n            'totalPriceInInstallment',\r\n            totalPriceInInstallment\r\n          );\r\n        }\r\n      }\r\n\r\n      // Add step5 form data (excluding legalStatus which goes to additionalDetails)\r\n      Object.keys(this.step5Form.value).forEach((key) => {\r\n        if (key !== 'legalStatus') {\r\n          httpFormData.append(key, this.step5Form.value[key]);\r\n        }\r\n      });\r\n\r\n      // Create additionalDetails object\r\n      const additionalDetails: any = {};\r\n\r\n      // Add fields from step2Form\r\n      additionalDetailsFields.forEach((field) => {\r\n        const value = this.step2Form.get(field)?.value;\r\n        if (value) {\r\n          additionalDetails[field] = value;\r\n        }\r\n      });\r\n\r\n      // Add legalStatus from step5Form only for specific unit types\r\n      const unitTypesWithLegalStatus = [\r\n        'duplexes',\r\n        'penthouses',\r\n        'basement',\r\n        'roofs',\r\n      ];\r\n      if (unitTypesWithLegalStatus.includes(unitType)) {\r\n        const legalStatus = this.step5Form.get('legalStatus')?.value;\r\n        if (legalStatus) {\r\n          additionalDetails['legalStatus'] = legalStatus;\r\n        }\r\n      }\r\n\r\n      // Send additionalDetails as individual form fields (not JSON)\r\n      Object.keys(additionalDetails).forEach((key) => {\r\n        httpFormData.append(\r\n          `additionalDetails[${key}]`,\r\n          additionalDetails[key]\r\n        );\r\n      });\r\n\r\n      //add files\r\n      const fileFields = [\r\n        'diagram',\r\n        'layout',\r\n        'videos',\r\n        'locationInMasterPlan',\r\n      ];\r\n      fileFields.forEach((field) => {\r\n        const files = this.step4Form.get(field)?.value;\r\n        if (files && files.length) {\r\n          const isMultiple = ['layout', 'videos'].includes(field);\r\n\r\n          if (isMultiple) {\r\n            files.forEach((file: File) => {\r\n              httpFormData.append(`${field}[]`, file);\r\n            });\r\n          } else {\r\n            httpFormData.append(field, files[0]);\r\n          }\r\n        }\r\n      });\r\n\r\n      // Handle otherAccessories as array\r\n      const accessoriesRaw = this.step2Form.get('otherAccessories')?.value;\r\n      const accessoriesArray = Array.isArray(accessoriesRaw)\r\n        ? accessoriesRaw\r\n        : [];\r\n\r\n      // Send otherAccessories as individual array elements\r\n      accessoriesArray.forEach((accessory, index) => {\r\n        httpFormData.append(`otherAccessories[${index}]`, accessory);\r\n      });\r\n\r\n      httpFormData.append('brokerId', this.brokerId.toString());\r\n\r\n      // Set as advertisement\r\n      if(checkAd){\r\n        httpFormData.append('isAdvertisement', '1');\r\n      }\r\n\r\n      // Show loading state\r\n      const button = document.querySelector('.btn-primary');\r\n      if (button) {\r\n        button.classList.add('btn-loading');\r\n      }\r\n\r\n      this.propertyService.createProperty(httpFormData).subscribe({\r\n        next: async (response) => {\r\n          console.log('Property data submitted:', response);\r\n          await Swal.fire('Property data submitted:', '', response.status);\r\n\r\n          this.router.navigate(['/broker/dataandproperties'], {\r\n            queryParams: { success: 'add' },\r\n          });\r\n        },\r\n        error: (err) => {\r\n          console.error('Error loading unitTypes:', err);\r\n          Swal.fire(err.message, '', err.status);\r\n\r\n          // Remove loading state\r\n          if (button) {\r\n            button.classList.remove('btn-loading');\r\n          }\r\n        },\r\n        complete: () => {\r\n          this.cdr.detectChanges();\r\n          // Remove loading state\r\n          if (button) {\r\n            button.classList.remove('btn-loading');\r\n          }\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  cancel() {\r\n    this.router.navigate(['/broker/dataandproperties']);\r\n  }\r\n\r\n  onFileChange(event: any, fieldName: string) {\r\n    if (event.target.files && event.target.files.length) {\r\n      const files = Array.from(event.target.files);\r\n      this.step4Form.patchValue({\r\n        [fieldName]: files,\r\n      });\r\n\r\n      console.log(`${fieldName}: ${files.length} files selected`);\r\n    }\r\n  }\r\n\r\n  getFileCount(fieldName: string): number {\r\n    const files = this.step4Form.get(fieldName)?.value;\r\n    return files && Array.isArray(files) ? files.length : 0;\r\n  }\r\n\r\n  // Check if current form is valid\r\n  isCurrentFormValid(): boolean {\r\n    const currentForm = this.getCurrentForm();\r\n\r\n    // For step 0, only check if unit type is selected\r\n    if (this.currentStep === 0) {\r\n      const compoundType = this.step0Form.get('compoundType')?.value;\r\n      const unitType = this.step0Form.get('type')?.value;\r\n      const isValid = !!(compoundType && unitType);\r\n      console.log('Step 0 validation:', { compoundType, unitType, isValid });\r\n      return isValid;\r\n    }\r\n\r\n    // For step 2, check only visible/required fields\r\n    if (this.currentStep === 2) {\r\n      return this.isStep2FormValid();\r\n    }\r\n\r\n    return currentForm.valid;\r\n  }\r\n\r\n  // Custom validation for Step 2 - only check visible fields\r\n  isStep2FormValid(): boolean {\r\n    const form = this.step2Form;\r\n    const fieldsToShow = this.getFieldsToShow();\r\n\r\n    // Required fields that must always be valid if they're shown\r\n    const requiredFields = ['unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus'];\r\n\r\n    // Note: otherAccessories, deliveryDate, buildingNumber, unitNumber are optional and don't block navigation\r\n\r\n    for (const fieldName of requiredFields) {\r\n      if (fieldsToShow.includes(fieldName)) {\r\n        const control = form.get(fieldName);\r\n        if (!control || control.invalid) {\r\n          console.log(`Step 2 validation failed for field: ${fieldName}`, control?.errors);\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Check conditional fields only if they're shown and required for the specific unit type\r\n    const conditionalFields = ['unitFacing', 'legalStatus', 'fitOutCondition', 'furnishingStatus', 'groundLayoutStatus', 'unitDesign', 'activity'];\r\n\r\n    for (const fieldName of conditionalFields) {\r\n      if (fieldsToShow.includes(fieldName) && this.isFieldRequiredForUnitType(fieldName)) {\r\n        const control = form.get(fieldName);\r\n        if (!control || control.invalid) {\r\n          console.log(`Step 2 validation failed for conditional field: ${fieldName}`, control?.errors);\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('Step 2 validation passed');\r\n    return true;\r\n  }\r\n\r\n  // Check if a field is required for the current unit type\r\n  isFieldRequiredForUnitType(fieldName: string): boolean {\r\n    switch (fieldName) {\r\n      case 'unitFacing':\r\n        return this.shouldShowUnitFacingField();\r\n      case 'legalStatus':\r\n        return this.shouldShowLegalStatusField();\r\n      case 'fitOutCondition':\r\n        return this.shouldShowFitOutConditionField();\r\n      case 'furnishingStatus':\r\n        return this.shouldShowFurnishingStatusField();\r\n      case 'groundLayoutStatus':\r\n        return this.shouldShowGroundLayoutStatusField();\r\n      case 'unitDesign':\r\n        return this.shouldShowUnitDesignField();\r\n      case 'activity':\r\n        return this.shouldShowActivityField();\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  // Navigate to next step\r\n  nextStep() {\r\n    if (this.currentStep < this.totalSteps) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  // Navigate to previous step\r\n  prevStep() {\r\n    if (this.currentStep > 0) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  formatUnitTypeKey(key: string): string {\r\n    if (!key || typeof key !== 'string') return '';\r\n\r\n    return key\r\n      .split('_')\r\n      .map((word) =>\r\n        word.trim() ? word[0].toUpperCase() + word.slice(1).toLowerCase() : ''\r\n      )\r\n      .join(' ');\r\n  }\r\n\r\n  toggleAccessory(value: string): void {\r\n    const index = this.selectedAccessories.indexOf(value);\r\n\r\n    if (index > -1) {\r\n      this.selectedAccessories.splice(index, 1);\r\n    } else {\r\n      this.selectedAccessories.push(value);\r\n    }\r\n\r\n    // Update form control\r\n    this.step2Form.patchValue({\r\n      otherAccessories: [...this.selectedAccessories],\r\n    });\r\n  }\r\n\r\n  // Handle \"All The Above Are Suitable\" checkbox\r\n  onAllAccessoriesChange(event: any): void {\r\n    if (event.target.checked) {\r\n      // Select all accessories\r\n      this.selectedAccessories = this.otherAccessoriesTypes.map((a) => a.value);\r\n    } else {\r\n      // Unselect all accessories\r\n      this.selectedAccessories = [];\r\n    }\r\n\r\n    this.step2Form.patchValue({\r\n      otherAccessories: [...this.selectedAccessories],\r\n    });\r\n  }\r\n\r\n  isAccessorySelected(value: string): boolean {\r\n    return this.selectedAccessories.includes(value);\r\n  }\r\n\r\n  getSelectedAccessoriesText(): string {\r\n    if (this.selectedAccessories.length === 0) {\r\n      return '';\r\n    }\r\n\r\n    if (this.selectedAccessories.length === 1) {\r\n      const accessory = this.otherAccessoriesTypes.find(\r\n        (a) => a.value === this.selectedAccessories[0]\r\n      );\r\n      return accessory ? accessory.key : '';\r\n    }\r\n\r\n    return `${this.selectedAccessories.length} accessories selected`;\r\n  }\r\n\r\n  // Get compound type text for display\r\n  getCompoundTypeText(value: string): string {\r\n    if (!value) return '';\r\n    const option = this.compoundOptions.find(opt => opt.value === value);\r\n    return option ? option.key : '';\r\n  }\r\n\r\n\r\n\r\n  // Get unit type text for display\r\n  getUnitTypeText(value: string): string {\r\n    if (!value) return '';\r\n    const unitType = this.filteredUnitTypes.find(unit => unit.value === value);\r\n    return unitType ? unitType.key : '';\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAmC,eAAe;AACpE,SAAiCC,UAAU,QAAQ,gBAAgB;AAInE,OAAOC,IAAI,MAAM,aAAa;AAMvB,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAgMrBC,EAAA;EACAC,MAAA;EACAC,eAAA;EACAC,GAAA;EAlMVC,UAAU,GAAG,CAAC;EACdC,WAAW,GAAG,CAAC;EACfC,cAAc;EACdC,gBAAgB;EAChBC,gBAAgB;EAChBC,gBAAgB;EAChBC,MAAM,GAAU,EAAE;EAClBC,SAAS,GAAqC,EAAE;EAChDC,KAAK,GAAU,EAAE;EACjBC,eAAe,GAAG,KAAK;EAEvBC,oBAAoB,GAAG,CACvB;IAAEC,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAED,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAE,EACxC;IAAED,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAM,CAAE,EAC9B;IAAED,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,EACpC;IAAED,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,EACtC;IAAED,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAe,CAAE,EAChD;IAAED,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE;EAA4B,CAAE,CAC9D;EAGC;EACAC,QAAQ;EAERC,cAAc,GAAqC,CACjD;IAAEH,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,EACtC;IAAED,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAe,CAAE,EAChD;IAAED,GAAG,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAkB,CAAE,EACtD;IAAED,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAE,EACxC;IAAED,GAAG,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAiB,CAAE,CACrD;EAEDG,UAAU,GAAqC,CAC7C;IAAEJ,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAED,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAED,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,EACtC;IAAED,GAAG,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAA4B,CAAE,CACjE;EAEDI,SAAS,GAAqC,CAC5C;IAAEL,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAED,GAAG,EAAE,uBAAuB;IAAEC,KAAK,EAAE;EAAuB,CAAE,EAChE;IAAED,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAED,GAAG,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAoB,CAAE,EAC1D;IAAED,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAQ,CAAE,EACpC;IAAED,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAE,EAC7C;IAAED,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAED,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAED,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAE,CACzC;EAEDK,aAAa,GAAqC,CAChD;IAAEN,GAAG,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAoB,CAAE,EAC1D;IAAED,GAAG,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAoB,CAAE,CAC3D;EAEDM,aAAa,GAAqC,CAChD;IAAEP,GAAG,EAAE,qBAAqB;IAAEC,KAAK,EAAE;EAAqB,CAAE,EAC5D;IAAED,GAAG,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAiB,CAAE,EACpD;IAAED,GAAG,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAc,CAAE,EAC9C;IACED,GAAG,EAAE,+BAA+B;IACpCC,KAAK,EAAE;GACR,EACD;IACED,GAAG,EAAE,uCAAuC;IAC5CC,KAAK,EAAE;GACR,CACF;EAEDO,oBAAoB,GAAqC,CACvD;IAAER,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,EACtC;IAAED,GAAG,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAc,CAAE,EAC9C;IAAED,GAAG,EAAE,4BAA4B;IAAEC,KAAK,EAAE;EAA4B,CAAE,CAC3E;EAEDQ,qBAAqB,GAAqC,CACxD;IAAET,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAa,CAAE,EAC5C;IACED,GAAG,EAAE,iCAAiC;IACtCC,KAAK,EAAE;GACR,EACD;IACED,GAAG,EAAE,oCAAoC;IACzCC,KAAK,EAAE;GACR,CACF;EAEDS,uBAAuB,GAAqC,CAC1D;IAAEV,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAED,GAAG,EAAE,oBAAoB;IAAEC,KAAK,EAAE;EAAoB,CAAE,EAC1D;IAAED,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAED,GAAG,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAgB,CAAE,CACnD;EAEDU,eAAe,GAAqC,CAClD;IAAEX,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAe,CAAE,EAChD;IAAED,GAAG,EAAE,yBAAyB;IAAEC,KAAK,EAAE;EAAyB,CAAE,EACpE;IAAED,GAAG,EAAE,0BAA0B;IAAEC,KAAK,EAAE;EAA0B,CAAE,EACtE;IACED,GAAG,EAAE,oCAAoC;IACzCC,KAAK,EAAE;GACR,EACD;IAAED,GAAG,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAgB,CAAE,CACnD;EAEDW,qBAAqB,GAAqC,CACxD;IAAEZ,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAED,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAE,EACxC;IAAED,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAM,CAAE,EAC9B;IAAED,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,EACpC;IAAED,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,EACtC;IAAED,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAe,CAAE,CACjD;EAEDY,mBAAmB,GAAa,EAAE;EAElCC,YAAY,GAAqC,CAC/C;IAAEd,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAM,CAAE,EAC9B;IAAED,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAa,CAAE,EAC5C;IACED,GAAG,EAAE,gCAAgC;IACrCC,KAAK,EAAE;GACR,CACF;EAEDc,UAAU,GAAqC,CAC7C;IAAEf,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,EACtC;IAAED,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAED,GAAG,EAAE,yBAAyB;IAAEC,KAAK,EAAE;EAAyB,CAAE,CACrE;EAED;EACAe,eAAe,GAAqC,CAClD;IAAEhB,GAAG,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAkB,CAAE,EACtD;IAAED,GAAG,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAiB,CAAE,CACrD;EAGD;EACAgB,YAAY,GAAqC,EAAE;EAEnD;EACAC,wBAAwB,GAAqC;EAC3D;EACA;IAAElB,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAED,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,EACtC;IAAED,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,EACpC;IAAED,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAED,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,EACtC;IAAED,GAAG,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAO,CAAE,EAChC;IAAED,GAAG,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAmB,CAAE,EACxD;IAAED,GAAG,EAAE,uBAAuB;IAAEC,KAAK,EAAE;EAAuB,CAAE;EAEhE;EACA;IAAED,GAAG,EAAE,qCAAqC;IAAEC,KAAK,EAAE;EAAqC,CAAE,EAC5F;IAAED,GAAG,EAAE,sBAAsB;IAAEC,KAAK,EAAE;EAAsB,CAAE,EAC9D;IAAED,GAAG,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAiB,CAAE,EACpD;IAAED,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAED,GAAG,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAmB,CAAE;EAExD;EACA;IAAED,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAED,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAE;EAExC;EACA;IAAED,GAAG,EAAE,yBAAyB;IAAEC,KAAK,EAAE;EAAyB,CAAE,EACpE;IAAED,GAAG,EAAE,6BAA6B;IAAEC,KAAK,EAAE;EAA6B,CAAE,EAC5E;IAAED,GAAG,EAAE,sBAAsB;IAAEC,KAAK,EAAE;EAAsB,CAAE,EAC9D;IAAED,GAAG,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAkB,CAAE,EACtD;IAAED,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAe,CAAE,EAChD;IAAED,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAED,GAAG,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAiB,CAAE,EACpD;IAAED,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAe,CAAE,CACjD;EAED;EACAkB,uBAAuB,GAAqC,EAAE;EAE9D;EACAC,iBAAiB,GAAqC,EAAE;EAExDC,SAAS;EACTC,SAAS;EACTC,SAAS;EACTC,SAAS;EACTC,SAAS;EACTC,SAAS;EAETC,YACU1C,EAAe,EACfC,MAAc,EACdC,eAAgC,EAChCC,GAAsB;IAHtB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,GAAG,GAAHA,GAAG;EACV;EAEHwC,QAAQA,CAAA;IACN,IAAI,CAAC1B,QAAQ,GAAG,CAAC;IACjB,IAAI,CAAC2B,SAAS,EAAE;IAChB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,SAAS,EAAE;IAEhB;IACA,IAAI,CAACZ,iBAAiB,GAAG,EAAE;EAC7B;EAEAS,SAASA,CAAA;IACP;IACA,IAAI,CAACR,SAAS,GAAG,IAAI,CAACpC,EAAE,CAACgD,KAAK,CAAC;MAC7BC,YAAY,EAAE,CAAC,EAAE,EAAE,CAACpD,UAAU,CAACqD,QAAQ,CAAC,CAAC;MAEzCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtD,UAAU,CAACqD,QAAQ,CAAC,CAAC,CAAE;KACpC,CAAC;IAEF;IACA,IAAI,CAACb,SAAS,GAAG,IAAI,CAACrC,EAAE,CAACgD,KAAK,CAAC;MAC7BI,MAAM,EAAE,CAAC,EAAE,EAAE,CAACvD,UAAU,CAACqD,QAAQ,CAAC,CAAC;MACnCG,MAAM,EAAE,CAAC,EAAE,EAAE,CAACxD,UAAU,CAACqD,QAAQ,CAAC,CAAC;MACnCI,eAAe,EAAE,CAAC,EAAE,EAAE,CAACzD,UAAU,CAACqD,QAAQ,EAAErD,UAAU,CAAC0D,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACvEC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC3D,UAAU,CAACqD,QAAQ,EAAErD,UAAU,CAAC4D,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACxEC,SAAS,EAAE,CAAC,EAAE,EAAE7D,UAAU,CAACqD,QAAQ,CAAC;MACpCS,UAAU,EAAE,CACV,EAAE,EACF,CAAC9D,UAAU,CAACqD,QAAQ,EAAErD,UAAU,CAAC4D,OAAO,CAAC,wBAAwB,CAAC,CAAC;KAEtE,CAAC;IAEF;IACA,IAAI,CAACnB,SAAS,GAAG,IAAI,CAACtC,EAAE,CAACgD,KAAK,CAAC;MAC7BY,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC/D,UAAU,CAAC0D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAChDM,UAAU,EAAE,CAAC,EAAE,EAAE,CAAChE,UAAU,CAAC0D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5CO,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjE,UAAU,CAACqD,QAAQ,CAAC,CAAC;MAClCa,QAAQ,EAAE,CACR,EAAE,EACF,CACElE,UAAU,CAACqD,QAAQ,EACnBrD,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,EACjBnE,UAAU,CAAC4D,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDQ,YAAY,EAAE,CAAC,EAAE,EAAE,CAACpE,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,EAAEnE,UAAU,CAAC4D,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;MACvES,UAAU,EAAE,CAAC,EAAE,EAAE,CAACrE,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,EAAEnE,UAAU,CAAC4D,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;MACrEU,aAAa,EAAE,CACb,EAAE,EACF,CACEtE,UAAU,CAACqD,QAAQ,EACnBrD,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,EACjBnE,UAAU,CAAC4D,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDW,iBAAiB,EAAE,CACjB,EAAE,EACF,CACEvE,UAAU,CAACqD,QAAQ,EACnBrD,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,EACjBnE,UAAU,CAAC4D,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDY,cAAc,EAAE,CACd,EAAE,EACF,CACExE,UAAU,CAACqD,QAAQ,EACnBrD,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,EACjBnE,UAAU,CAAC4D,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDa,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC1E,UAAU,CAACqD,QAAQ,CAAC,CAAC;MACjCsB,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC3E,UAAU,CAACqD,QAAQ,CAAC,CAAC;MAC1CuB,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC,EAAE,EAAE,CAACjF,UAAU,CAACqD,QAAQ,CAAC,CAAC;MAC3C6B,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,gBAAgB,EAAE,CAAC,EAAE,CAAC;MAAE;MACxBC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAE;KACpB,CAAC;IAEF;IACA,IAAI,CAAC1C,SAAS,GAAG,IAAI,CAACvC,EAAE,CAACgD,KAAK,CAAC;MAC7BkC,aAAa,EAAE,CAAC,EAAE,EAAErF,UAAU,CAACqD,QAAQ,CAAC;MACxCiC,0BAA0B,EAAE,CAAC,EAAE,EAAE,CAACtF,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDoB,uBAAuB,EAAE,CAAC,EAAE,EAAE,CAACvF,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClDqB,mBAAmB,EAAE,CAAC,EAAE,EAAE,CAACxF,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9CsB,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAACzF,UAAU,CAACmE,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC;IAEF;IACA,IAAI,CAACxB,SAAS,GAAG,IAAI,CAACxC,EAAE,CAACgD,KAAK,CAAC;MAC7BuC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,oBAAoB,EAAE,CAAC,EAAE;KAC1B,CAAC;IAEF;IACA,IAAI,CAACjD,SAAS,GAAG,IAAI,CAACzC,EAAE,CAACgD,KAAK,CAAC;MAC7B;IAAA,CACD,CAAC;EACJ;EAEA;EACA2C,cAAcA,CAAA;IACZ,QAAQ,IAAI,CAACtF,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC+B,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACC,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACC,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACC,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACC,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACC,SAAS;MACvB;QACE,OAAO,IAAI,CAACL,SAAS;IACzB;EACF;EAEAS,aAAaA,CAAA;IACX,IAAI,CAAC3C,eAAe,CAAC0F,YAAY,EAAE,CAACC,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC/D,YAAY,GAAGgE,MAAM,CAACC,OAAO,CAACF,QAAQ,CAACG,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACpF,GAAG,EAAEC,KAAK,CAAC,MAAM;UACvED,GAAG;UACHC,KAAK,EAAEA;SACR,CAAC,CAAC;QACH,IAAI,CAACkB,uBAAuB,GAAG,IAAI,CAACF,YAAY,CAAC,CAAC;QAClDoE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACrE,YAAY,CAAC;MACrD,CAAC;MACDsE,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEC,GAAG,CAAC;MAChD,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACrG,GAAG,CAACsG,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEA;EACAC,eAAeA,CAAA;IACb,MAAMzD,YAAY,GAAG,IAAI,CAACb,SAAS,CAACuE,GAAG,CAAC,cAAc,CAAC,EAAE3F,KAAK;IAE9D,IAAIiC,YAAY,KAAK,kBAAkB,EAAE;MACvC,IAAI,CAACd,iBAAiB,GAAG,IAAI,CAACF,wBAAwB;IACxD,CAAC,MAAM,IAAIgB,YAAY,KAAK,iBAAiB,EAAE;MAC7C,IAAI,CAACd,iBAAiB,GAAG,IAAI,CAACD,uBAAuB;IACvD,CAAC,MAAM;MACL,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC7B;IAEA;IACA,IAAI,CAACC,SAAS,CAACwE,UAAU,CAAC;MAAEzD,IAAI,EAAE;IAAE,CAAE,CAAC;IACvC,IAAI,CAAC1C,gBAAgB,GAAG,EAAE;IAE1B;IACA,IAAI,CAACN,GAAG,CAACsG,aAAa,EAAE;EAC1B;EAEA3D,UAAUA,CAAA;IACR,IAAI,CAACjC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACX,eAAe,CAAC2G,SAAS,EAAE,CAAChB,SAAS,CAAC;MACzCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,EAAE;UAC7B,IAAI,CAACxF,MAAM,GAAGqF,QAAQ,CAACG,IAAI;QAC7B,CAAC,MAAM;UACLE,OAAO,CAACU,IAAI,CAAC,4BAA4B,CAAC;UAC1C,IAAI,CAACpG,MAAM,GAAG,EAAE;QAClB;MACF,CAAC;MACD4F,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;MAC7C,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAC3F,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACV,GAAG,CAACsG,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEA1D,SAASA,CAACK,MAAe;IACvB,IAAI,CAAClD,eAAe,CAAC6G,QAAQ,CAAC3D,MAAM,CAAC,CAACyC,SAAS,CAAC;MAC9CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,EAAE;UAC7B,IAAI,CAACtF,KAAK,GAAGmF,QAAQ,CAACG,IAAI;QAC5B,CAAC,MAAM;UACLE,OAAO,CAACU,IAAI,CAAC,2BAA2B,CAAC;UACzC,IAAI,CAAClG,KAAK,GAAG,EAAE;QACjB;MACF,CAAC;MACD0F,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAAC3F,KAAK,GAAG,EAAE;MACjB,CAAC;MACD4F,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACrG,GAAG,CAACsG,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEA;EAEF;EACAO,eAAeA,CAAA;IACb,MAAM/D,YAAY,GAAG,IAAI,CAACb,SAAS,CAACuE,GAAG,CAAC,cAAc,CAAC,EAAE3F,KAAK;IAC9D,MAAMmC,IAAI,GAAG,IAAI,CAACf,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;IAE9C;IACA,IAAIiC,YAAY,KAAK,kBAAkB,KAAME,IAAI,KAAK,YAAY,IAAGA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAGA,IAAI,KAAK,OAAO,IAAGA,IAAI,KAAK,UAAU,CAAC,EAAE;MACjL,OAAO,CAAC,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAG,mBAAmB,EAAG,YAAY,EAAE,MAAM,EAAE,eAAe,EAAC,gBAAgB,EAAE,aAAa,EAAE,kBAAkB,EAAG,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAE;IACxT;IAEA,OAAO,EAAG;EACZ;EAEA;EACA8D,eAAeA,CAACC,SAAiB;IAC/B,OAAO,IAAI,CAACF,eAAe,EAAE,CAACG,QAAQ,CAACD,SAAS,CAAC;EACnD;EAGE;EACAE,UAAUA,CAAChE,MAAc,EAAEiE,QAAgB;IACzC,IAAI,CAAC/G,cAAc,GAAG8C,MAAM;IAC5B,IAAI,CAAC7C,gBAAgB,GAAG8G,QAAQ;IAChC,IAAI,CAAChF,SAAS,CAACuE,UAAU,CAAC;MACxBxD,MAAM,EAAEA;KACT,CAAC;IACF,IAAI,CAACL,SAAS,CAACK,MAAM,CAAC;EACxB;EAEAkE,cAAcA,CAACC,SAAiB;IAC9B,IAAI,CAAC9G,gBAAgB,GAAG8G,SAAS;IACjC,IAAI,CAACnF,SAAS,CAACwE,UAAU,CAAC;MACxBzD,IAAI,EAAEoE;KACP,CAAC;IAEF;IACA,MAAMC,mBAAmB,GAAG,CAC1B,YAAY,EACZ,UAAU,EACV,SAAS,EACT,YAAY,CACb;IACD,IAAI,CAACA,mBAAmB,CAACL,QAAQ,CAACI,SAAS,CAAC,EAAE;MAC5C,IAAI,CAACjF,SAAS,CAACsE,UAAU,CAAC;QACxBtC,UAAU,EAAE;OACb,CAAC;IACJ;IAEA;IACA,MAAMmD,uBAAuB,GAAG,CAC9B,mBAAmB,EACnB,eAAe,EACf,qCAAqC,EACrC,uBAAuB,EACvB,YAAY,CAEb;IACD,IAAI,CAACA,uBAAuB,CAACN,QAAQ,CAACI,SAAS,CAAC,EAAE;MAChD,IAAI,CAACjF,SAAS,CAACsE,UAAU,CAAC;QACxB3C,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE;OACb,CAAC;IACJ;IAEA;IACA,MAAMwD,qBAAqB,GAAG,CAC5B,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,CACtC;IACD,IAAI,CAACA,qBAAqB,CAACP,QAAQ,CAACI,SAAS,CAAC,EAAE;MAC9C,IAAI,CAACjF,SAAS,CAACsE,UAAU,CAAC;QACxB/B,QAAQ,EAAE;OACX,CAAC;IACJ;IAEA;IACA,MAAM8C,yBAAyB,GAAG,CAChC,eAAe,EACf,YAAY,EACZ,uBAAuB,EACvB,qCAAqC,CACtC;IACD,IAAI,CAACA,yBAAyB,CAACR,QAAQ,CAACI,SAAS,CAAC,EAAE;MAClD,IAAI,CAACjF,SAAS,CAACsE,UAAU,CAAC;QACxBjC,kBAAkB,EAAE;OACrB,CAAC;IACJ;IAEA;IACA,MAAMiD,uBAAuB,GAAG,CAAC,mBAAmB,CAAC;IACrD,IAAI,CAACA,uBAAuB,CAACT,QAAQ,CAACI,SAAS,CAAC,EAAE;MAChD,IAAI,CAACjF,SAAS,CAACsE,UAAU,CAAC;QACxBhC,UAAU,EAAE;OACb,CAAC;IACJ;IAEA;IACA,MAAMiD,4BAA4B,GAAG,CACnC,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,mBAAmB,EACnB,qCAAqC,CACtC;IACD,IAAI,CAACA,4BAA4B,CAACV,QAAQ,CAACI,SAAS,CAAC,EAAE;MACrD,IAAI,CAACjF,SAAS,CAACsE,UAAU,CAAC;QACxBnC,eAAe,EAAE;OAClB,CAAC;IACJ;IAEA;IACA,MAAMqD,yBAAyB,GAAG,CAChC,YAAY,EACZ,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,EACrC,sBAAsB,CACvB;IACD,IAAIA,yBAAyB,CAACX,QAAQ,CAACI,SAAS,CAAC,EAAE;MACjD,IAAI,CAACjF,SAAS,CAACsE,UAAU,CAAC;QACxBlC,gBAAgB,EAAE;OACnB,CAAC;IACJ;IAEA;IACA,MAAMqD,wBAAwB,GAAG,CAC/B,UAAU,EACV,YAAY,EACZ,UAAU,EACV,OAAO,CACR;IACD,IAAI,CAACA,wBAAwB,CAACZ,QAAQ,CAACI,SAAS,CAAC,EAAE;MACjD,IAAI,CAACjF,SAAS,CAACsE,UAAU,CAAC;QACxB3B,WAAW,EAAE;OACd,CAAC;IACJ;EACF;EAEA+C,UAAUA,CAAC3E,MAAc,EAAE4E,QAAgB;IACzC,IAAI,CAACzH,gBAAgB,GAAGyH,QAAQ;IAChC,IAAI,CAAC5F,SAAS,CAACuE,UAAU,CAAC;MACxBvD,MAAM,EAAEA;KACT,CAAC;EACJ;EAEA;EACA6E,gBAAgBA,CAAChB,SAAiB,EAAElG,KAAa;IAC/C,IAAI,CAACsB,SAAS,CAACsE,UAAU,CAAC;MACxB,CAACM,SAAS,GAAGlG;KACd,CAAC;EACJ;EAEA;EACAmH,gBAAgBA,CAACjB,SAAiB,EAAElG,KAAa;IAC/C,IAAI,CAACuB,SAAS,CAACqE,UAAU,CAAC;MACxB,CAACM,SAAS,GAAGlG;KACd,CAAC;IAEF;IACA,IAAIkG,SAAS,KAAK,eAAe,EAAE;MACjC,IAAI,CAACkB,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAA,gBAAgBA,CAAA;IACd,IAAI,CAAC7F,SAAS,CAACqE,UAAU,CAAC;MACxBvB,mBAAmB,EAAE,IAAI;MACzBC,gBAAgB,EAAE,IAAI;MACtBH,0BAA0B,EAAE,IAAI;MAChCC,uBAAuB,EAAE;KAC1B,CAAC;EACJ;EAEA;EACAiD,oBAAoBA,CAAA;IAClB,MAAMnD,aAAa,GAAG,IAAI,CAAC3C,SAAS,CAACoE,GAAG,CAAC,eAAe,CAAC,EAAE3F,KAAK;IAChE,OACEkE,aAAa,KAAK,MAAM,IACxBA,aAAa,KAAK,+BAA+B;EAErD;EAEA;EACAoD,2BAA2BA,CAAA;IACzB,MAAMpD,aAAa,GAAG,IAAI,CAAC3C,SAAS,CAACoE,GAAG,CAAC,eAAe,CAAC,EAAE3F,KAAK;IAChE,OACEkE,aAAa,KAAK,aAAa,IAC/BA,aAAa,KAAK,+BAA+B;EAErD;EAEA;EACAqD,yBAAyBA,CAAA;IACvB,MAAMC,QAAQ,GAAG,IAAI,CAACpG,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;IAClD,MAAMwG,mBAAmB,GAAG,CAC1B,YAAY,EACZ,UAAU,EACV,SAAS,EACT,YAAY,CACb;IACD,OAAOA,mBAAmB,CAACL,QAAQ,CAACqB,QAAQ,CAAC;EAC/C;EAEA;EACAC,oBAAoBA,CAAA;IAClB,MAAMD,QAAQ,GAAG,IAAI,CAACpG,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;IAClD,MAAMyG,uBAAuB,GAAG,CAC9B,mBAAmB,EACnB,eAAe,EACf,qCAAqC,EACrC,uBAAuB,EACvB,YAAY,CACb;IACD,OAAOA,uBAAuB,CAACN,QAAQ,CAACqB,QAAQ,CAAC;EACnD;EAEA;EACAE,iCAAiCA,CAAA;IAC/B,MAAMF,QAAQ,GAAG,IAAI,CAACpG,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;IAClD,MAAM2G,yBAAyB,GAAG,CAChC,eAAe,EACf,YAAY,EACZ,uBAAuB,EACvB,qCAAqC,CACtC;IACD,OAAOA,yBAAyB,CAACR,QAAQ,CAACqB,QAAQ,CAAC;EACrD;EAEA;EACAG,uBAAuBA,CAAA;IACrB,MAAMH,QAAQ,GAAG,IAAI,CAACpG,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;IAClD,MAAM0G,qBAAqB,GAAG,CAC5B,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,CACtC;IACD,OAAOA,qBAAqB,CAACP,QAAQ,CAACqB,QAAQ,CAAC;EACjD;EAEA;EACAI,yBAAyBA,CAAA;IACvB,MAAMJ,QAAQ,GAAG,IAAI,CAACpG,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;IAClD,MAAM4G,uBAAuB,GAAG,CAAC,mBAAmB,CAAC;IACrD,OAAOA,uBAAuB,CAACT,QAAQ,CAACqB,QAAQ,CAAC;EACnD;EAEA;EACAK,0BAA0BA,CAAA;IACxB,MAAML,QAAQ,GAAG,IAAI,CAACpG,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;IAClD,MAAM+G,wBAAwB,GAAG,CAC/B,UAAU,EACV,YAAY,EACZ,UAAU,EACV,OAAO,CACR;IACD,OAAOA,wBAAwB,CAACZ,QAAQ,CAACqB,QAAQ,CAAC;EACpD;EAEA;EACAM,8BAA8BA,CAAA;IAC5B,MAAMN,QAAQ,GAAG,IAAI,CAACpG,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;IAClD,MAAM6G,4BAA4B,GAAG,CACnC,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,mBAAmB,EACnB,qCAAqC,CACtC;IACD,OAAOA,4BAA4B,CAACV,QAAQ,CAACqB,QAAQ,CAAC;EACxD;EAEA;EACAO,+BAA+BA,CAAA;IAC7B,MAAMP,QAAQ,GAAG,IAAI,CAACpG,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;IAClD,MAAM8G,yBAAyB,GAAG,CAChC,YAAY,EACZ,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,EACrC,sBAAsB,CACvB;IACD,OAAO,CAACA,yBAAyB,CAACX,QAAQ,CAACqB,QAAQ,CAAC;EACtD;EAEA;EACAQ,gBAAgBA,CAAC9B,SAAiB,EAAElG,KAAa;IAC/CoF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MAAEa,SAAS;MAAElG;IAAK,CAAE,CAAC;IAE7D,IAAI,CAACoB,SAAS,CAACwE,UAAU,CAAC;MACxB,CAACM,SAAS,GAAGlG;KACd,CAAC;IAEF;IACA,IAAIkG,SAAS,KAAK,cAAc,EAAE;MAChC,IAAI,CAACR,eAAe,EAAE;IACxB;IAEA;IACA,IAAIQ,SAAS,KAAK,MAAM,EAAE;MACxB,IAAI,CAACzG,gBAAgB,GAAGO,KAAK;MAC7B,IAAI,CAACsG,cAAc,CAACtG,KAAK,CAAC,CAAC,CAAC;IAC9B;IAEAoF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACjE,SAAS,CAACpB,KAAK,CAAC;IAE7D;IACA,IAAI,CAACb,GAAG,CAACsG,aAAa,EAAE;EAC1B;EAEA;EACAwC,gBAAgBA,CAAC/B,SAAiB,EAAElG,KAAa;IAC/C,IAAI,CAACyB,SAAS,CAACmE,UAAU,CAAC;MACxB,CAACM,SAAS,GAAGlG;KACd,CAAC;EACJ;EAEAkI,UAAUA,CAACC,OAAiB;IAAA,IAAAC,KAAA;IAC1B,IAAI,IAAI,CAACC,kBAAkB,EAAE,EAAE;MAC7B,MAAMC,QAAQ,GAAiB;QAC7B,GAAG,IAAI,CAAClH,SAAS,CAACpB,KAAK;QACvB,GAAG,IAAI,CAACqB,SAAS,CAACrB,KAAK;QACvB,GAAG,IAAI,CAACsB,SAAS,CAACtB,KAAK;QACvB,GAAG,IAAI,CAACuB,SAAS,CAACvB,KAAK;QACvB,GAAG,IAAI,CAACyB,SAAS,CAACzB;OACnB;MACD;MACA,MAAMuI,YAAY,GAAG,IAAIC,QAAQ,EAAE;MAEnC;MACAxD,MAAM,CAACyD,IAAI,CAAC,IAAI,CAACrH,SAAS,CAACpB,KAAK,CAAC,CAAC0I,OAAO,CAAE3I,GAAG,IAAI;QAChDwI,YAAY,CAACI,MAAM,CAAC5I,GAAG,EAAE,IAAI,CAACqB,SAAS,CAACpB,KAAK,CAACD,GAAG,CAAC,CAAC;MACrD,CAAC,CAAC;MAEF;MACAiF,MAAM,CAACyD,IAAI,CAAC,IAAI,CAACpH,SAAS,CAACrB,KAAK,CAAC,CAAC0I,OAAO,CAAE3I,GAAG,IAAI;QAChDwI,YAAY,CAACI,MAAM,CAAC5I,GAAG,EAAE,IAAI,CAACsB,SAAS,CAACrB,KAAK,CAACD,GAAG,CAAC,CAAC;MACrD,CAAC,CAAC;MAEF;MACA,MAAM6I,uBAAuB,GAAG,CAAC,gBAAgB,CAAC;MAElD;MACA,MAAMpB,QAAQ,GAAG,IAAI,CAACpG,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;MAElD;MACA,MAAMyG,uBAAuB,GAAG,CAC9B,mBAAmB,EACnB,eAAe,EACf,qCAAqC,EACrC,uBAAuB,EACvB,YAAY,CACb;MACD,IAAIA,uBAAuB,CAACN,QAAQ,CAACqB,QAAQ,CAAC,EAAE;QAC9CoB,uBAAuB,CAACC,IAAI,CAAC,YAAY,CAAC;QAC1CD,uBAAuB,CAACC,IAAI,CAAC,cAAc,CAAC;MAC9C;MAEA;MACA,MAAMnC,qBAAqB,GAAG,CAC5B,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,CACtC;MACD,IAAIA,qBAAqB,CAACP,QAAQ,CAACqB,QAAQ,CAAC,EAAE;QAC5CoB,uBAAuB,CAACC,IAAI,CAAC,UAAU,CAAC;MAC1C;MAEA;MACA,MAAMlC,yBAAyB,GAAG,CAChC,eAAe,EACf,YAAY,EACZ,uBAAuB,EACvB,qCAAqC,CACtC;MACD,IAAIA,yBAAyB,CAACR,QAAQ,CAACqB,QAAQ,CAAC,EAAE;QAChDoB,uBAAuB,CAACC,IAAI,CAAC,oBAAoB,CAAC;MACpD;MAEA;MACA,MAAMjC,uBAAuB,GAAG,CAAC,mBAAmB,CAAC;MACrD,IAAIA,uBAAuB,CAACT,QAAQ,CAACqB,QAAQ,CAAC,EAAE;QAC9CoB,uBAAuB,CAACC,IAAI,CAAC,YAAY,CAAC;MAC5C;MAEA;MACA,MAAMhC,4BAA4B,GAAG,CACnC,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,mBAAmB,EACnB,qCAAqC,CACtC;MACD,IAAIA,4BAA4B,CAACV,QAAQ,CAACqB,QAAQ,CAAC,EAAE;QACnDoB,uBAAuB,CAACC,IAAI,CAAC,iBAAiB,CAAC;MACjD;MAEA;MACA,MAAM/B,yBAAyB,GAAG,CAChC,YAAY,EACZ,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,EACrC,sBAAsB,CACvB;MACD,IAAI,CAACA,yBAAyB,CAACX,QAAQ,CAACqB,QAAQ,CAAC,EAAE;QACjDoB,uBAAuB,CAACC,IAAI,CAAC,kBAAkB,CAAC;MAClD;MAEA;MACA,MAAMrC,mBAAmB,GAAG,CAC1B,YAAY,EACZ,UAAU,EACV,SAAS,EACT,YAAY,CACb;MACD,IAAIA,mBAAmB,CAACL,QAAQ,CAACqB,QAAQ,CAAC,EAAE;QAC1CoB,uBAAuB,CAACC,IAAI,CAAC,YAAY,CAAC;MAC5C;MAEA;MACA7D,MAAM,CAACyD,IAAI,CAAC,IAAI,CAACnH,SAAS,CAACtB,KAAK,CAAC,CAAC0I,OAAO,CAAE3I,GAAG,IAAI;QAChD,IACEA,GAAG,KAAK,kBAAkB,IAC1B,CAAC6I,uBAAuB,CAACzC,QAAQ,CAACpG,GAAG,CAAC,EACtC;UACAwI,YAAY,CAACI,MAAM,CAAC5I,GAAG,EAAE,IAAI,CAACuB,SAAS,CAACtB,KAAK,CAACD,GAAG,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MAEF;MACA,MAAMmE,aAAa,GAAG,IAAI,CAAC3C,SAAS,CAACoE,GAAG,CAAC,eAAe,CAAC,EAAE3F,KAAK;MAEhE;MACAuI,YAAY,CAACI,MAAM,CAAC,eAAe,EAAEzE,aAAa,CAAC;MAEnD;MACA,IAAIA,aAAa,KAAK,MAAM,EAAE;QAC5B;QACA,MAAMG,mBAAmB,GAAG,IAAI,CAAC9C,SAAS,CAACoE,GAAG,CAC5C,qBAAqB,CACtB,EAAE3F,KAAK;QACR,MAAMsE,gBAAgB,GAAG,IAAI,CAAC/C,SAAS,CAACoE,GAAG,CAAC,kBAAkB,CAAC,EAAE3F,KAAK;QAEtE,IAAIqE,mBAAmB,EAAE;UACvBkE,YAAY,CAACI,MAAM,CAAC,qBAAqB,EAAEtE,mBAAmB,CAAC;QACjE;QACA,IAAIC,gBAAgB,EAAE;UACpBiE,YAAY,CAACI,MAAM,CAAC,kBAAkB,EAAErE,gBAAgB,CAAC;QAC3D;MACF,CAAC,MAAM,IAAIJ,aAAa,KAAK,aAAa,EAAE;QAC1C;QACA,MAAMC,0BAA0B,GAAG,IAAI,CAAC5C,SAAS,CAACoE,GAAG,CACnD,4BAA4B,CAC7B,EAAE3F,KAAK;QACR,MAAMoE,uBAAuB,GAAG,IAAI,CAAC7C,SAAS,CAACoE,GAAG,CAChD,yBAAyB,CAC1B,EAAE3F,KAAK;QAER,IAAImE,0BAA0B,EAAE;UAC9BoE,YAAY,CAACI,MAAM,CACjB,4BAA4B,EAC5BxE,0BAA0B,CAC3B;QACH;QACA,IAAIC,uBAAuB,EAAE;UAC3BmE,YAAY,CAACI,MAAM,CACjB,yBAAyB,EACzBvE,uBAAuB,CACxB;QACH;MACF,CAAC,MAAM,IAAIF,aAAa,KAAK,+BAA+B,EAAE;QAC5D;QACA,MAAMG,mBAAmB,GAAG,IAAI,CAAC9C,SAAS,CAACoE,GAAG,CAC5C,qBAAqB,CACtB,EAAE3F,KAAK;QACR,MAAMsE,gBAAgB,GAAG,IAAI,CAAC/C,SAAS,CAACoE,GAAG,CAAC,kBAAkB,CAAC,EAAE3F,KAAK;QACtE,MAAMmE,0BAA0B,GAAG,IAAI,CAAC5C,SAAS,CAACoE,GAAG,CACnD,4BAA4B,CAC7B,EAAE3F,KAAK;QACR,MAAMoE,uBAAuB,GAAG,IAAI,CAAC7C,SAAS,CAACoE,GAAG,CAChD,yBAAyB,CAC1B,EAAE3F,KAAK;QAER,IAAIqE,mBAAmB,EAAE;UACvBkE,YAAY,CAACI,MAAM,CAAC,qBAAqB,EAAEtE,mBAAmB,CAAC;QACjE;QACA,IAAIC,gBAAgB,EAAE;UACpBiE,YAAY,CAACI,MAAM,CAAC,kBAAkB,EAAErE,gBAAgB,CAAC;QAC3D;QACA,IAAIH,0BAA0B,EAAE;UAC9BoE,YAAY,CAACI,MAAM,CACjB,4BAA4B,EAC5BxE,0BAA0B,CAC3B;QACH;QACA,IAAIC,uBAAuB,EAAE;UAC3BmE,YAAY,CAACI,MAAM,CACjB,yBAAyB,EACzBvE,uBAAuB,CACxB;QACH;MACF;MAEA;MACAY,MAAM,CAACyD,IAAI,CAAC,IAAI,CAAChH,SAAS,CAACzB,KAAK,CAAC,CAAC0I,OAAO,CAAE3I,GAAG,IAAI;QAChD,IAAIA,GAAG,KAAK,aAAa,EAAE;UACzBwI,YAAY,CAACI,MAAM,CAAC5I,GAAG,EAAE,IAAI,CAAC0B,SAAS,CAACzB,KAAK,CAACD,GAAG,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MAEF;MACA,MAAM+I,iBAAiB,GAAQ,EAAE;MAEjC;MACAF,uBAAuB,CAACF,OAAO,CAAEK,KAAK,IAAI;QACxC,MAAM/I,KAAK,GAAG,IAAI,CAACsB,SAAS,CAACqE,GAAG,CAACoD,KAAK,CAAC,EAAE/I,KAAK;QAC9C,IAAIA,KAAK,EAAE;UACT8I,iBAAiB,CAACC,KAAK,CAAC,GAAG/I,KAAK;QAClC;MACF,CAAC,CAAC;MAEF;MACA,MAAM+G,wBAAwB,GAAG,CAC/B,UAAU,EACV,YAAY,EACZ,UAAU,EACV,OAAO,CACR;MACD,IAAIA,wBAAwB,CAACZ,QAAQ,CAACqB,QAAQ,CAAC,EAAE;QAC/C,MAAMvD,WAAW,GAAG,IAAI,CAACxC,SAAS,CAACkE,GAAG,CAAC,aAAa,CAAC,EAAE3F,KAAK;QAC5D,IAAIiE,WAAW,EAAE;UACf6E,iBAAiB,CAAC,aAAa,CAAC,GAAG7E,WAAW;QAChD;MACF;MAEA;MACAe,MAAM,CAACyD,IAAI,CAACK,iBAAiB,CAAC,CAACJ,OAAO,CAAE3I,GAAG,IAAI;QAC7CwI,YAAY,CAACI,MAAM,CACjB,qBAAqB5I,GAAG,GAAG,EAC3B+I,iBAAiB,CAAC/I,GAAG,CAAC,CACvB;MACH,CAAC,CAAC;MAEF;MACA,MAAMiJ,UAAU,GAAG,CACjB,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,sBAAsB,CACvB;MACDA,UAAU,CAACN,OAAO,CAAEK,KAAK,IAAI;QAC3B,MAAME,KAAK,GAAG,IAAI,CAACzH,SAAS,CAACmE,GAAG,CAACoD,KAAK,CAAC,EAAE/I,KAAK;QAC9C,IAAIiJ,KAAK,IAAIA,KAAK,CAACC,MAAM,EAAE;UACzB,MAAMC,UAAU,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAChD,QAAQ,CAAC4C,KAAK,CAAC;UAEvD,IAAII,UAAU,EAAE;YACdF,KAAK,CAACP,OAAO,CAAEU,IAAU,IAAI;cAC3Bb,YAAY,CAACI,MAAM,CAAC,GAAGI,KAAK,IAAI,EAAEK,IAAI,CAAC;YACzC,CAAC,CAAC;UACJ,CAAC,MAAM;YACLb,YAAY,CAACI,MAAM,CAACI,KAAK,EAAEE,KAAK,CAAC,CAAC,CAAC,CAAC;UACtC;QACF;MACF,CAAC,CAAC;MAEF;MACA,MAAMI,cAAc,GAAG,IAAI,CAAC/H,SAAS,CAACqE,GAAG,CAAC,kBAAkB,CAAC,EAAE3F,KAAK;MACpE,MAAMsJ,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,GAClDA,cAAc,GACd,EAAE;MAEN;MACAC,gBAAgB,CAACZ,OAAO,CAAC,CAACe,SAAS,EAAEC,KAAK,KAAI;QAC5CnB,YAAY,CAACI,MAAM,CAAC,oBAAoBe,KAAK,GAAG,EAAED,SAAS,CAAC;MAC9D,CAAC,CAAC;MAEFlB,YAAY,CAACI,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC1I,QAAQ,CAAC0J,QAAQ,EAAE,CAAC;MAEzD;MACA,IAAGxB,OAAO,EAAC;QACTI,YAAY,CAACI,MAAM,CAAC,iBAAiB,EAAE,GAAG,CAAC;MAC7C;MAEA;MACA,MAAMiB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC;MACrD,IAAIF,MAAM,EAAE;QACVA,MAAM,CAACG,SAAS,CAACC,GAAG,CAAC,aAAa,CAAC;MACrC;MAEA,IAAI,CAAC9K,eAAe,CAAC+K,cAAc,CAAC1B,YAAY,CAAC,CAAC1D,SAAS,CAAC;QAC1DC,IAAI;UAAA,IAAAoF,IAAA,GAAAC,iBAAA,CAAE,WAAOpF,QAAQ,EAAI;YACvBK,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEN,QAAQ,CAAC;YACjD,MAAMjG,IAAI,CAACsL,IAAI,CAAC,0BAA0B,EAAE,EAAE,EAAErF,QAAQ,CAACsF,MAAM,CAAC;YAEhEjC,KAAI,CAACnJ,MAAM,CAACqL,QAAQ,CAAC,CAAC,2BAA2B,CAAC,EAAE;cAClDC,WAAW,EAAE;gBAAEC,OAAO,EAAE;cAAK;aAC9B,CAAC;UACJ,CAAC;UAAA,gBAPD1F,IAAIA,CAAA2F,EAAA;YAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;UAAA;QAAA,GAOH;QACDrF,KAAK,EAAGC,GAAG,IAAI;UACbH,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEC,GAAG,CAAC;UAC9CzG,IAAI,CAACsL,IAAI,CAAC7E,GAAG,CAACqF,OAAO,EAAE,EAAE,EAAErF,GAAG,CAAC8E,MAAM,CAAC;UAEtC;UACA,IAAIT,MAAM,EAAE;YACVA,MAAM,CAACG,SAAS,CAACc,MAAM,CAAC,aAAa,CAAC;UACxC;QACF,CAAC;QACDrF,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACrG,GAAG,CAACsG,aAAa,EAAE;UACxB;UACA,IAAImE,MAAM,EAAE;YACVA,MAAM,CAACG,SAAS,CAACc,MAAM,CAAC,aAAa,CAAC;UACxC;QACF;OACD,CAAC;IACJ;EACF;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAAC7L,MAAM,CAACqL,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;EACrD;EAEAS,YAAYA,CAACC,KAAU,EAAE9E,SAAiB;IACxC,IAAI8E,KAAK,CAACC,MAAM,CAAChC,KAAK,IAAI+B,KAAK,CAACC,MAAM,CAAChC,KAAK,CAACC,MAAM,EAAE;MACnD,MAAMD,KAAK,GAAGM,KAAK,CAAC2B,IAAI,CAACF,KAAK,CAACC,MAAM,CAAChC,KAAK,CAAC;MAC5C,IAAI,CAACzH,SAAS,CAACoE,UAAU,CAAC;QACxB,CAACM,SAAS,GAAG+C;OACd,CAAC;MAEF7D,OAAO,CAACC,GAAG,CAAC,GAAGa,SAAS,KAAK+C,KAAK,CAACC,MAAM,iBAAiB,CAAC;IAC7D;EACF;EAEAiC,YAAYA,CAACjF,SAAiB;IAC5B,MAAM+C,KAAK,GAAG,IAAI,CAACzH,SAAS,CAACmE,GAAG,CAACO,SAAS,CAAC,EAAElG,KAAK;IAClD,OAAOiJ,KAAK,IAAIM,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,GAAGA,KAAK,CAACC,MAAM,GAAG,CAAC;EACzD;EAEA;EACAb,kBAAkBA,CAAA;IAChB,MAAM+C,WAAW,GAAG,IAAI,CAACzG,cAAc,EAAE;IAEzC;IACA,IAAI,IAAI,CAACtF,WAAW,KAAK,CAAC,EAAE;MAC1B,MAAM4C,YAAY,GAAG,IAAI,CAACb,SAAS,CAACuE,GAAG,CAAC,cAAc,CAAC,EAAE3F,KAAK;MAC9D,MAAMwH,QAAQ,GAAG,IAAI,CAACpG,SAAS,CAACuE,GAAG,CAAC,MAAM,CAAC,EAAE3F,KAAK;MAClD,MAAMqL,OAAO,GAAG,CAAC,EAAEpJ,YAAY,IAAIuF,QAAQ,CAAC;MAC5CpC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;QAAEpD,YAAY;QAAEuF,QAAQ;QAAE6D;MAAO,CAAE,CAAC;MACtE,OAAOA,OAAO;IAChB;IAEA;IACA,IAAI,IAAI,CAAChM,WAAW,KAAK,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACiM,gBAAgB,EAAE;IAChC;IAEA,OAAOF,WAAW,CAACG,KAAK;EAC1B;EAEA;EACAD,gBAAgBA,CAAA;IACd,MAAME,IAAI,GAAG,IAAI,CAAClK,SAAS;IAC3B,MAAMmK,YAAY,GAAG,IAAI,CAACzF,eAAe,EAAE;IAE3C;IACA,MAAM0F,cAAc,GAAG,CAAC,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,CAAC;IAEpH;IAEA,KAAK,MAAMxF,SAAS,IAAIwF,cAAc,EAAE;MACtC,IAAID,YAAY,CAACtF,QAAQ,CAACD,SAAS,CAAC,EAAE;QACpC,MAAMyF,OAAO,GAAGH,IAAI,CAAC7F,GAAG,CAACO,SAAS,CAAC;QACnC,IAAI,CAACyF,OAAO,IAAIA,OAAO,CAACC,OAAO,EAAE;UAC/BxG,OAAO,CAACC,GAAG,CAAC,uCAAuCa,SAAS,EAAE,EAAEyF,OAAO,EAAEE,MAAM,CAAC;UAChF,OAAO,KAAK;QACd;MACF;IACF;IAEA;IACA,MAAMC,iBAAiB,GAAG,CAAC,YAAY,EAAE,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,YAAY,EAAE,UAAU,CAAC;IAE9I,KAAK,MAAM5F,SAAS,IAAI4F,iBAAiB,EAAE;MACzC,IAAIL,YAAY,CAACtF,QAAQ,CAACD,SAAS,CAAC,IAAI,IAAI,CAAC6F,0BAA0B,CAAC7F,SAAS,CAAC,EAAE;QAClF,MAAMyF,OAAO,GAAGH,IAAI,CAAC7F,GAAG,CAACO,SAAS,CAAC;QACnC,IAAI,CAACyF,OAAO,IAAIA,OAAO,CAACC,OAAO,EAAE;UAC/BxG,OAAO,CAACC,GAAG,CAAC,mDAAmDa,SAAS,EAAE,EAAEyF,OAAO,EAAEE,MAAM,CAAC;UAC5F,OAAO,KAAK;QACd;MACF;IACF;IAEAzG,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC,OAAO,IAAI;EACb;EAEA;EACA0G,0BAA0BA,CAAC7F,SAAiB;IAC1C,QAAQA,SAAS;MACf,KAAK,YAAY;QACf,OAAO,IAAI,CAACqB,yBAAyB,EAAE;MACzC,KAAK,aAAa;QAChB,OAAO,IAAI,CAACM,0BAA0B,EAAE;MAC1C,KAAK,iBAAiB;QACpB,OAAO,IAAI,CAACC,8BAA8B,EAAE;MAC9C,KAAK,kBAAkB;QACrB,OAAO,IAAI,CAACC,+BAA+B,EAAE;MAC/C,KAAK,oBAAoB;QACvB,OAAO,IAAI,CAACL,iCAAiC,EAAE;MACjD,KAAK,YAAY;QACf,OAAO,IAAI,CAACE,yBAAyB,EAAE;MACzC,KAAK,UAAU;QACb,OAAO,IAAI,CAACD,uBAAuB,EAAE;MACvC;QACE,OAAO,KAAK;IAChB;EACF;EAEA;EACAqE,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC3M,WAAW,GAAG,IAAI,CAACD,UAAU,EAAE;MACtC,IAAI,CAACC,WAAW,EAAE;IACpB;EACF;EAEA;EACA4M,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC5M,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA6M,iBAAiBA,CAACnM,GAAW;IAC3B,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAO,EAAE;IAE9C,OAAOA,GAAG,CACPoM,KAAK,CAAC,GAAG,CAAC,CACVhH,GAAG,CAAEiH,IAAI,IACRA,IAAI,CAACC,IAAI,EAAE,GAAGD,IAAI,CAAC,CAAC,CAAC,CAACE,WAAW,EAAE,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAG,EAAE,CACvE,CACAC,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,eAAeA,CAAC1M,KAAa;IAC3B,MAAM0J,KAAK,GAAG,IAAI,CAAC9I,mBAAmB,CAAC+L,OAAO,CAAC3M,KAAK,CAAC;IAErD,IAAI0J,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC9I,mBAAmB,CAACgM,MAAM,CAAClD,KAAK,EAAE,CAAC,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAAC9I,mBAAmB,CAACiI,IAAI,CAAC7I,KAAK,CAAC;IACtC;IAEA;IACA,IAAI,CAACsB,SAAS,CAACsE,UAAU,CAAC;MACxB5B,gBAAgB,EAAE,CAAC,GAAG,IAAI,CAACpD,mBAAmB;KAC/C,CAAC;EACJ;EAEA;EACAiM,sBAAsBA,CAAC7B,KAAU;IAC/B,IAAIA,KAAK,CAACC,MAAM,CAAC6B,OAAO,EAAE;MACxB;MACA,IAAI,CAAClM,mBAAmB,GAAG,IAAI,CAACD,qBAAqB,CAACwE,GAAG,CAAE4H,CAAC,IAAKA,CAAC,CAAC/M,KAAK,CAAC;IAC3E,CAAC,MAAM;MACL;MACA,IAAI,CAACY,mBAAmB,GAAG,EAAE;IAC/B;IAEA,IAAI,CAACU,SAAS,CAACsE,UAAU,CAAC;MACxB5B,gBAAgB,EAAE,CAAC,GAAG,IAAI,CAACpD,mBAAmB;KAC/C,CAAC;EACJ;EAEAoM,mBAAmBA,CAAChN,KAAa;IAC/B,OAAO,IAAI,CAACY,mBAAmB,CAACuF,QAAQ,CAACnG,KAAK,CAAC;EACjD;EAEAiN,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACrM,mBAAmB,CAACsI,MAAM,KAAK,CAAC,EAAE;MACzC,OAAO,EAAE;IACX;IAEA,IAAI,IAAI,CAACtI,mBAAmB,CAACsI,MAAM,KAAK,CAAC,EAAE;MACzC,MAAMO,SAAS,GAAG,IAAI,CAAC9I,qBAAqB,CAACuM,IAAI,CAC9CH,CAAC,IAAKA,CAAC,CAAC/M,KAAK,KAAK,IAAI,CAACY,mBAAmB,CAAC,CAAC,CAAC,CAC/C;MACD,OAAO6I,SAAS,GAAGA,SAAS,CAAC1J,GAAG,GAAG,EAAE;IACvC;IAEA,OAAO,GAAG,IAAI,CAACa,mBAAmB,CAACsI,MAAM,uBAAuB;EAClE;EAEA;EACAiE,mBAAmBA,CAACnN,KAAa;IAC/B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IACrB,MAAMoN,MAAM,GAAG,IAAI,CAACrM,eAAe,CAACmM,IAAI,CAACG,GAAG,IAAIA,GAAG,CAACrN,KAAK,KAAKA,KAAK,CAAC;IACpE,OAAOoN,MAAM,GAAGA,MAAM,CAACrN,GAAG,GAAG,EAAE;EACjC;EAIA;EACAuN,eAAeA,CAACtN,KAAa;IAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IACrB,MAAMwH,QAAQ,GAAG,IAAI,CAACrG,iBAAiB,CAAC+L,IAAI,CAACK,IAAI,IAAIA,IAAI,CAACvN,KAAK,KAAKA,KAAK,CAAC;IAC1E,OAAOwH,QAAQ,GAAGA,QAAQ,CAACzH,GAAG,GAAG,EAAE;EACrC;CACD;AAvsCYhB,oBAAoB,GAAAyO,UAAA,EALhC5O,SAAS,CAAC;EACT6O,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,QAAQ,EAAE;CACX,CAAC,C,EACW5O,oBAAoB,CAusChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}