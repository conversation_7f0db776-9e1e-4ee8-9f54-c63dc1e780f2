<div class="mb-5 mt-0">
  <app-broker-title></app-broker-title>
</div>

<div class="card mb-5 mb-xl-10">
  <div class="card-body pt-5 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-3">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
          <div class="d-flex flex-column my-4">
            <div class="d-flex align-items-center mb-2">
              <span class="text-gray-800 fs-2 fw-bolder me-1">{{ request?.title || 'Loading...' }}</span>
            </div>

            <div class="d-flex flex-wrap fw-bold fs-5 mb-4 pe-2">
              <span class="d-flex align-items-center text-gray-600 me-5 mb-2">
                <i class="fa-solid fa-user me-1 text-mid-blue"></i>
                {{ request?.user?.name || 'N/A' }}
              </span>
              <span class="d-flex align-items-center text-gray-600 me-5 mb-2">
                <i class="fa-solid fa-calendar me-1 text-mid-blue"></i>
                {{ request?.createdAt || 'N/A' }}
              </span>
            </div>
          </div>

          <div class="d-flex my-4">
            <span class="badge badge-light-dark-blue px-3 py-3 me-3 fw-bold fs-6">
              {{ request?.status || 'N/A' }}
            </span>
            <!--broker actions-->
            <div *ngIf="user?.role == 'broker'">
              <div *ngIf="request?.status == 'new'">
                <a class="btn btn-sm btn-mid-blue me-3 cursor-pointer fw-bold fs-6" (click)="updateRequestStatus(request.id, userId, 'in_processing')">
                  <i class="fa-solid fa-play"></i>
                  Start Processing
                </a>
              </div>
            </div>
            <div *ngIf="user?.role == 'broker'">
              <a class="btn btn-sm btn-danger me-3 cursor-pointer fw-bold fs-6" (click)="archiveRequest(request.id, brokerId)">
                <i class="fa-regular fa-eye-slash"></i>
                Archive
              </a>
            </div>
            <!--client actions-->
            <div *ngIf="user?.role == 'client'">
              <div *ngIf="request?.status != 'finished'">
                <a class="btn btn-sm btn-mid-blue me-3 cursor-pointer fw-bold fs-6" (click)="updateRequestStatus(request.id, userId, 'finished')">
                  <i class="fa-solid fa-close"></i>
                  Finish Request
                </a>
              </div>
            </div>
          </div>
        </div>

        <div class="d-flex flex-wrap flex-stack">
          <div class="d-flex flex-column flex-grow-1 pe-8">
            <div class="d-flex flex-wrap">
              <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                <div class="d-flex align-items-center">
                  <div class="fs-2 fw-bolder">
                    <i class="fa-solid fa-reply me-1 text-mid-blue"></i>
                    {{ request?.numberOfReplies || 0 }}
                  </div>
                </div>
                <div class="fw-bold fs-6 text-gray-500">Replies</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="d-flex overflow-auto mb-5">
      <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder flex-nowrap">
        <li class="nav-item">
          <a
            class="nav-link me-6 btn btn-active-dark-blue btn-light-primary"
            routerLink="overview"
            routerLinkActive="active"
          >
            Overview
          </a>
        </li>
        <li class="nav-item" *ngIf="canReply">
          <a
            class="nav-link me-6 btn btn-active-dark-blue btn-light-primary"
            routerLink="units-recommendation"
            routerLinkActive="active"
          >
            Units Recommendation
          </a>
        </li>
        <li class="nav-item">
          <a
            class="nav-link me-6 btn btn-active-dark-blue btn-light-primary"
            [routerLink]="['history']"
            [queryParams]="{ requestUserId: request?.user?.id }"
            routerLinkActive="active"
          >
            History
          </a>
        </li>
        <li class="nav-item">
          <a
            class="nav-link me-6 btn btn-active-dark-blue btn-light-primary"
            routerLink="replies"
            routerLinkActive="active"
          >
            Replies
          </a>
        </li>
      </ul>
    </div>

    <div class="card-body pt-3 pb-0 px-0">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
