{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/units.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../../developer-dashboard/components/header/header.component\";\nfunction UnitDetailsComponent_div_11_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 49);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const item_r2 = ctx_r0.$implicit;\n    const i_r3 = ctx_r0.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r2.url || \"assets/media/auth/404-error.png\", i0.ɵɵsanitizeUrl)(\"alt\", \"Unit Image \" + (i_r3 + 1));\n  }\n}\nfunction UnitDetailsComponent_div_11_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"video\", 50);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r2.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UnitDetailsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, UnitDetailsComponent_div_11_ng_container_1_Template, 2, 2, \"ng-container\", 29)(2, UnitDetailsComponent_div_11_ng_container_2_Template, 2, 1, \"ng-container\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r3 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.type === \"video\");\n  }\n}\nfunction UnitDetailsComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"button\", 51);\n  }\n  if (rf & 2) {\n    const i_r4 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r4 === 0);\n    i0.ɵɵattribute(\"data-bs-slide-to\", i_r4);\n  }\n}\nfunction UnitDetailsComponent_span_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"It spans an area of \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \".\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(4, 1, ctx_r4.unitDetails == null ? null : ctx_r4.unitDetails.unitArea, \"1.0-0\"), \" m\\u00B2\");\n  }\n}\nfunction UnitDetailsComponent_span_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"The price per meter in cash is \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \",\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(4, 1, ctx_r4.unitDetails == null ? null : ctx_r4.unitDetails.pricePerMeterInCash, \"1.0-0\"), \" EGP\");\n  }\n}\nfunction UnitDetailsComponent_span_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" while the price per meter in installments is \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \".\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(4, 1, ctx_r4.unitDetails == null ? null : ctx_r4.unitDetails.pricePerMeterInInstallment, \"1.0-0\"), \" EGP\");\n  }\n}\nfunction UnitDetailsComponent_span_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"The total cash price is \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \",\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(4, 1, ctx_r4.unitDetails == null ? null : ctx_r4.unitDetails.totalPriceInCash, \"1.0-0\"), \" EGP\");\n  }\n}\nfunction UnitDetailsComponent_span_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" and the total installment price is \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \".\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(4, 1, ctx_r4.unitDetails == null ? null : ctx_r4.unitDetails.totalPriceInInstallment, \"1.0-0\"), \" EGP\");\n  }\n}\nfunction UnitDetailsComponent_div_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵelement(2, \"input\", 54);\n    i0.ɵɵelementStart(3, \"label\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const feature_r6 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r4.isFeatureEnabled(feature_r6.value));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r6.name);\n  }\n}\nexport class UnitDetailsComponent {\n  route;\n  router;\n  unitsService;\n  unitId = null;\n  unitDetails;\n  isLoading = false;\n  // Carousel properties\n  AllImages = [];\n  isAutoRotating = true;\n  // Features mapping\n  features = [{\n    name: 'Garage',\n    value: 'garage'\n  }, {\n    name: 'Clubhouse',\n    value: 'clubhouse'\n  }, {\n    name: 'Club',\n    value: 'club'\n  }, {\n    name: 'Storage',\n    value: 'storage'\n  }, {\n    name: 'Elevator',\n    value: 'elevator'\n  }, {\n    name: 'Swimming Pool',\n    value: 'swimming_pool'\n  }];\n  constructor(route, router, unitsService) {\n    this.route = route;\n    this.router = router;\n    this.unitsService = unitsService;\n  }\n  ngOnInit() {\n    this.route.queryParams.subscribe(params => {\n      if (params['unitId']) {\n        this.unitId = +params['unitId'];\n      }\n    });\n    this.loadUnitDetails();\n  }\n  loadUnitDetails() {\n    if (this.unitId) {\n      this.isLoading = true;\n      this.unitsService.getUnitById(this.unitId).subscribe({\n        next: response => {\n          console.log(response.data);\n          this.unitDetails = response.data;\n          console.log(this.unitDetails);\n          this.concate(this.unitDetails);\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading unit details:', error);\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  concate(details) {\n    // Add gallery items if they exist\n    if (details && details.gallery && details.gallery.length > 0) {\n      this.AllImages = [...details.gallery];\n    }\n    // Add diagram if it exists\n    if (details && details.diagram) {\n      this.AllImages.push({\n        type: 'image',\n        url: details.diagram\n      });\n    }\n    // Add location in master plan if it exists\n    if (details && details.locationInMasterPlan) {\n      this.AllImages.push({\n        type: 'image',\n        url: details.locationInMasterPlan\n      });\n    }\n  }\n  isFeatureEnabled(featureValue) {\n    return this.unitDetails?.otherAccessories?.includes(featureValue) || false;\n  }\n  goBack() {\n    this.router.navigate(['/developer/projects/models/units']);\n  }\n  static ɵfac = function UnitDetailsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UnitDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.UnitsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UnitDetailsComponent,\n    selectors: [[\"app-unit-details\"]],\n    decls: 114,\n    vars: 30,\n    consts: [[1, \"mb-5\", \"mt-0\"], [3, \"title\", \"subtitle\"], [1, \"row\"], [1, \"col-lg-8\"], [1, \"card\", \"mb-5\"], [1, \"card-body\", \"p-0\"], [1, \"position-relative\", \"unit-image-container\"], [1, \"unit-image-background\"], [1, \"h-100\"], [\"id\", \"unitImagesCarousel\", \"data-bs-ride\", \"carousel\", \"data-bs-interval\", \"3000\", 1, \"carousel\", \"slide\", \"h-100\"], [1, \"carousel-inner\", \"h-100\"], [\"class\", \"carousel-item h-100\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"carousel-indicators\"], [\"type\", \"button\", \"data-bs-target\", \"#unitImagesCarousel\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"position-absolute\", \"top-0\", \"start-0\", \"m-3\"], [1, \"badge\", \"bg-pink\", \"text-white\", \"fs-7\", \"px-3\", \"py-2\", \"rounded-pill\"], [1, \"position-absolute\", \"bottom-0\", \"start-0\", \"end-0\", \"unit-info-overlay\"], [1, \"p-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-2\"], [1, \"text-white\", \"fw-bold\", \"mb-0\", \"me-3\"], [1, \"fa-solid\", \"fa-check-circle\", \"text-success\", \"fa-lg\"], [1, \"text-white\", \"mb-0\"], [1, \"fa-solid\", \"fa-location-dot\", \"me-2\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"card-body\"], [1, \"property-details-text\"], [1, \"mb-3\", \"fs-5\"], [4, \"ngIf\"], [1, \"col-lg-4\"], [1, \"card\", \"mb-4\"], [1, \"card-body\", \"text-center\"], [1, \"text-success\", \"fw-bold\", \"mb-2\"], [1, \"d-flex\", \"justify-content-center\", \"gap-2\", \"mb-3\"], [1, \"badge\", \"bg-light-primary\", \"text-primary\"], [1, \"badge\", \"bg-light-success\", \"text-success\"], [1, \"row\", \"g-3\"], [1, \"col-6\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fa-solid\", \"fa-bed\", \"text-primary\", \"me-2\"], [1, \"text-muted\", \"d-block\"], [1, \"fw-semibold\"], [1, \"fa-solid\", \"fa-bath\", \"text-primary\", \"me-2\"], [1, \"fa-solid\", \"fa-ruler-combined\", \"text-primary\", \"me-2\"], [1, \"fa-solid\", \"fa-building\", \"text-primary\", \"me-2\"], [1, \"row\", \"g-2\"], [\"class\", \"col-12\", 4, \"ngFor\", \"ngForOf\"], [1, \"carousel-item\", \"h-100\"], [1, \"d-block\", \"w-100\", \"h-100\", \"cursor-pointer\", 2, \"object-fit\", \"cover\", \"border-radius\", \"0.5rem\", 3, \"src\", \"alt\"], [\"controls\", \"\", 1, \"d-block\", \"w-100\", \"h-100\", \"cursor-pointer\", 2, \"object-fit\", \"cover\", \"border-radius\", \"0.5rem\", 3, \"src\"], [\"type\", \"button\", \"data-bs-target\", \"#unitImagesCarousel\"], [1, \"col-12\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"disabled\", \"\", 1, \"form-check-input\", 3, \"checked\"], [1, \"form-check-label\", \"text-dark\"]],\n    template: function UnitDetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"app-developer-header\", 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9)(10, \"div\", 10);\n        i0.ɵɵtemplate(11, UnitDetailsComponent_div_11_Template, 3, 4, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 12);\n        i0.ɵɵtemplate(13, UnitDetailsComponent_button_13_Template, 1, 3, \"button\", 13);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(14, \"div\", 14)(15, \"span\", 15);\n        i0.ɵɵtext(16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 16)(18, \"div\", 17)(19, \"div\", 18)(20, \"h2\", 19);\n        i0.ɵɵtext(21);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(22, \"i\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"p\", 21);\n        i0.ɵɵelement(24, \"i\", 22);\n        i0.ɵɵtext(25);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(26, \"div\", 23)(27, \"div\", 24)(28, \"h5\", 25);\n        i0.ɵɵtext(29, \"Property details\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"div\", 26)(31, \"div\", 27)(32, \"p\", 28);\n        i0.ɵɵtext(33, \" This is a \");\n        i0.ɵɵelementStart(34, \"strong\");\n        i0.ɵɵtext(35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(36, \" unit located in \");\n        i0.ɵɵelementStart(37, \"strong\");\n        i0.ɵɵtext(38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(39, \". The unit is situated in building number \");\n        i0.ɵɵelementStart(40, \"strong\");\n        i0.ɵɵtext(41);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(42, \" on the \");\n        i0.ɵɵelementStart(43, \"strong\");\n        i0.ɵɵtext(44);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(45, \" floor. \");\n        i0.ɵɵtemplate(46, UnitDetailsComponent_span_46_Template, 6, 4, \"span\", 29);\n        i0.ɵɵtext(47, \" The unit features \");\n        i0.ɵɵelementStart(48, \"strong\");\n        i0.ɵɵtext(49);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(50, \" finishing and is available with \");\n        i0.ɵɵelementStart(51, \"strong\");\n        i0.ɵɵtext(52);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(53, \" payment options. \");\n        i0.ɵɵtemplate(54, UnitDetailsComponent_span_54_Template, 6, 4, \"span\", 29)(55, UnitDetailsComponent_span_55_Template, 6, 4, \"span\", 29)(56, UnitDetailsComponent_span_56_Template, 6, 4, \"span\", 29)(57, UnitDetailsComponent_span_57_Template, 6, 4, \"span\", 29);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(58, \"div\", 30)(59, \"div\", 31)(60, \"div\", 32)(61, \"h3\", 33);\n        i0.ɵɵtext(62);\n        i0.ɵɵpipe(63, \"number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(64, \"div\", 34)(65, \"span\", 35);\n        i0.ɵɵtext(66, \"Compound\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(67, \"span\", 36);\n        i0.ɵɵtext(68);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(69, \"div\", 31)(70, \"div\", 24)(71, \"h5\", 25);\n        i0.ɵɵtext(72, \"Unit Information\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(73, \"div\", 26)(74, \"div\", 37)(75, \"div\", 38)(76, \"div\", 39);\n        i0.ɵɵelement(77, \"i\", 40);\n        i0.ɵɵelementStart(78, \"div\")(79, \"small\", 41);\n        i0.ɵɵtext(80, \"Bedrooms\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(81, \"span\", 42);\n        i0.ɵɵtext(82);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(83, \"div\", 38)(84, \"div\", 39);\n        i0.ɵɵelement(85, \"i\", 43);\n        i0.ɵɵelementStart(86, \"div\")(87, \"small\", 41);\n        i0.ɵɵtext(88, \"Bathrooms\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(89, \"span\", 42);\n        i0.ɵɵtext(90);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(91, \"div\", 38)(92, \"div\", 39);\n        i0.ɵɵelement(93, \"i\", 44);\n        i0.ɵɵelementStart(94, \"div\")(95, \"small\", 41);\n        i0.ɵɵtext(96, \"Area\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(97, \"span\", 42);\n        i0.ɵɵtext(98);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(99, \"div\", 38)(100, \"div\", 39);\n        i0.ɵɵelement(101, \"i\", 45);\n        i0.ɵɵelementStart(102, \"div\")(103, \"small\", 41);\n        i0.ɵɵtext(104, \"Floor\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(105, \"span\", 42);\n        i0.ɵɵtext(106);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(107, \"div\", 31)(108, \"div\", 24)(109, \"h5\", 25);\n        i0.ɵɵtext(110, \"Features\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(111, \"div\", 26)(112, \"div\", 46);\n        i0.ɵɵtemplate(113, UnitDetailsComponent_div_113_Template, 5, 2, \"div\", 47);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"title\", ((ctx.unitDetails == null ? null : ctx.unitDetails.type) || \"unknown\") + \" - \" + ((ctx.unitDetails == null ? null : ctx.unitDetails.modelCode) || \"unknown unit code\"))(\"subtitle\", ((ctx.unitDetails.city == null ? null : ctx.unitDetails.city.name_en) || \"unknown\") + \", \" + ((ctx.unitDetails == null ? null : ctx.unitDetails.area == null ? null : ctx.unitDetails.area.name_en) || \"\"));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngForOf\", ctx.AllImages);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.AllImages);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.unitDetails == null ? null : ctx.unitDetails.type) || \"unknown\", \" \");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate2(\" \", (ctx.unitDetails == null ? null : ctx.unitDetails.modelCode) || \"unknown unit code\", \" - \", (ctx.unitDetails == null ? null : ctx.unitDetails.numberOfRooms) || \"unknown\", \" Rooms \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.unitDetails.city == null ? null : ctx.unitDetails.city.name_en) || \"unknown\", \" \");\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.type);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate2(\"\", ctx.unitDetails == null ? null : ctx.unitDetails.area == null ? null : ctx.unitDetails.area.name_en, \", \", ctx.unitDetails == null ? null : ctx.unitDetails.city == null ? null : ctx.unitDetails.city.name_en, \"\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.buildingNumber);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.floor);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.unitDetails == null ? null : ctx.unitDetails.unitArea);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.finishingType);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.paymentSystem);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.unitDetails == null ? null : ctx.unitDetails.pricePerMeterInCash);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.unitDetails == null ? null : ctx.unitDetails.pricePerMeterInInstallment);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.unitDetails == null ? null : ctx.unitDetails.totalPriceInCash);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.unitDetails == null ? null : ctx.unitDetails.totalPriceInInstallment);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(63, 27, ctx.unitDetails == null ? null : ctx.unitDetails.totalPriceInCash, \"1.0-0\"), \" EGP \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.status);\n        i0.ɵɵadvance(14);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.numberOfRooms);\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.numberOfBathrooms);\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate1(\"\", ctx.unitDetails == null ? null : ctx.unitDetails.unitArea, \" m\\u00B2\");\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.floor);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngForOf\", ctx.features);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i4.HeaderComponent, i3.DecimalPipe],\n    styles: [\".cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\\n  border: 1px solid rgba(0, 0, 0, 0.125);\\n  transition: all 0.3s ease;\\n}\\n.card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.badge.bg-light-primary[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--bs-primary-rgb), 0.1) !important;\\n}\\n.badge.bg-light-success[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--bs-success-rgb), 0.1) !important;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n}\\n\\n.form-check-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.position-relative[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.position-relative[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n}\\n\\n@media (max-width: 768px) {\\n  .col-lg-8[_ngcontent-%COMP%], \\n   .col-lg-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n}\\n.spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  font-weight: 500;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border: none;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\\n  transform: translateY(-1px);\\n}\\n\\n.text-success[_ngcontent-%COMP%] {\\n  color: #28a745 !important;\\n  font-size: 1.5rem;\\n}\\n\\n.fa-solid.text-primary[_ngcontent-%COMP%] {\\n  color: #667eea !important;\\n}\\n\\n.unit-image-container[_ngcontent-%COMP%] {\\n  height: 400px;\\n  border-radius: 0.5rem;\\n  overflow: hidden;\\n}\\n\\n.unit-image-background[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n}\\n\\n.upload-section[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.upload-section[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #0d6efd !important;\\n  font-weight: 600;\\n}\\n.upload-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d !important;\\n}\\n\\n.unit-info-overlay[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);\\n}\\n.unit-info-overlay[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);\\n}\\n.unit-info-overlay[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\\n}\\n\\n.bg-pink[_ngcontent-%COMP%] {\\n  background-color: #e91e63 !important;\\n}\\n\\n.property-details-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  line-height: 1.6;\\n  color: #495057;\\n  font-size: 0.95rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "item_r2", "url", "ɵɵsanitizeUrl", "i_r3", "ɵɵelementStart", "ɵɵtemplate", "UnitDetailsComponent_div_11_ng_container_1_Template", "UnitDetailsComponent_div_11_ng_container_2_Template", "ɵɵelementEnd", "ɵɵclassProp", "type", "i_r4", "ɵɵtext", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r4", "unitDetails", "unitArea", "pricePerMeterInCash", "pricePerMeterInInstallment", "totalPriceInCash", "totalPriceInInstallment", "isFeatureEnabled", "feature_r6", "value", "ɵɵtextInterpolate", "name", "UnitDetailsComponent", "route", "router", "unitsService", "unitId", "isLoading", "AllImages", "isAutoRotating", "features", "constructor", "ngOnInit", "queryParams", "subscribe", "params", "loadUnitDetails", "getUnitById", "next", "response", "console", "log", "data", "concate", "error", "details", "gallery", "length", "diagram", "push", "locationInMasterPlan", "featureValue", "otherAccessories", "includes", "goBack", "navigate", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "UnitsService", "selectors", "decls", "vars", "consts", "template", "UnitDetailsComponent_Template", "rf", "ctx", "UnitDetailsComponent_div_11_Template", "UnitDetailsComponent_button_13_Template", "UnitDetailsComponent_span_46_Template", "UnitDetailsComponent_span_54_Template", "UnitDetailsComponent_span_55_Template", "UnitDetailsComponent_span_56_Template", "UnitDetailsComponent_span_57_Template", "UnitDetailsComponent_div_113_Template", "modelCode", "city", "name_en", "area", "ɵɵtextInterpolate2", "numberOfRooms", "buildingNumber", "floor", "finishingType", "paymentSystem", "status", "numberOfBathrooms"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\developer\\projects\\components\\unit-details\\unit-details.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\developer\\projects\\components\\unit-details\\unit-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { UnitsService } from '../../../services/units.service';\r\n\r\n@Component({\r\n  selector: 'app-unit-details',\r\n  templateUrl: './unit-details.component.html',\r\n  styleUrl: './unit-details.component.scss',\r\n})\r\nexport class UnitDetailsComponent implements OnInit {\r\n  unitId: number | null = null;\r\n  unitDetails: any;\r\n  isLoading: boolean = false;\r\n\r\n  // Carousel properties\r\n  AllImages: any[] = [];\r\n  isAutoRotating: boolean = true;\r\n\r\n  // Features mapping\r\n  features = [\r\n    { name: 'Garage', value: 'garage' },\r\n    { name: 'Clubhouse', value: 'clubhouse' },\r\n    { name: 'Club', value: 'club' },\r\n    { name: 'Storage', value: 'storage' },\r\n    { name: 'Elevator', value: 'elevator' },\r\n    { name: 'Swimming Pool', value: 'swimming_pool' }\r\n  ];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private unitsService: UnitsService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.route.queryParams.subscribe((params) => {\r\n      if (params['unitId']) {\r\n        this.unitId = +params['unitId'];\r\n      }\r\n    });\r\n    this.loadUnitDetails();\r\n  }\r\n\r\n  loadUnitDetails() {\r\n    if (this.unitId) {\r\n      this.isLoading = true;\r\n      this.unitsService.getUnitById(this.unitId).subscribe({\r\n        next: (response) => {\r\n          console.log(response.data);\r\n          this.unitDetails = response.data;\r\n          console.log(this.unitDetails);\r\n          this.concate(this.unitDetails);\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading unit details:', error);\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  concate(details: any  ) {\r\n\r\n\r\n    // Add gallery items if they exist\r\n    if (details && details.gallery && details.gallery.length > 0) {\r\n    this.AllImages = [...details.gallery];\r\n    }\r\n\r\n    // Add diagram if it exists\r\n    if (details && details.diagram) {\r\n       this.AllImages.push({\r\n        type: 'image',\r\n        url: details.diagram,\r\n\r\n      });\r\n    }\r\n\r\n    // Add location in master plan if it exists\r\n    if (details && details.locationInMasterPlan) {\r\n       this.AllImages.push({\r\n        type: 'image',\r\n        url: details.locationInMasterPlan,\r\n\r\n      });\r\n    }\r\n\r\n  }\r\n\r\n  isFeatureEnabled(featureValue: string): boolean {\r\n    return this.unitDetails?.otherAccessories?.includes(featureValue) || false;\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/developer/projects/models/units']);\r\n  }\r\n}\r\n", "<!-- Header -->\r\n<div class=\"mb-5 mt-0\">\r\n  <app-developer-header\r\n    [title]=\"\r\n      (unitDetails?.type || 'unknown') +\r\n      ' - ' +\r\n      (unitDetails?.modelCode || 'unknown unit code')\r\n    \"\r\n    [subtitle]=\"\r\n      (unitDetails.city?.name_en || 'unknown') +\r\n      ', ' +\r\n      (unitDetails?.area?.name_en || '')\r\n    \"\r\n  >\r\n  </app-developer-header>\r\n</div>\r\n\r\n<!-- Unit Details Content -->\r\n<div class=\"row\">\r\n  <!-- Left Column - Main Image and Gallery -->\r\n  <div class=\"col-lg-8\">\r\n    <!-- Main Unit Image -->\r\n    <div class=\"card mb-5\">\r\n      <div class=\"card-body p-0\">\r\n        <div class=\"position-relative unit-image-container\">\r\n          <!-- Background with gradient -->\r\n          <div class=\"unit-image-background\">\r\n            <!-- Bootstrap Carousel -->\r\n            <div class=\"h-100\">\r\n              <div\r\n                id=\"unitImagesCarousel\"\r\n                class=\"carousel slide h-100\"\r\n                data-bs-ride=\"carousel\"\r\n                data-bs-interval=\"3000\"\r\n              >\r\n                <!-- Carousel Inner -->\r\n                <div class=\"carousel-inner h-100\">\r\n                  <!-- <div class=\"carousel-item h-100\" *ngFor=\"let image of unitImages; let i = index\"\r\n                    [class.active]=\"i === 0\">\r\n                    <img [src]=\"image.url || 'assets/media/auth/404-error.png'\" [alt]=\"'Unit Image ' + (i + 1)\"\r\n                      class=\"d-block w-100 h-100 cursor-pointer\" style=\"object-fit: cover; border-radius: 0.5rem\" />\r\n                  </div> -->\r\n\r\n                  <!-- Gallery Items -->\r\n                  <div\r\n                    class=\"carousel-item h-100\"\r\n                    *ngFor=\"let item of AllImages; let i = index\"\r\n                    [class.active]=\"i === 0\"\r\n                  >\r\n                    <ng-container *ngIf=\"item.type === 'image'\">\r\n                      <img\r\n                        [src]=\"item.url || 'assets/media/auth/404-error.png'\"\r\n                        [alt]=\"'Unit Image ' + (i + 1)\"\r\n                        class=\"d-block w-100 h-100 cursor-pointer\"\r\n                        style=\"object-fit: cover; border-radius: 0.5rem\"\r\n                      />\r\n                    </ng-container>\r\n                    <ng-container *ngIf=\"item.type === 'video'\">\r\n                      <video\r\n                        [src]=\"item.url\"\r\n                        controls\r\n                        class=\"d-block w-100 h-100 cursor-pointer\"\r\n                        style=\"object-fit: cover; border-radius: 0.5rem\"\r\n                      ></video>\r\n                    </ng-container>\r\n                  </div>\r\n                  <!-- <div class=\"carousel-item h-100\" *ngIf=\"unitDetails?.diagram\">\r\n                    <img [src]=\"unitDetails?.diagram || 'assets/media/auth/404-error.png'\" [alt]=\"'Unit Diagram'\"\r\n                      class=\"d-block w-100 h-100 cursor-pointer\" style=\"object-fit: cover; border-radius: 0.5rem\" />\r\n                  </div> -->\r\n                  <!-- Location in Master Plan Item -->\r\n                  <!-- <div class=\"carousel-item h-100\" *ngIf=\"unitDetails?.locationInMasterPlan\">\r\n                    <img [src]=\" unitDetails?.locationInMasterPlan|| 'assets/media/auth/404-error.png'\"\r\n                      alt=\"Location in Master Plan\" class=\"d-block w-100 h-100 cursor-pointer\"\r\n                      style=\"object-fit: cover; border-radius: 0.5rem\" />\r\n                  </div> -->\r\n                </div>\r\n\r\n                <!-- Carousel Indicators -->\r\n                <div class=\"carousel-indicators\">\r\n                  <button\r\n                    type=\"button\"\r\n                    data-bs-target=\"#unitImagesCarousel\"\r\n                    *ngFor=\"let image of AllImages; let i = index\"\r\n                    [attr.data-bs-slide-to]=\"i\"\r\n                    [class.active]=\"i === 0\"\r\n                  ></button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Status Badge -->\r\n          <div class=\"position-absolute top-0 start-0 m-3\">\r\n            <span class=\"badge bg-pink text-white fs-7 px-3 py-2 rounded-pill\">\r\n              {{ unitDetails?.type || \"unknown\" }}\r\n            </span>\r\n          </div>\r\n\r\n          <!-- Unit Information Overlay -->\r\n          <div\r\n            class=\"position-absolute bottom-0 start-0 end-0 unit-info-overlay\"\r\n          >\r\n            <div class=\"p-4\">\r\n              <div class=\"d-flex align-items-center mb-2\">\r\n                <h2 class=\"text-white fw-bold mb-0 me-3\">\r\n                  {{ unitDetails?.modelCode || \"unknown unit code\" }} -\r\n                  {{ unitDetails?.numberOfRooms || \"unknown\" }} Rooms\r\n                </h2>\r\n                <i class=\"fa-solid fa-check-circle text-success fa-lg\"></i>\r\n              </div>\r\n              <p class=\"text-white mb-0\">\r\n                <i class=\"fa-solid fa-location-dot me-2\"></i>\r\n                {{ unitDetails.city?.name_en || \"unknown\" }}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Property Details Section -->\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <h5 class=\"card-title mb-0\">Property details</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <div class=\"property-details-text\">\r\n          <p class=\"mb-3 fs-5\">\r\n            This is a <strong>{{ unitDetails?.type }}</strong> unit located in\r\n            <strong\r\n              >{{ unitDetails?.area?.name_en }},\r\n              {{ unitDetails?.city?.name_en }}</strong\r\n            >. The unit is situated in building number\r\n            <strong>{{ unitDetails?.buildingNumber }}</strong> on the\r\n            <strong>{{ unitDetails?.floor }}</strong> floor.\r\n\r\n            <span *ngIf=\"unitDetails?.unitArea\"\r\n              >It spans an area of\r\n              <strong>{{ unitDetails?.unitArea | number : \"1.0-0\" }} m²</strong\r\n              >.</span\r\n            >\r\n\r\n            The unit features\r\n            <strong>{{ unitDetails?.finishingType }}</strong> finishing and is\r\n            available with\r\n            <strong>{{ unitDetails?.paymentSystem }}</strong> payment options.\r\n\r\n            <span *ngIf=\"unitDetails?.pricePerMeterInCash\"\r\n              >The price per meter in cash is\r\n              <strong\r\n                >{{\r\n                  unitDetails?.pricePerMeterInCash | number : \"1.0-0\"\r\n                }}\r\n                EGP</strong\r\n              >,</span\r\n            >\r\n            <span *ngIf=\"unitDetails?.pricePerMeterInInstallment\">\r\n              while the price per meter in installments is\r\n              <strong\r\n                >{{\r\n                  unitDetails?.pricePerMeterInInstallment | number : \"1.0-0\"\r\n                }}\r\n                EGP</strong\r\n              >.</span\r\n            >\r\n\r\n            <span *ngIf=\"unitDetails?.totalPriceInCash\"\r\n              >The total cash price is\r\n              <strong\r\n                >{{\r\n                  unitDetails?.totalPriceInCash | number : \"1.0-0\"\r\n                }}\r\n                EGP</strong\r\n              >,</span\r\n            >\r\n            <span *ngIf=\"unitDetails?.totalPriceInInstallment\">\r\n              and the total installment price is\r\n              <strong\r\n                >{{\r\n                  unitDetails?.totalPriceInInstallment | number : \"1.0-0\"\r\n                }}\r\n                EGP</strong\r\n              >.</span\r\n            >\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Right Column - Unit Information -->\r\n  <div class=\"col-lg-4\">\r\n    <!-- Price Card -->\r\n    <div class=\"card mb-4\">\r\n      <div class=\"card-body text-center\">\r\n        <h3 class=\"text-success fw-bold mb-2\">\r\n          {{ unitDetails?.totalPriceInCash | number : \"1.0-0\" }} EGP\r\n        </h3>\r\n        <div class=\"d-flex justify-content-center gap-2 mb-3\">\r\n          <span class=\"badge bg-light-primary text-primary\">Compound</span>\r\n          <span class=\"badge bg-light-success text-success\">{{\r\n            unitDetails?.status\r\n          }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Unit Information -->\r\n    <div class=\"card mb-4\">\r\n      <div class=\"card-header\">\r\n        <h5 class=\"card-title mb-0\">Unit Information</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <div class=\"row g-3\">\r\n          <div class=\"col-6\">\r\n            <div class=\"d-flex align-items-center\">\r\n              <i class=\"fa-solid fa-bed text-primary me-2\"></i>\r\n              <div>\r\n                <small class=\"text-muted d-block\">Bedrooms</small>\r\n                <span class=\"fw-semibold\">{{\r\n                  unitDetails?.numberOfRooms\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-6\">\r\n            <div class=\"d-flex align-items-center\">\r\n              <i class=\"fa-solid fa-bath text-primary me-2\"></i>\r\n              <div>\r\n                <small class=\"text-muted d-block\">Bathrooms</small>\r\n                <span class=\"fw-semibold\">{{\r\n                  unitDetails?.numberOfBathrooms\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-6\">\r\n            <div class=\"d-flex align-items-center\">\r\n              <i class=\"fa-solid fa-ruler-combined text-primary me-2\"></i>\r\n              <div>\r\n                <small class=\"text-muted d-block\">Area</small>\r\n                <span class=\"fw-semibold\">{{ unitDetails?.unitArea }} m²</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-6\">\r\n            <div class=\"d-flex align-items-center\">\r\n              <i class=\"fa-solid fa-building text-primary me-2\"></i>\r\n              <div>\r\n                <small class=\"text-muted d-block\">Floor</small>\r\n                <span class=\"fw-semibold\">{{ unitDetails?.floor }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Features -->\r\n    <div class=\"card mb-4\">\r\n      <div class=\"card-header\">\r\n        <h5 class=\"card-title mb-0\">Features</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <div class=\"row g-2\">\r\n          <div class=\"col-12\" *ngFor=\"let feature of features\">\r\n            <div class=\"form-check\">\r\n              <input\r\n                class=\"form-check-input\"\r\n                type=\"checkbox\"\r\n                [checked]=\"isFeatureEnabled(feature.value)\"\r\n                disabled\r\n              />\r\n              <label class=\"form-check-label text-dark\">{{\r\n                feature.name\r\n              }}</label>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;ICiDoBA,EAAA,CAAAC,uBAAA,GAA4C;IAC1CD,EAAA,CAAAE,SAAA,cAKE;;;;;;;IAJAF,EAAA,CAAAG,SAAA,EAAqD;IACrDH,EADA,CAAAI,UAAA,QAAAC,OAAA,CAAAC,GAAA,uCAAAN,EAAA,CAAAO,aAAA,CAAqD,yBAAAC,IAAA,MACtB;;;;;IAKnCR,EAAA,CAAAC,uBAAA,GAA4C;IAC1CD,EAAA,CAAAE,SAAA,gBAKS;;;;;IAJPF,EAAA,CAAAG,SAAA,EAAgB;IAAhBH,EAAA,CAAAI,UAAA,QAAAC,OAAA,CAAAC,GAAA,EAAAN,EAAA,CAAAO,aAAA,CAAgB;;;;;IAftBP,EAAA,CAAAS,cAAA,cAIC;IASCT,EARA,CAAAU,UAAA,IAAAC,mDAAA,2BAA4C,IAAAC,mDAAA,2BAQA;IAQ9CZ,EAAA,CAAAa,YAAA,EAAM;;;;;IAlBJb,EAAA,CAAAc,WAAA,WAAAN,IAAA,OAAwB;IAETR,EAAA,CAAAG,SAAA,EAA2B;IAA3BH,EAAA,CAAAI,UAAA,SAAAC,OAAA,CAAAU,IAAA,aAA2B;IAQ3Bf,EAAA,CAAAG,SAAA,EAA2B;IAA3BH,EAAA,CAAAI,UAAA,SAAAC,OAAA,CAAAU,IAAA,aAA2B;;;;;IAuB5Cf,EAAA,CAAAE,SAAA,iBAMU;;;;IADRF,EAAA,CAAAc,WAAA,WAAAE,IAAA,OAAwB;;;;;;IAoDhChB,EAAA,CAAAS,cAAA,WACG;IAAAT,EAAA,CAAAiB,MAAA,2BACD;IAAAjB,EAAA,CAAAS,cAAA,aAAQ;IAAAT,EAAA,CAAAiB,MAAA,GAAiD;;IAAAjB,EAAA,CAAAa,YAAA,EACxD;IAAAb,EAAA,CAAAiB,MAAA,QAAC;IAAAjB,EAAA,CAAAa,YAAA,EACH;;;;IAFSb,EAAA,CAAAG,SAAA,GAAiD;IAAjDH,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAmB,WAAA,OAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,QAAA,uBAAiD;;;;;IAS3DtB,EAAA,CAAAS,cAAA,WACG;IAAAT,EAAA,CAAAiB,MAAA,sCACD;IAAAjB,EAAA,CAAAS,cAAA,aACG;IAAAT,EAAA,CAAAiB,MAAA,GAGE;;IAAAjB,EAAA,CAAAa,YAAA,EACJ;IAAAb,EAAA,CAAAiB,MAAA,QAAC;IAAAjB,EAAA,CAAAa,YAAA,EACH;;;;IALIb,EAAA,CAAAG,SAAA,GAGE;IAHFH,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAmB,WAAA,OAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAE,mBAAA,mBAGE;;;;;IAGPvB,EAAA,CAAAS,cAAA,WAAsD;IACpDT,EAAA,CAAAiB,MAAA,qDACA;IAAAjB,EAAA,CAAAS,cAAA,aACG;IAAAT,EAAA,CAAAiB,MAAA,GAGE;;IAAAjB,EAAA,CAAAa,YAAA,EACJ;IAAAb,EAAA,CAAAiB,MAAA,QAAC;IAAAjB,EAAA,CAAAa,YAAA,EACH;;;;IALIb,EAAA,CAAAG,SAAA,GAGE;IAHFH,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAmB,WAAA,OAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAG,0BAAA,mBAGE;;;;;IAIPxB,EAAA,CAAAS,cAAA,WACG;IAAAT,EAAA,CAAAiB,MAAA,+BACD;IAAAjB,EAAA,CAAAS,cAAA,aACG;IAAAT,EAAA,CAAAiB,MAAA,GAGE;;IAAAjB,EAAA,CAAAa,YAAA,EACJ;IAAAb,EAAA,CAAAiB,MAAA,QAAC;IAAAjB,EAAA,CAAAa,YAAA,EACH;;;;IALIb,EAAA,CAAAG,SAAA,GAGE;IAHFH,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAmB,WAAA,OAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAI,gBAAA,mBAGE;;;;;IAGPzB,EAAA,CAAAS,cAAA,WAAmD;IACjDT,EAAA,CAAAiB,MAAA,2CACA;IAAAjB,EAAA,CAAAS,cAAA,aACG;IAAAT,EAAA,CAAAiB,MAAA,GAGE;;IAAAjB,EAAA,CAAAa,YAAA,EACJ;IAAAb,EAAA,CAAAiB,MAAA,QAAC;IAAAjB,EAAA,CAAAa,YAAA,EACH;;;;IALIb,EAAA,CAAAG,SAAA,GAGE;IAHFH,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAmB,WAAA,OAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAK,uBAAA,mBAGE;;;;;IAqFP1B,EADF,CAAAS,cAAA,cAAqD,cAC3B;IACtBT,EAAA,CAAAE,SAAA,gBAKE;IACFF,EAAA,CAAAS,cAAA,gBAA0C;IAAAT,EAAA,CAAAiB,MAAA,GAExC;IAENjB,EAFM,CAAAa,YAAA,EAAQ,EACN,EACF;;;;;IAPAb,EAAA,CAAAG,SAAA,GAA2C;IAA3CH,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAO,gBAAA,CAAAC,UAAA,CAAAC,KAAA,EAA2C;IAGH7B,EAAA,CAAAG,SAAA,GAExC;IAFwCH,EAAA,CAAA8B,iBAAA,CAAAF,UAAA,CAAAG,IAAA,CAExC;;;AD3QhB,OAAM,MAAOC,oBAAoB;EAoBrBC,KAAA;EACAC,MAAA;EACAC,YAAA;EArBVC,MAAM,GAAkB,IAAI;EAC5Bf,WAAW;EACXgB,SAAS,GAAY,KAAK;EAE1B;EACAC,SAAS,GAAU,EAAE;EACrBC,cAAc,GAAY,IAAI;EAE9B;EACAC,QAAQ,GAAG,CACT;IAAET,IAAI,EAAE,QAAQ;IAAEF,KAAK,EAAE;EAAQ,CAAE,EACnC;IAAEE,IAAI,EAAE,WAAW;IAAEF,KAAK,EAAE;EAAW,CAAE,EACzC;IAAEE,IAAI,EAAE,MAAM;IAAEF,KAAK,EAAE;EAAM,CAAE,EAC/B;IAAEE,IAAI,EAAE,SAAS;IAAEF,KAAK,EAAE;EAAS,CAAE,EACrC;IAAEE,IAAI,EAAE,UAAU;IAAEF,KAAK,EAAE;EAAU,CAAE,EACvC;IAAEE,IAAI,EAAE,eAAe;IAAEF,KAAK,EAAE;EAAe,CAAE,CAClD;EAEDY,YACUR,KAAqB,EACrBC,MAAc,EACdC,YAA0B;IAF1B,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;EACnB;EAEHO,QAAQA,CAAA;IACN,IAAI,CAACT,KAAK,CAACU,WAAW,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC1C,IAAIA,MAAM,CAAC,QAAQ,CAAC,EAAE;QACpB,IAAI,CAACT,MAAM,GAAG,CAACS,MAAM,CAAC,QAAQ,CAAC;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,IAAI,CAACV,MAAM,EAAE;MACf,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACF,YAAY,CAACY,WAAW,CAAC,IAAI,CAACX,MAAM,CAAC,CAACQ,SAAS,CAAC;QACnDI,IAAI,EAAGC,QAAQ,IAAI;UACjBC,OAAO,CAACC,GAAG,CAACF,QAAQ,CAACG,IAAI,CAAC;UAC1B,IAAI,CAAC/B,WAAW,GAAG4B,QAAQ,CAACG,IAAI;UAChCF,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC9B,WAAW,CAAC;UAC7B,IAAI,CAACgC,OAAO,CAAC,IAAI,CAAChC,WAAW,CAAC;UAC9B,IAAI,CAACgB,SAAS,GAAG,KAAK;QACxB,CAAC;QACDiB,KAAK,EAAGA,KAAK,IAAI;UACfJ,OAAO,CAACI,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnD,IAAI,CAACjB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEAgB,OAAOA,CAACE,OAAY;IAGlB;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACC,OAAO,IAAID,OAAO,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAACnB,SAAS,GAAG,CAAC,GAAGiB,OAAO,CAACC,OAAO,CAAC;IACrC;IAEA;IACA,IAAID,OAAO,IAAIA,OAAO,CAACG,OAAO,EAAE;MAC7B,IAAI,CAACpB,SAAS,CAACqB,IAAI,CAAC;QACnB5C,IAAI,EAAE,OAAO;QACbT,GAAG,EAAEiD,OAAO,CAACG;OAEd,CAAC;IACJ;IAEA;IACA,IAAIH,OAAO,IAAIA,OAAO,CAACK,oBAAoB,EAAE;MAC1C,IAAI,CAACtB,SAAS,CAACqB,IAAI,CAAC;QACnB5C,IAAI,EAAE,OAAO;QACbT,GAAG,EAAEiD,OAAO,CAACK;OAEd,CAAC;IACJ;EAEF;EAEAjC,gBAAgBA,CAACkC,YAAoB;IACnC,OAAO,IAAI,CAACxC,WAAW,EAAEyC,gBAAgB,EAAEC,QAAQ,CAACF,YAAY,CAAC,IAAI,KAAK;EAC5E;EAEAG,MAAMA,CAAA;IACJ,IAAI,CAAC9B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,kCAAkC,CAAC,CAAC;EAC5D;;qCAvFWjC,oBAAoB,EAAAhC,EAAA,CAAAkE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApE,EAAA,CAAAkE,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAArE,EAAA,CAAAkE,iBAAA,CAAAI,EAAA,CAAAC,YAAA;EAAA;;UAApBvC,oBAAoB;IAAAwC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRjC9E,EAAA,CAAAS,cAAA,aAAuB;QACrBT,EAAA,CAAAE,SAAA,8BAYuB;QACzBF,EAAA,CAAAa,YAAA,EAAM;QAqBUb,EAlBhB,CAAAS,cAAA,aAAiB,aAEO,aAEG,aACM,aAC2B,aAEf,aAEd,aAMhB,eAEmC;QAQhCT,EAAA,CAAAU,UAAA,KAAAsE,oCAAA,kBAIC;QA4BHhF,EAAA,CAAAa,YAAA,EAAM;QAGNb,EAAA,CAAAS,cAAA,eAAiC;QAC/BT,EAAA,CAAAU,UAAA,KAAAuE,uCAAA,qBAMC;QAITjF,EAHM,CAAAa,YAAA,EAAM,EACF,EACF,EACF;QAIJb,EADF,CAAAS,cAAA,eAAiD,gBACoB;QACjET,EAAA,CAAAiB,MAAA,IACF;QACFjB,EADE,CAAAa,YAAA,EAAO,EACH;QAQAb,EALN,CAAAS,cAAA,eAEC,eACkB,eAC6B,cACD;QACvCT,EAAA,CAAAiB,MAAA,IAEF;QAAAjB,EAAA,CAAAa,YAAA,EAAK;QACLb,EAAA,CAAAE,SAAA,aAA2D;QAC7DF,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAS,cAAA,aAA2B;QACzBT,EAAA,CAAAE,SAAA,aAA6C;QAC7CF,EAAA,CAAAiB,MAAA,IACF;QAKVjB,EALU,CAAAa,YAAA,EAAI,EACA,EACF,EACF,EACF,EACF;QAKFb,EAFJ,CAAAS,cAAA,eAAkB,eACS,cACK;QAAAT,EAAA,CAAAiB,MAAA,wBAAgB;QAC9CjB,EAD8C,CAAAa,YAAA,EAAK,EAC7C;QAGFb,EAFJ,CAAAS,cAAA,eAAuB,eACc,aACZ;QACnBT,EAAA,CAAAiB,MAAA,mBAAU;QAAAjB,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAiB,MAAA,IAAuB;QAAAjB,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAiB,MAAA,yBACnD;QAAAjB,EAAA,CAAAS,cAAA,cACG;QAAAT,EAAA,CAAAiB,MAAA,IAC+B;QAAAjB,EAAA,CAAAa,YAAA,EACjC;QAAAb,EAAA,CAAAiB,MAAA,kDACD;QAAAjB,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAiB,MAAA,IAAiC;QAAAjB,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAiB,MAAA,gBACnD;QAAAjB,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAiB,MAAA,IAAwB;QAAAjB,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAiB,MAAA,gBAE1C;QAAAjB,EAAA,CAAAU,UAAA,KAAAwE,qCAAA,mBACG;QAKHlF,EAAA,CAAAiB,MAAA,2BACA;QAAAjB,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAiB,MAAA,IAAgC;QAAAjB,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAiB,MAAA,yCAElD;QAAAjB,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAiB,MAAA,IAAgC;QAAAjB,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAiB,MAAA,0BAElD;QA4BAjB,EA5BA,CAAAU,UAAA,KAAAyE,qCAAA,mBACG,KAAAC,qCAAA,mBAQmD,KAAAC,qCAAA,mBAWnD,KAAAC,qCAAA,mBAQgD;QAa7DtF,EAJQ,CAAAa,YAAA,EAAI,EACA,EACF,EACF,EACF;QAOAb,EAJN,CAAAS,cAAA,eAAsB,eAEG,eACc,cACK;QACpCT,EAAA,CAAAiB,MAAA,IACF;;QAAAjB,EAAA,CAAAa,YAAA,EAAK;QAEHb,EADF,CAAAS,cAAA,eAAsD,gBACF;QAAAT,EAAA,CAAAiB,MAAA,gBAAQ;QAAAjB,EAAA,CAAAa,YAAA,EAAO;QACjEb,EAAA,CAAAS,cAAA,gBAAkD;QAAAT,EAAA,CAAAiB,MAAA,IAEhD;QAGRjB,EAHQ,CAAAa,YAAA,EAAO,EACL,EACF,EACF;QAKFb,EAFJ,CAAAS,cAAA,eAAuB,eACI,cACK;QAAAT,EAAA,CAAAiB,MAAA,wBAAgB;QAC9CjB,EAD8C,CAAAa,YAAA,EAAK,EAC7C;QAIAb,EAHN,CAAAS,cAAA,eAAuB,eACA,eACA,eACsB;QACrCT,EAAA,CAAAE,SAAA,aAAiD;QAE/CF,EADF,CAAAS,cAAA,WAAK,iBAC+B;QAAAT,EAAA,CAAAiB,MAAA,gBAAQ;QAAAjB,EAAA,CAAAa,YAAA,EAAQ;QAClDb,EAAA,CAAAS,cAAA,gBAA0B;QAAAT,EAAA,CAAAiB,MAAA,IAExB;QAGRjB,EAHQ,CAAAa,YAAA,EAAO,EACL,EACF,EACF;QAEJb,EADF,CAAAS,cAAA,eAAmB,eACsB;QACrCT,EAAA,CAAAE,SAAA,aAAkD;QAEhDF,EADF,CAAAS,cAAA,WAAK,iBAC+B;QAAAT,EAAA,CAAAiB,MAAA,iBAAS;QAAAjB,EAAA,CAAAa,YAAA,EAAQ;QACnDb,EAAA,CAAAS,cAAA,gBAA0B;QAAAT,EAAA,CAAAiB,MAAA,IAExB;QAGRjB,EAHQ,CAAAa,YAAA,EAAO,EACL,EACF,EACF;QAEJb,EADF,CAAAS,cAAA,eAAmB,eACsB;QACrCT,EAAA,CAAAE,SAAA,aAA4D;QAE1DF,EADF,CAAAS,cAAA,WAAK,iBAC+B;QAAAT,EAAA,CAAAiB,MAAA,YAAI;QAAAjB,EAAA,CAAAa,YAAA,EAAQ;QAC9Cb,EAAA,CAAAS,cAAA,gBAA0B;QAAAT,EAAA,CAAAiB,MAAA,IAA8B;QAG9DjB,EAH8D,CAAAa,YAAA,EAAO,EAC3D,EACF,EACF;QAEJb,EADF,CAAAS,cAAA,eAAmB,gBACsB;QACrCT,EAAA,CAAAE,SAAA,cAAsD;QAEpDF,EADF,CAAAS,cAAA,YAAK,kBAC+B;QAAAT,EAAA,CAAAiB,MAAA,cAAK;QAAAjB,EAAA,CAAAa,YAAA,EAAQ;QAC/Cb,EAAA,CAAAS,cAAA,iBAA0B;QAAAT,EAAA,CAAAiB,MAAA,KAAwB;QAM9DjB,EAN8D,CAAAa,YAAA,EAAO,EACrD,EACF,EACF,EACF,EACF,EACF;QAKFb,EAFJ,CAAAS,cAAA,gBAAuB,gBACI,eACK;QAAAT,EAAA,CAAAiB,MAAA,iBAAQ;QACtCjB,EADsC,CAAAa,YAAA,EAAK,EACrC;QAEJb,EADF,CAAAS,cAAA,gBAAuB,gBACA;QACnBT,EAAA,CAAAU,UAAA,MAAA6E,qCAAA,kBAAqD;QAiB/DvF,EAJQ,CAAAa,YAAA,EAAM,EACF,EACF,EACF,EACF;;;QAxRFb,EAAA,CAAAG,SAAA,EAIC;QACDH,EALA,CAAAI,UAAA,YAAA2E,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAN,IAAA,4BAAAgE,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAmE,SAAA,0BAIC,eAAAT,GAAA,CAAA1D,WAAA,CAAAoE,IAAA,kBAAAV,GAAA,CAAA1D,WAAA,CAAAoE,IAAA,CAAAC,OAAA,2BAAAX,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAsE,IAAA,kBAAAZ,GAAA,CAAA1D,WAAA,CAAAsE,IAAA,CAAAD,OAAA,SAKA;QAkCgC1F,EAAA,CAAAG,SAAA,IAAc;QAAdH,EAAA,CAAAI,UAAA,YAAA2E,GAAA,CAAAzC,SAAA,CAAc;QAqCbtC,EAAA,CAAAG,SAAA,GAAc;QAAdH,EAAA,CAAAI,UAAA,YAAA2E,GAAA,CAAAzC,SAAA,CAAc;QAYtCtC,EAAA,CAAAG,SAAA,GACF;QADEH,EAAA,CAAAkB,kBAAA,OAAA6D,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAN,IAAA,oBACF;QAUMf,EAAA,CAAAG,SAAA,GAEF;QAFEH,EAAA,CAAA4F,kBAAA,OAAAb,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAmE,SAAA,kCAAAT,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAwE,aAAA,0BAEF;QAKA7F,EAAA,CAAAG,SAAA,GACF;QADEH,EAAA,CAAAkB,kBAAA,OAAA6D,GAAA,CAAA1D,WAAA,CAAAoE,IAAA,kBAAAV,GAAA,CAAA1D,WAAA,CAAAoE,IAAA,CAAAC,OAAA,oBACF;QAegB1F,EAAA,CAAAG,SAAA,IAAuB;QAAvBH,EAAA,CAAA8B,iBAAA,CAAAiD,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAN,IAAA,CAAuB;QAEtCf,EAAA,CAAAG,SAAA,GAC+B;QAD/BH,EAAA,CAAA4F,kBAAA,KAAAb,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAsE,IAAA,kBAAAZ,GAAA,CAAA1D,WAAA,CAAAsE,IAAA,CAAAD,OAAA,QAAAX,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAoE,IAAA,kBAAAV,GAAA,CAAA1D,WAAA,CAAAoE,IAAA,CAAAC,OAAA,KAC+B;QAE1B1F,EAAA,CAAAG,SAAA,GAAiC;QAAjCH,EAAA,CAAA8B,iBAAA,CAAAiD,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAyE,cAAA,CAAiC;QACjC9F,EAAA,CAAAG,SAAA,GAAwB;QAAxBH,EAAA,CAAA8B,iBAAA,CAAAiD,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAA0E,KAAA,CAAwB;QAEzB/F,EAAA,CAAAG,SAAA,GAA2B;QAA3BH,EAAA,CAAAI,UAAA,SAAA2E,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAC,QAAA,CAA2B;QAO1BtB,EAAA,CAAAG,SAAA,GAAgC;QAAhCH,EAAA,CAAA8B,iBAAA,CAAAiD,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAA2E,aAAA,CAAgC;QAEhChG,EAAA,CAAAG,SAAA,GAAgC;QAAhCH,EAAA,CAAA8B,iBAAA,CAAAiD,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAA4E,aAAA,CAAgC;QAEjCjG,EAAA,CAAAG,SAAA,GAAsC;QAAtCH,EAAA,CAAAI,UAAA,SAAA2E,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAE,mBAAA,CAAsC;QAStCvB,EAAA,CAAAG,SAAA,EAA6C;QAA7CH,EAAA,CAAAI,UAAA,SAAA2E,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAG,0BAAA,CAA6C;QAU7CxB,EAAA,CAAAG,SAAA,EAAmC;QAAnCH,EAAA,CAAAI,UAAA,SAAA2E,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAI,gBAAA,CAAmC;QASnCzB,EAAA,CAAAG,SAAA,EAA0C;QAA1CH,EAAA,CAAAI,UAAA,SAAA2E,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAK,uBAAA,CAA0C;QAqBnD1B,EAAA,CAAAG,SAAA,GACF;QADEH,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAmB,WAAA,SAAA4D,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAI,gBAAA,oBACF;QAGoDzB,EAAA,CAAAG,SAAA,GAEhD;QAFgDH,EAAA,CAAA8B,iBAAA,CAAAiD,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAA6E,MAAA,CAEhD;QAiB8BlG,EAAA,CAAAG,SAAA,IAExB;QAFwBH,EAAA,CAAA8B,iBAAA,CAAAiD,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAwE,aAAA,CAExB;QASwB7F,EAAA,CAAAG,SAAA,GAExB;QAFwBH,EAAA,CAAA8B,iBAAA,CAAAiD,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAA8E,iBAAA,CAExB;QASwBnG,EAAA,CAAAG,SAAA,GAA8B;QAA9BH,EAAA,CAAAkB,kBAAA,KAAA6D,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAAC,QAAA,aAA8B;QAS9BtB,EAAA,CAAAG,SAAA,GAAwB;QAAxBH,EAAA,CAAA8B,iBAAA,CAAAiD,GAAA,CAAA1D,WAAA,kBAAA0D,GAAA,CAAA1D,WAAA,CAAA0E,KAAA,CAAwB;QAehB/F,EAAA,CAAAG,SAAA,GAAW;QAAXH,EAAA,CAAAI,UAAA,YAAA2E,GAAA,CAAAvC,QAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}