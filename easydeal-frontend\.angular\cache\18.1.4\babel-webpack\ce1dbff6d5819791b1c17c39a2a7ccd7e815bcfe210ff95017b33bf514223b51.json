{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/taskes/New folder/easydeal-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../services/authentication.service\";\nimport * as i4 from \"@angular/common\";\nfunction LoginComponent_div_2_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"phone\"), \" \");\n  }\n}\nfunction LoginComponent_div_2_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"password\"), \" \");\n  }\n}\nfunction LoginComponent_div_2_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.loginErrorMessage, \" \");\n  }\n}\nfunction LoginComponent_div_2_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36);\n  }\n}\nfunction LoginComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"h1\", 6);\n    i0.ɵɵtext(4, \"Login to your account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"form\", 7);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_div_2_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.login());\n    });\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"label\", 9);\n    i0.ɵɵelement(8, \"i\", 10);\n    i0.ɵɵtext(9, \" Phone \");\n    i0.ɵɵelement(10, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 12);\n    i0.ɵɵlistener(\"blur\", function LoginComponent_div_2_Template_input_blur_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"phone\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, LoginComponent_div_2_div_12_Template, 2, 1, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 8)(14, \"label\", 14);\n    i0.ɵɵelement(15, \"i\", 15);\n    i0.ɵɵtext(16, \" Password \");\n    i0.ɵɵelement(17, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 16);\n    i0.ɵɵlistener(\"blur\", function LoginComponent_div_2_Template_input_blur_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"password\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, LoginComponent_div_2_div_19_Template, 2, 1, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 17)(21, \"div\", 18)(22, \"a\", 19);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_2_Template_a_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToForgotPassword());\n    });\n    i0.ɵɵtext(23, \"Forgot your password?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 20);\n    i0.ɵɵelement(25, \"input\", 21);\n    i0.ɵɵelementStart(26, \"label\", 22);\n    i0.ɵɵtext(27, \" Remember me \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(28, LoginComponent_div_2_div_28_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementStart(29, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_2_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.login());\n    });\n    i0.ɵɵtemplate(30, LoginComponent_div_2_span_30_Template, 1, 0, \"span\", 25);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 26)(33, \"div\", 27)(34, \"a\", 28)(35, \"span\", 29);\n    i0.ɵɵtext(36, \"Don't have an account? \");\n    i0.ɵɵelementStart(37, \"span\", 30);\n    i0.ɵɵtext(38, \"Create one\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(39, \"i\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 32);\n    i0.ɵɵtext(41, \" Need help? \");\n    i0.ɵɵelementStart(42, \"span\", 33);\n    i0.ɵɵtext(43, \"Contact us\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.loginForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"phone\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"phone\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loginErrorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingLogin);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isLoginFormValid() || ctx_r1.isLoadingLogin);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingLogin);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoadingLogin ? \"Logging in...\" : \"Log In\", \" \");\n  }\n}\nfunction LoginComponent_div_3_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"input\", ctx_r1.forgotPasswordForm), \" \");\n  }\n}\nfunction LoginComponent_div_3_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otpErrorMessage, \" \");\n  }\n}\nfunction LoginComponent_div_3_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36);\n  }\n}\nfunction LoginComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"h1\", 6);\n    i0.ɵɵtext(4, \"Forgot Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 37);\n    i0.ɵɵtext(6, \" Don't worry, we'll send you password recovery instructions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"form\", 38)(8, \"div\", 8)(9, \"label\", 39);\n    i0.ɵɵelement(10, \"i\", 40);\n    i0.ɵɵtext(11, \" Email or Mobile Number \");\n    i0.ɵɵelement(12, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 41);\n    i0.ɵɵlistener(\"blur\", function LoginComponent_div_3_Template_input_blur_13_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"input\", ctx_r1.forgotPasswordForm));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, LoginComponent_div_3_div_14_Template, 2, 1, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, LoginComponent_div_3_div_15_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementStart(16, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_3_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleNextStepAndSendCode());\n    });\n    i0.ɵɵtemplate(17, LoginComponent_div_3_span_17_Template, 1, 0, \"span\", 25);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 32)(20, \"a\", 43);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_3_Template_a_click_20_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.backToLogin());\n    });\n    i0.ɵɵelement(21, \"i\", 44);\n    i0.ɵɵtext(22, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 32);\n    i0.ɵɵtext(24, \" Need help? \");\n    i0.ɵɵelementStart(25, \"a\", 45);\n    i0.ɵɵtext(26, \"Contact us\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.forgotPasswordForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"input\", ctx_r1.forgotPasswordForm));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"input\", ctx_r1.forgotPasswordForm));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.otpErrorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingSendOtp);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isForgotPasswordFormValid() || ctx_r1.isLoadingSendOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingSendOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoadingSendOtp ? \"Sending...\" : \"Send Verification Code\", \" \");\n  }\n}\nfunction LoginComponent_div_4_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"input\", 53);\n    i0.ɵɵlistener(\"input\", function LoginComponent_div_4_div_7_Template_input_input_1_listener($event) {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.autoFocusNext($event, i_r6));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctrl_r7 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"is-invalid\", ctrl_r7.invalid && ctrl_r7.touched);\n    i0.ɵɵproperty(\"formControlName\", i_r6);\n  }\n}\nfunction LoginComponent_div_4_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1, \" Resend in \");\n    i0.ɵɵelementStart(2, \"span\", 55);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" 0:\", ctx_r1.countdown < 10 ? \"0\" + ctx_r1.countdown : ctx_r1.countdown, \" \");\n  }\n}\nfunction LoginComponent_div_4_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_4_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onResendCode());\n    });\n    i0.ɵɵtext(1, \" Resend Code \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_4_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otpErrorMessage, \" \");\n  }\n}\nfunction LoginComponent_div_4_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36);\n  }\n}\nfunction LoginComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"h1\", 6);\n    i0.ɵɵtext(4, \"Enter Verification Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 46)(6, \"div\", 47);\n    i0.ɵɵtemplate(7, LoginComponent_div_4_div_7_Template, 2, 3, \"div\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 49);\n    i0.ɵɵtemplate(9, LoginComponent_div_4_span_9_Template, 4, 1, \"span\", 50)(10, LoginComponent_div_4_button_10_Template, 2, 0, \"button\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, LoginComponent_div_4_div_11_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementStart(12, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_4_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.checkOTP());\n    });\n    i0.ɵɵtemplate(13, LoginComponent_div_4_span_13_Template, 1, 0, \"span\", 25);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 32)(16, \"a\", 43);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_4_Template_a_click_16_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.backToForgotPassword());\n    });\n    i0.ɵɵelement(17, \"i\", 44);\n    i0.ɵɵtext(18, \" Back to Forgot Password \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 32);\n    i0.ɵɵtext(20, \" Need help? \");\n    i0.ɵɵelementStart(21, \"span\", 33);\n    i0.ɵɵtext(22, \"Contact us\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.forgotPasswordForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.verificationCodeControls);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showResendButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showResendButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.otpErrorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isVerificationCodeValid() || ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoadingCheckOtp ? \"Verifying...\" : \"Verified - Next\", \" \");\n  }\n}\nfunction LoginComponent_div_5_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"newPassword\", ctx_r1.resetPasswordForm), \" \");\n  }\n}\nfunction LoginComponent_div_5_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"password_confirmation\", ctx_r1.resetPasswordForm), \" \");\n  }\n}\nfunction LoginComponent_div_5_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFormError(ctx_r1.resetPasswordForm), \" \");\n  }\n}\nfunction LoginComponent_div_5_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.resetPasswordErrorMessage, \" \");\n  }\n}\nfunction LoginComponent_div_5_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36);\n  }\n}\nfunction LoginComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"h1\", 6);\n    i0.ɵɵtext(4, \"Reset Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 37);\n    i0.ɵɵtext(6, \"Enter your new password for your account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"form\", 38)(8, \"div\", 8)(9, \"label\", 57);\n    i0.ɵɵelement(10, \"i\", 15);\n    i0.ɵɵtext(11, \" New Password \");\n    i0.ɵɵelement(12, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 58);\n    i0.ɵɵlistener(\"blur\", function LoginComponent_div_5_Template_input_blur_13_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"newPassword\", ctx_r1.resetPasswordForm));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, LoginComponent_div_5_div_14_Template, 2, 1, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 8)(16, \"label\", 59);\n    i0.ɵɵelement(17, \"i\", 15);\n    i0.ɵɵtext(18, \" Confirm Password \");\n    i0.ɵɵelement(19, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 60);\n    i0.ɵɵlistener(\"blur\", function LoginComponent_div_5_Template_input_blur_20_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"password_confirmation\", ctx_r1.resetPasswordForm));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, LoginComponent_div_5_div_21_Template, 2, 1, \"div\", 13)(22, LoginComponent_div_5_div_22_Template, 2, 1, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, LoginComponent_div_5_div_23_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementStart(24, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_5_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.submitResetPassword());\n    });\n    i0.ɵɵtemplate(25, LoginComponent_div_5_span_25_Template, 1, 0, \"span\", 25);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 32)(28, \"a\", 43);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_5_Template_a_click_28_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.backToVerificationCode());\n    });\n    i0.ɵɵelement(29, \"i\", 44);\n    i0.ɵɵtext(30, \" Back to Verification Code \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.resetPasswordForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"newPassword\", ctx_r1.resetPasswordForm));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"newPassword\", ctx_r1.resetPasswordForm));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"password_confirmation\", ctx_r1.resetPasswordForm) || ctx_r1.getFormError(ctx_r1.resetPasswordForm));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"password_confirmation\", ctx_r1.resetPasswordForm));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFormError(ctx_r1.resetPasswordForm));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resetPasswordErrorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingResetPassword);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isResetPasswordFormValid() || ctx_r1.isLoadingResetPassword);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingResetPassword);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoadingResetPassword ? \"Saving...\" : \"Save New Password\", \" \");\n  }\n}\nexport class LoginComponent {\n  fb;\n  router;\n  authenticationService;\n  cd;\n  currentStep = 1;\n  loginForm;\n  forgotPasswordForm;\n  resetPasswordForm;\n  isLoading = false;\n  verificationDigits = ['', '', '', '', ''];\n  countdown = 25;\n  showResendButton = false;\n  // Loading states\n  isLoadingLogin = false;\n  isLoadingSendOtp = false;\n  isLoadingCheckOtp = false;\n  isLoadingResetPassword = false;\n  // Error messages\n  loginErrorMessage = '';\n  otpErrorMessage = '';\n  resetPasswordErrorMessage = '';\n  // Validators\n  static phonePattern = Validators.pattern(/^(01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\n  static emailOrPhonePattern = Validators.pattern(/^([^\\s@]+@[^\\s@]+\\.[^\\s@]+|01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\n  static passwordPattern = Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/);\n  constructor(fb, router, authenticationService, cd) {\n    this.fb = fb;\n    this.router = router;\n    this.authenticationService = authenticationService;\n    this.cd = cd;\n    this.loginForm = this.createLoginForm();\n    this.forgotPasswordForm = this.createForgotPasswordForm();\n    this.resetPasswordForm = this.createResetPasswordForm();\n  }\n  createLoginForm() {\n    return this.fb.group({\n      phone: ['', [Validators.required, LoginComponent.phonePattern]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      rememberMe: [false]\n    });\n  }\n  createForgotPasswordForm() {\n    return this.fb.group({\n      input: ['', [Validators.required, LoginComponent.emailOrPhonePattern]],\n      verificationCode: this.fb.array(Array(5).fill('').map(() => this.fb.control('', [Validators.required, Validators.pattern('[0-9]')])))\n    });\n  }\n  createResetPasswordForm() {\n    return this.fb.group({\n      newPassword: ['', [Validators.required, Validators.minLength(8), LoginComponent.passwordPattern]],\n      password_confirmation: ['', [Validators.required]]\n    });\n  }\n  ngOnInit() {}\n  // Helper methods for validation\n  isFieldInvalid(fieldName, formGroup) {\n    const form = formGroup || this.loginForm;\n    const field = form.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  markFieldAsTouched(fieldName, formGroup) {\n    const form = formGroup || this.loginForm;\n    form.get(fieldName)?.markAsTouched();\n  }\n  getFieldError(fieldName, formGroup) {\n    const form = formGroup || this.loginForm;\n    const field = form.get(fieldName);\n    if (!field?.errors) return '';\n    const errors = field.errors;\n    if (errors['required']) return 'This field is required';\n    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';\n    if (errors['pattern'] && fieldName === 'input') return 'Enter valid email or phone number';\n    if (errors['pattern'] && fieldName === 'newPassword') return 'Need uppercase, lowercase & number';\n    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;\n    return 'Invalid input';\n  }\n  getFormError(formGroup) {\n    const password = formGroup.get('newPassword')?.value;\n    const confirmPassword = formGroup.get('password_confirmation')?.value;\n    if (password && confirmPassword && password !== confirmPassword) {\n      return 'Passwords do not match';\n    }\n    return '';\n  }\n  // Check if forms are valid\n  isLoginFormValid() {\n    return this.loginForm.valid;\n  }\n  isForgotPasswordFormValid() {\n    const input = this.forgotPasswordForm.get('input');\n    return !!input?.valid;\n  }\n  isVerificationCodeValid() {\n    const verificationCode = this.forgotPasswordForm.get('verificationCode');\n    return verificationCode.valid;\n  }\n  isResetPasswordFormValid() {\n    const newPassword = this.resetPasswordForm.get('newPassword');\n    const passwordConfirmation = this.resetPasswordForm.get('password_confirmation');\n    const passwordsMatch = newPassword?.value === passwordConfirmation?.value;\n    return !!(newPassword?.valid && passwordConfirmation?.valid && passwordsMatch);\n  }\n  login() {\n    if (!this.isLoginFormValid()) {\n      this.markFieldAsTouched('phone');\n      this.markFieldAsTouched('password');\n      return;\n    }\n    this.isLoadingLogin = true;\n    this.loginErrorMessage = '';\n    let params = this.loginForm.value;\n    this.authenticationService.login(params).subscribe({\n      next: response => {\n        let user = response.data;\n        localStorage.setItem('authToken', user.authToken);\n        this.authenticationService.setCurrentUser(response.data);\n        console.log('login successfully:', response);\n        this.isLoadingLogin = false;\n        if (user.role == 'developer') {\n          this.router.navigate(['/developer/dashboards']);\n        } else if (user.role == 'broker') {\n          this.router.navigate(['/broker/dashboard']);\n        } else if (user.role == 'client') {\n          this.router.navigate(['/requests']);\n        } else if (user.role == 'admin') {\n          this.router.navigate(['/super-admin/dashboard']);\n        }\n        this.cd.markForCheck();\n      },\n      error: error => {\n        console.error('Failed to login:', error);\n        this.isLoadingLogin = false;\n        this.loginErrorMessage = error?.error?.message || 'Login failed. Please check your credentials.';\n        this.cd.markForCheck();\n      }\n    });\n  }\n  get verificationCodeControls() {\n    return this.forgotPasswordForm.get('verificationCode').controls;\n  }\n  goToForgotPassword() {\n    this.currentStep = 2;\n  }\n  backToLogin() {\n    this.currentStep = 1;\n  }\n  backToForgotPassword() {\n    this.currentStep = 2;\n  }\n  backToVerificationCode() {\n    this.currentStep = 3;\n  }\n  nextStep() {\n    if (this.currentStep < 4) {\n      // Clear error messages when moving between steps\n      if (this.currentStep === 2) {\n        this.otpErrorMessage = '';\n      } else if (this.currentStep === 3) {\n        this.otpErrorMessage = '';\n      }\n      this.currentStep++;\n    }\n  }\n  submitResetPassword() {\n    var _this = this;\n    if (!this.isResetPasswordFormValid()) {\n      this.markFieldAsTouched('newPassword', this.resetPasswordForm);\n      this.markFieldAsTouched('password_confirmation', this.resetPasswordForm);\n      return;\n    }\n    this.isLoadingResetPassword = true;\n    this.resetPasswordErrorMessage = '';\n    const input = this.forgotPasswordForm.get('input')?.value?.trim();\n    let params = {};\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\n    if (isEmail) {\n      params.email = input;\n    } else if (isPhone) {\n      params.phone = input;\n    }\n    params.password = this.resetPasswordForm.get('newPassword')?.value?.trim();\n    params.password_confirmation = this.resetPasswordForm.get('password_confirmation')?.value?.trim();\n    this.authenticationService.resetPassword(params).subscribe({\n      next: function () {\n        var _ref = _asyncToGenerator(function* (response) {\n          console.log('reset password success:', response);\n          _this.isLoadingResetPassword = false;\n          // Navigate back to login instead of reloading\n          _this.currentStep = 1;\n          _this.loginErrorMessage = 'Password reset successfully! Please login with your new password.';\n          _this.cd.markForCheck();\n        });\n        return function next(_x) {\n          return _ref.apply(this, arguments);\n        };\n      }(),\n      error: err => {\n        console.error('Failed to reset:', err);\n        this.isLoadingResetPassword = false;\n        this.resetPasswordErrorMessage = err?.error?.message || 'Failed to reset password. Please try again.';\n        this.cd.markForCheck();\n      }\n    });\n  }\n  clearOtpInputs() {\n    this.verificationDigits = ['', '', '', '', ''];\n    const verificationCodeArray = this.forgotPasswordForm.get('verificationCode');\n    verificationCodeArray.controls.forEach(control => {\n      control.setValue('');\n      control.markAsUntouched();\n      control.markAsPristine();\n    });\n  }\n  handleNextStepAndSendCode() {\n    this.sendVerificationCode(true);\n  }\n  sendVerificationCode(moveToNextStep = false) {\n    if (!this.isForgotPasswordFormValid()) {\n      this.markFieldAsTouched('input', this.forgotPasswordForm);\n      return;\n    }\n    this.isLoadingSendOtp = true;\n    this.otpErrorMessage = '';\n    const input = this.forgotPasswordForm.get('input')?.value?.trim();\n    let params = {};\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\n    if (isEmail) {\n      params.email = input;\n    } else if (isPhone) {\n      params.phone = input;\n    }\n    this.authenticationService.sendOtp(params).subscribe({\n      next: response => {\n        console.log('OTP sent:', response);\n        this.isLoadingSendOtp = false;\n        this.startCountdown();\n        if (moveToNextStep) {\n          this.nextStep();\n        }\n        this.cd.markForCheck();\n      },\n      error: error => {\n        console.error('Failed to send OTP:', error);\n        this.isLoadingSendOtp = false;\n        this.otpErrorMessage = error?.error?.message || 'Failed to send verification code. Please try again.';\n        this.cd.markForCheck();\n      }\n    });\n  }\n  checkOTP() {\n    if (!this.isVerificationCodeValid()) {\n      const verificationCodeArray = this.forgotPasswordForm.get('verificationCode');\n      verificationCodeArray.controls.forEach(control => control.markAsTouched());\n      return;\n    }\n    this.isLoadingCheckOtp = true;\n    this.otpErrorMessage = '';\n    const input = this.forgotPasswordForm.get('input')?.value?.trim();\n    const codeArray = this.forgotPasswordForm.get('verificationCode')?.value;\n    const otp = codeArray.join('');\n    let params = {};\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\n    if (isEmail) {\n      params.email = input;\n    } else if (isPhone) {\n      params.phone = input;\n    }\n    params.otp = otp;\n    this.authenticationService.checkOtp(params).subscribe({\n      next: response => {\n        console.log('OTP checked:', response);\n        this.isLoadingCheckOtp = false;\n        this.nextStep();\n        this.cd.markForCheck();\n      },\n      error: error => {\n        console.error('Failed to check OTP:', error);\n        this.isLoadingCheckOtp = false;\n        this.otpErrorMessage = error?.error?.message || 'Invalid verification code. Please try again.';\n        this.cd.markForCheck();\n      }\n    });\n  }\n  startCountdown() {\n    this.showResendButton = false;\n    this.countdown = 25;\n    const intervalId = setInterval(() => {\n      this.countdown--;\n      if (this.countdown === 0) {\n        clearInterval(intervalId);\n        this.showResendButton = true;\n      }\n      this.cd.markForCheck();\n    }, 1000);\n  }\n  autoFocusNext(event, index) {\n    const input = event.target;\n    if (input.value && index < 5) {\n      const nextInput = input.parentElement?.nextElementSibling?.querySelector('input');\n      nextInput?.focus();\n    }\n  }\n  onResendCode() {\n    this.clearOtpInputs();\n    this.otpErrorMessage = '';\n    this.sendVerificationCode(false);\n  }\n  static ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthenticationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 6,\n    vars: 4,\n    consts: [[1, \"register-container\"], [1, \"register-card\"], [\"class\", \"registration-stepper-step\", 4, \"ngIf\"], [1, \"registration-stepper-step\"], [1, \"login-stepper\"], [1, \"step-content\"], [1, \"step-title\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-group\"], [\"for\", \"phone\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-user\"], [1, \"required\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", \"placeholder\", \"01xxxxxxxxx\", \"required\", \"\", \"autocomplete\", \"tel\", 1, \"form-control\", 3, \"blur\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-lock\"], [\"type\", \"password\", \"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"required\", \"\", \"autocomplete\", \"current-password\", 1, \"form-control\", 3, \"blur\"], [1, \"form-row\"], [1, \"forgot-password-link\"], [1, \"forgot-link\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"rememberMe\", \"formControlName\", \"rememberMe\", 1, \"form-check-input\"], [\"for\", \"rememberMe\", 1, \"form-check-label\"], [\"class\", \"alert alert-danger mt-3\", \"role\", \"alert\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-verification\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [1, \"help-text\", \"register-section\"], [1, \"register-text\"], [\"routerLink\", \"../register\", 1, \"register-link\"], [1, \"register-message\"], [1, \"create-link\"], [1, \"ki-outline\", \"ki-arrow-right\"], [1, \"help-text\"], [1, \"contact-link\"], [1, \"invalid-feedback\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"mt-3\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"step-subtitle\"], [3, \"formGroup\"], [\"for\", \"forgot-email\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-phone\"], [\"type\", \"text\", \"id\", \"forgot-email\", \"formControlName\", \"input\", \"placeholder\", \"Enter your email or mobile number\", \"required\", \"\", 1, \"form-control\", 3, \"blur\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-verification\", 3, \"click\", \"disabled\"], [1, \"back-link\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"ki-outline\", \"ki-arrow-left\"], [\"href\", \"#\", 1, \"contact-link\"], [1, \"verification-code-section\", 3, \"formGroup\"], [\"formArrayName\", \"verificationCode\", 1, \"verification-inputs\"], [\"class\", \"code-input\", 4, \"ngFor\", \"ngForOf\"], [1, \"countdown-section\"], [\"class\", \"countdown-text\", 4, \"ngIf\"], [\"class\", \"btn btn-link\", 3, \"click\", 4, \"ngIf\"], [1, \"code-input\"], [\"type\", \"text\", \"maxlength\", \"1\", 1, \"verification-input\", 3, \"input\", \"formControlName\"], [1, \"countdown-text\"], [1, \"countdown-timer\"], [1, \"btn\", \"btn-link\", 3, \"click\"], [\"for\", \"new-password\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"new-password\", \"formControlName\", \"newPassword\", \"placeholder\", \"Enter your new password\", \"minlength\", \"8\", \"pattern\", \"^(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\d).{8,}$\", \"title\", \"Password must be at least 8 characters with uppercase, lowercase and number\", \"required\", \"\", \"autocomplete\", \"new-password\", 1, \"form-control\", 3, \"blur\"], [\"for\", \"confirm-password\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"confirm-password\", \"formControlName\", \"password_confirmation\", \"placeholder\", \"Re-enter your password\", \"required\", \"\", \"autocomplete\", \"new-password\", 1, \"form-control\", 3, \"blur\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, LoginComponent_div_2_Template, 44, 13, \"div\", 2)(3, LoginComponent_div_3_Template, 27, 10, \"div\", 2)(4, LoginComponent_div_4_Template, 23, 10, \"div\", 2)(5, LoginComponent_div_5_Template, 31, 14, \"div\", 2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MinLengthValidator, i1.MaxLengthValidator, i1.PatternValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormArrayName, i2.RouterLink],\n    styles: [\".required[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  font-weight: bold;\\n  margin-left: 2px;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  margin-bottom: 16px;\\n  border: 1px solid transparent;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  text-align: center;\\n  direction: ltr;\\n}\\n.alert.alert-danger[_ngcontent-%COMP%] {\\n  color: #721c24;\\n  background-color: #f8d7da;\\n  border-color: #f5c6cb;\\n}\\n\\n[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: 100vh;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  z-index: 9999;\\n}\\n\\n.register-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n  background-image: url(\\\"/angular/assets/media/login/EaseDealPage.png\\\");\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n  padding: 20px 20px 20px 0;\\n  font-family: \\\"Segoe UI\\\", Tahoma, Geneva, Verdana, sans-serif;\\n  direction: ltr;\\n  text-align: left;\\n  position: relative;\\n}\\n\\n.register-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 450px;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 15px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_slideUp 0.6s ease-out;\\n  position: relative;\\n  z-index: 9999;\\n  margin-left: auto;\\n  margin-right: 100px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1);\\n  padding: 40px;\\n  direction: ltr;\\n  text-align: left;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.user-type-selection[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .form-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  line-height: 1.5;\\n  margin-bottom: 30px;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .user-type-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  gap: 15px;\\n  margin-bottom: 30px;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .user-type-options[_ngcontent-%COMP%]   .user-type-option[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #f8f9fa;\\n  border: 2px solid #e9ecef;\\n  border-radius: 15px;\\n  padding: 10px 15px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .user-type-options[_ngcontent-%COMP%]   .user-type-option[_ngcontent-%COMP%]:hover {\\n  border-color: #667eea;\\n  background: #f0f2ff;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .user-type-options[_ngcontent-%COMP%]   .user-type-option.selected[_ngcontent-%COMP%] {\\n  border-color: #667eea;\\n  background: #2a44b6;\\n  color: white;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .user-type-options[_ngcontent-%COMP%]   .user-type-option.selected[_ngcontent-%COMP%]   .option-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .user-type-options[_ngcontent-%COMP%]   .user-type-option[_ngcontent-%COMP%]   .option-icon[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .user-type-options[_ngcontent-%COMP%]   .user-type-option[_ngcontent-%COMP%]   .option-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #2a44b6;\\n  transition: color 0.3s ease;\\n  font-size: 1.2rem;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .user-type-options[_ngcontent-%COMP%]   .user-type-option[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 0.9rem;\\n  margin: 0;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .continue-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px;\\n  background: #1e35a6;\\n  border: none;\\n  border-radius: 25px;\\n  color: white;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: background-color 0.3s ease;\\n  margin-bottom: 20px;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .continue-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #667eea;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .continue-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .login-link-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 15px;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .login-link-container[_ngcontent-%COMP%]   .login-text[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .login-link-container[_ngcontent-%COMP%]   .login-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .login-link-container[_ngcontent-%COMP%]   .login-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .login-link-container[_ngcontent-%COMP%]   .login-link[_ngcontent-%COMP%]:hover {\\n  color: #5a6fd8;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .support-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .support-link[_ngcontent-%COMP%]   .support-text[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n}\\n.user-type-selection[_ngcontent-%COMP%]   .support-link[_ngcontent-%COMP%]   .support-text[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.login-stepper[_ngcontent-%COMP%]   .back-to-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .back-to-selection[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #667eea;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 5px 0;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .back-to-selection[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  color: #5a6fd8;\\n  text-decoration: underline;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .back-to-selection[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  min-height: 50px;\\n  margin-bottom: 5px;\\n  text-align: center;\\n  padding: 2px 5px;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  color: #232176;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n  text-align: center;\\n  padding-bottom: 8px;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .login-title[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  margin-bottom: 10px;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .login-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 25px;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  text-align: left;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 1rem;\\n  justify-content: flex-start;\\n  text-align: left;\\n  width: 100%;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1rem;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 15px;\\n  border: 1px solid #ddd;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  transition: border-color 0.3s ease;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #dc3545;\\n  font-size: 12px;\\n  margin-top: 5px;\\n  font-weight: 500;\\n  text-align: left;\\n  direction: ltr;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  text-align: left;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  cursor: pointer;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 15px;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]   .forgot-link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n}\\n.login-stepper[_ngcontent-%COMP%]   .login-form-content[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]   .forgot-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.registration-stepper-step[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  text-align: left;\\n  width: 100%;\\n}\\n.form-group[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 1rem;\\n  justify-content: flex-start;\\n  text-align: left;\\n  width: 100%;\\n}\\n.form-group[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  font-size: 1rem;\\n}\\n\\n.progress-bar-container[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n  height: 8px;\\n  background: #e9ecef;\\n  border-radius: 4px;\\n}\\n.progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #28a745;\\n  border-radius: 2px;\\n}\\n\\n.back-to-login[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 15px;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #667eea;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 5px 0;\\n  margin: 0 auto;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  color: #5a6fd8;\\n  text-decoration: underline;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%] {\\n  width: 424.39px;\\n  padding: 18px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  border-radius: 25px;\\n  margin: 30px auto 35px;\\n  background: #1e35a6;\\n  color: white;\\n  text-align: center;\\n  border: none;\\n  cursor: pointer;\\n  transition: background-color 0.3s ease;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #667eea;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .btn-verification.loading[_ngcontent-%COMP%] {\\n  position: relative;\\n  pointer-events: none;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .btn-verification.loading[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n  border-width: 0.125em;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 1rem;\\n  height: 1rem;\\n  vertical-align: text-bottom;\\n  border: 0.125em solid currentColor;\\n  border-right-color: transparent;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spinner-border 0.75s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_spinner-border {\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.back-to-login[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 25px;\\n  margin-bottom: 20px;\\n  font-size: 13px;\\n  color: #6c757d;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  cursor: pointer;\\n  font-weight: 500;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   .register-link[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   .register-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .register-section[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  margin-bottom: 15px;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .register-section[_ngcontent-%COMP%]   .register-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .register-section[_ngcontent-%COMP%]   .register-text[_ngcontent-%COMP%]   .register-link[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  text-decoration: none;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  transition: all 0.3s ease;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .register-section[_ngcontent-%COMP%]   .register-text[_ngcontent-%COMP%]   .register-link[_ngcontent-%COMP%]   .register-message[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 13px;\\n  font-weight: 400;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .register-section[_ngcontent-%COMP%]   .register-text[_ngcontent-%COMP%]   .register-link[_ngcontent-%COMP%]   .register-message[_ngcontent-%COMP%]   .create-link[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-weight: 500;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .register-section[_ngcontent-%COMP%]   .register-text[_ngcontent-%COMP%]   .register-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #6c757d;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .register-section[_ngcontent-%COMP%]   .register-text[_ngcontent-%COMP%]   .register-link[_ngcontent-%COMP%]:hover   .register-message[_ngcontent-%COMP%]   .create-link[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .register-section[_ngcontent-%COMP%]   .register-text[_ngcontent-%COMP%]   .register-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateX(2px);\\n  color: #007bff;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  width: 100%;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%]   .forgot-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%]   .forgot-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  text-align: right;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  cursor: pointer;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  width: 100%;\\n}\\n.form-row[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n.form-row[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%]   .forgot-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.form-row[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%]   .forgot-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.form-row[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  text-align: right;\\n}\\n.form-row[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n.form-row[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  cursor: pointer;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  width: 100%;\\n}\\n.form-row[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n.form-row[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%]   .forgot-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.form-row[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%]   .forgot-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.form-row[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  text-align: right;\\n}\\n.form-row[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n.form-row[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  cursor: pointer;\\n}\\n\\n.step-subtitle[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n  text-align: center;\\n  line-height: 1.5;\\n}\\n\\n.back-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  justify-content: center;\\n  margin: 15px 0;\\n}\\n.back-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.back-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n.contact-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.contact-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.verification-code-section[_ngcontent-%COMP%] {\\n  margin: 15px 0;\\n  text-align: center;\\n}\\n.verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 12px;\\n}\\n.verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  text-align: center;\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n  border: 2px solid #ddd;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  color: #333;\\n  transition: all 0.3s ease;\\n}\\n.verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\\n}\\n.verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]::-webkit-outer-spin-button, .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]::-webkit-inner-spin-button {\\n  -webkit-appearance: none;\\n  margin: 0;\\n}\\n.verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[type=number][_ngcontent-%COMP%] {\\n  -moz-appearance: textfield;\\n}\\n\\n.countdown-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 25px 0;\\n}\\n.countdown-section[_ngcontent-%COMP%]   .countdown-text[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n.countdown-section[_ngcontent-%COMP%]   .countdown-text[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-weight: bold;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .register-container[_ngcontent-%COMP%] {\\n    padding: 10px 10px 10px 0;\\n    justify-content: flex-start;\\n  }\\n  .register-card[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    border-radius: 10px;\\n    margin-left: auto;\\n    margin-right: auto;\\n    padding: 30px 20px;\\n  }\\n  .user-type-selection[_ngcontent-%COMP%]   .user-type-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n  .user-type-selection[_ngcontent-%COMP%]   .user-type-options[_ngcontent-%COMP%]   .user-type-option[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .register-card[_ngcontent-%COMP%] {\\n    margin: 0 10px;\\n    max-width: calc(100% - 20px);\\n    padding: 20px 15px;\\n  }\\n  .user-type-selection[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .user-type-selection[_ngcontent-%COMP%]   .form-subtitle[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "getFieldError", "loginErrorMessage", "ɵɵelement", "ɵɵlistener", "LoginComponent_div_2_Template_form_ngSubmit_5_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "login", "LoginComponent_div_2_Template_input_blur_11_listener", "mark<PERSON>ieldAsTouched", "ɵɵtemplate", "LoginComponent_div_2_div_12_Template", "LoginComponent_div_2_Template_input_blur_18_listener", "LoginComponent_div_2_div_19_Template", "LoginComponent_div_2_Template_a_click_22_listener", "goToForgotPassword", "LoginComponent_div_2_div_28_Template", "LoginComponent_div_2_Template_button_click_29_listener", "LoginComponent_div_2_span_30_Template", "ɵɵproperty", "loginForm", "ɵɵclassProp", "isFieldInvalid", "isLoadingLogin", "isLoginFormValid", "forgotPasswordForm", "otpErrorMessage", "LoginComponent_div_3_Template_input_blur_13_listener", "_r3", "LoginComponent_div_3_div_14_Template", "LoginComponent_div_3_div_15_Template", "LoginComponent_div_3_Template_button_click_16_listener", "handleNextStepAndSendCode", "LoginComponent_div_3_span_17_Template", "LoginComponent_div_3_Template_a_click_20_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isLoadingSendOtp", "isForgotPasswordFormValid", "LoginComponent_div_4_div_7_Template_input_input_1_listener", "$event", "i_r6", "_r5", "index", "autoFocusNext", "ctrl_r7", "invalid", "touched", "countdown", "LoginComponent_div_4_button_10_Template_button_click_0_listener", "_r8", "onResendCode", "LoginComponent_div_4_div_7_Template", "LoginComponent_div_4_span_9_Template", "LoginComponent_div_4_button_10_Template", "LoginComponent_div_4_div_11_Template", "LoginComponent_div_4_Template_button_click_12_listener", "_r4", "checkOTP", "LoginComponent_div_4_span_13_Template", "LoginComponent_div_4_Template_a_click_16_listener", "backToForgotPassword", "verificationCodeControls", "showResendButton", "isLoadingCheckOtp", "isVerificationCodeValid", "resetPasswordForm", "getFormError", "resetPasswordErrorMessage", "LoginComponent_div_5_Template_input_blur_13_listener", "_r9", "LoginComponent_div_5_div_14_Template", "LoginComponent_div_5_Template_input_blur_20_listener", "LoginComponent_div_5_div_21_Template", "LoginComponent_div_5_div_22_Template", "LoginComponent_div_5_div_23_Template", "LoginComponent_div_5_Template_button_click_24_listener", "submitResetPassword", "LoginComponent_div_5_span_25_Template", "LoginComponent_div_5_Template_a_click_28_listener", "backToVerificationCode", "isLoadingResetPassword", "isResetPasswordFormValid", "LoginComponent", "fb", "router", "authenticationService", "cd", "currentStep", "isLoading", "verificationDigits", "phonePattern", "pattern", "emailOrPhonePattern", "passwordPattern", "constructor", "createLoginForm", "createForgotPasswordForm", "createResetPasswordForm", "group", "phone", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "rememberMe", "input", "verificationCode", "array", "Array", "fill", "map", "control", "newPassword", "password_confirmation", "ngOnInit", "fieldName", "formGroup", "form", "field", "get", "dirty", "<PERSON><PERSON><PERSON><PERSON>ched", "errors", "<PERSON><PERSON><PERSON><PERSON>", "value", "confirmPassword", "valid", "passwordConfirmation", "passwordsMatch", "params", "subscribe", "next", "response", "user", "data", "localStorage", "setItem", "authToken", "setCurrentUser", "console", "log", "role", "navigate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "message", "controls", "nextStep", "_this", "trim", "isEmail", "test", "isPhone", "email", "resetPassword", "_ref", "_asyncToGenerator", "_x", "apply", "arguments", "err", "clearOtpInputs", "verificationCodeArray", "for<PERSON>ach", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mark<PERSON><PERSON>ristine", "sendVerificationCode", "moveToNextStep", "sendOtp", "startCountdown", "codeArray", "otp", "join", "checkOtp", "intervalId", "setInterval", "clearInterval", "event", "target", "nextInput", "parentElement", "nextElement<PERSON><PERSON>ling", "querySelector", "focus", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "AuthenticationService", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "LoginComponent_div_2_Template", "LoginComponent_div_3_Template", "LoginComponent_div_4_Template", "LoginComponent_div_5_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\components\\login\\login.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit } from '@angular/core';\r\nimport { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { AuthenticationService } from '../../services/authentication.service';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss'],\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  currentStep = 1;\r\n  loginForm: FormGroup;\r\n  forgotPasswordForm: FormGroup;\r\n  resetPasswordForm: FormGroup;\r\n  isLoading = false;\r\n  verificationDigits: string[] = ['', '', '', '', ''];\r\n\r\n  countdown: number = 25;\r\n  showResendButton: boolean = false;\r\n\r\n  // Loading states\r\n  isLoadingLogin: boolean = false;\r\n  isLoadingSendOtp: boolean = false;\r\n  isLoadingCheckOtp: boolean = false;\r\n  isLoadingResetPassword: boolean = false;\r\n\r\n  // Error messages\r\n  loginErrorMessage: string = '';\r\n  otpErrorMessage: string = '';\r\n  resetPasswordErrorMessage: string = '';\r\n\r\n  // Validators\r\n  static phonePattern = Validators.pattern(/^(01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\r\n  static emailOrPhonePattern = Validators.pattern(/^([^\\s@]+@[^\\s@]+\\.[^\\s@]+|01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\r\n  static passwordPattern = Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/);\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private authenticationService: AuthenticationService,\r\n    private cd: ChangeDetectorRef\r\n  ) {\r\n    this.loginForm = this.createLoginForm();\r\n    this.forgotPasswordForm = this.createForgotPasswordForm();\r\n    this.resetPasswordForm = this.createResetPasswordForm();\r\n  }\r\n\r\n  private createLoginForm(): FormGroup {\r\n    return this.fb.group({\r\n      phone: ['', [Validators.required, LoginComponent.phonePattern]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      rememberMe: [false],\r\n    });\r\n  }\r\n\r\n  private createForgotPasswordForm(): FormGroup {\r\n    return this.fb.group({\r\n      input: ['', [Validators.required, LoginComponent.emailOrPhonePattern]],\r\n      verificationCode: this.fb.array(\r\n        Array(5)\r\n          .fill('')\r\n          .map(() =>\r\n            this.fb.control('', [\r\n              Validators.required,\r\n              Validators.pattern('[0-9]'),\r\n            ])\r\n          )\r\n      ),\r\n    });\r\n  }\r\n\r\n  private createResetPasswordForm(): FormGroup {\r\n    return this.fb.group({\r\n      newPassword: ['', [Validators.required, Validators.minLength(8), LoginComponent.passwordPattern]],\r\n      password_confirmation: ['', [Validators.required]],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {}\r\n\r\n  // Helper methods for validation\r\n  isFieldInvalid(fieldName: string, formGroup?: FormGroup): boolean {\r\n    const form = formGroup || this.loginForm;\r\n    const field = form.get(fieldName);\r\n    return !!(field && field.invalid && (field.dirty || field.touched));\r\n  }\r\n\r\n  markFieldAsTouched(fieldName: string, formGroup?: FormGroup): void {\r\n    const form = formGroup || this.loginForm;\r\n    form.get(fieldName)?.markAsTouched();\r\n  }\r\n\r\n  getFieldError(fieldName: string, formGroup?: FormGroup): string {\r\n    const form = formGroup || this.loginForm;\r\n    const field = form.get(fieldName);\r\n    if (!field?.errors) return '';\r\n\r\n    const errors = field.errors;\r\n    if (errors['required']) return 'This field is required';\r\n    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';\r\n    if (errors['pattern'] && fieldName === 'input') return 'Enter valid email or phone number';\r\n    if (errors['pattern'] && fieldName === 'newPassword') return 'Need uppercase, lowercase & number';\r\n    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;\r\n\r\n    return 'Invalid input';\r\n  }\r\n\r\n  getFormError(formGroup: FormGroup): string {\r\n    const password = formGroup.get('newPassword')?.value;\r\n    const confirmPassword = formGroup.get('password_confirmation')?.value;\r\n\r\n    if (password && confirmPassword && password !== confirmPassword) {\r\n      return 'Passwords do not match';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Check if forms are valid\r\n  isLoginFormValid(): boolean {\r\n    return this.loginForm.valid;\r\n  }\r\n\r\n  isForgotPasswordFormValid(): boolean {\r\n    const input = this.forgotPasswordForm.get('input');\r\n    return !!input?.valid;\r\n  }\r\n\r\n  isVerificationCodeValid(): boolean {\r\n    const verificationCode = this.forgotPasswordForm.get('verificationCode') as FormArray;\r\n    return verificationCode.valid;\r\n  }\r\n\r\n  isResetPasswordFormValid(): boolean {\r\n    const newPassword = this.resetPasswordForm.get('newPassword');\r\n    const passwordConfirmation = this.resetPasswordForm.get('password_confirmation');\r\n    const passwordsMatch = newPassword?.value === passwordConfirmation?.value;\r\n\r\n    return !!(newPassword?.valid && passwordConfirmation?.valid && passwordsMatch);\r\n  }\r\n\r\n  login(): void {\r\n    if (!this.isLoginFormValid()) {\r\n      this.markFieldAsTouched('phone');\r\n      this.markFieldAsTouched('password');\r\n      return;\r\n    }\r\n\r\n    this.isLoadingLogin = true;\r\n    this.loginErrorMessage = '';\r\n    let params = this.loginForm.value;\r\n\r\n    this.authenticationService.login(params).subscribe({\r\n      next: (response: any) => {\r\n        let user = response.data;\r\n        localStorage.setItem('authToken', user.authToken);\r\n        this.authenticationService.setCurrentUser(response.data);\r\n        console.log('login successfully:', response);\r\n        this.isLoadingLogin = false;\r\n\r\n        if (user.role == 'developer') {\r\n          this.router.navigate(['/developer/dashboards']);\r\n        } else if (user.role == 'broker') {\r\n          this.router.navigate(['/broker/dashboard']);\r\n        } else if (user.role == 'client') {\r\n          this.router.navigate(['/requests']);\r\n        } else if (user.role == 'admin') {\r\n          this.router.navigate(['/super-admin/dashboard']);\r\n        }\r\n        this.cd.markForCheck();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Failed to login:', error);\r\n        this.isLoadingLogin = false;\r\n        this.loginErrorMessage = error?.error?.message || 'Login failed. Please check your credentials.';\r\n        this.cd.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  get verificationCodeControls() {\r\n    return (this.forgotPasswordForm.get('verificationCode') as FormArray)\r\n      .controls;\r\n  }\r\n\r\n  goToForgotPassword(): void {\r\n    this.currentStep = 2;\r\n  }\r\n  backToLogin(): void {\r\n    this.currentStep = 1;\r\n  }\r\n  backToForgotPassword(): void {\r\n    this.currentStep = 2;\r\n  }\r\n  backToVerificationCode(): void {\r\n    this.currentStep = 3;\r\n  }\r\n\r\n  nextStep(): void {\r\n    if (this.currentStep < 4) {\r\n      // Clear error messages when moving between steps\r\n      if (this.currentStep === 2) {\r\n        this.otpErrorMessage = '';\r\n      } else if (this.currentStep === 3) {\r\n        this.otpErrorMessage = '';\r\n      }\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  submitResetPassword() {\r\n    if (!this.isResetPasswordFormValid()) {\r\n      this.markFieldAsTouched('newPassword', this.resetPasswordForm);\r\n      this.markFieldAsTouched('password_confirmation', this.resetPasswordForm);\r\n      return;\r\n    }\r\n\r\n    this.isLoadingResetPassword = true;\r\n    this.resetPasswordErrorMessage = '';\r\n    const input = this.forgotPasswordForm.get('input')?.value?.trim();\r\n\r\n    let params: {\r\n      email?: string;\r\n      phone?: string;\r\n      password?: string;\r\n      password_confirmation?: string;\r\n    } = {};\r\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\r\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\r\n\r\n    if (isEmail) {\r\n      params.email = input;\r\n    } else if (isPhone) {\r\n      params.phone = input;\r\n    }\r\n    params.password = this.resetPasswordForm.get('newPassword')?.value?.trim();\r\n    params.password_confirmation = this.resetPasswordForm\r\n      .get('password_confirmation')\r\n      ?.value?.trim();\r\n\r\n    this.authenticationService.resetPassword(params).subscribe({\r\n      next: async (response) => {\r\n        console.log('reset password success:', response);\r\n        this.isLoadingResetPassword = false;\r\n        // Navigate back to login instead of reloading\r\n        this.currentStep = 1;\r\n        this.loginErrorMessage = 'Password reset successfully! Please login with your new password.';\r\n        this.cd.markForCheck();\r\n      },\r\n      error: (err) => {\r\n        console.error('Failed to reset:', err);\r\n        this.isLoadingResetPassword = false;\r\n        this.resetPasswordErrorMessage = err?.error?.message || 'Failed to reset password. Please try again.';\r\n        this.cd.markForCheck();\r\n      },\r\n    });\r\n  }\r\n\r\n  private clearOtpInputs() {\r\n    this.verificationDigits = ['', '', '', '', ''];\r\n    const verificationCodeArray = this.forgotPasswordForm.get('verificationCode') as FormArray;\r\n    verificationCodeArray.controls.forEach((control) => {\r\n      control.setValue('');\r\n      control.markAsUntouched();\r\n      control.markAsPristine();\r\n    });\r\n  }\r\n\r\n  handleNextStepAndSendCode(): void {\r\n    this.sendVerificationCode(true);\r\n  }\r\n\r\n  sendVerificationCode(moveToNextStep: boolean = false) {\r\n    if (!this.isForgotPasswordFormValid()) {\r\n      this.markFieldAsTouched('input', this.forgotPasswordForm);\r\n      return;\r\n    }\r\n\r\n    this.isLoadingSendOtp = true;\r\n    this.otpErrorMessage = '';\r\n    const input = this.forgotPasswordForm.get('input')?.value?.trim();\r\n\r\n    let params: { email?: string; phone?: string } = {};\r\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\r\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\r\n\r\n    if (isEmail) {\r\n      params.email = input;\r\n    } else if (isPhone) {\r\n      params.phone = input;\r\n    }\r\n\r\n    this.authenticationService.sendOtp(params).subscribe({\r\n      next: (response: any) => {\r\n        console.log('OTP sent:', response);\r\n        this.isLoadingSendOtp = false;\r\n        this.startCountdown();\r\n        if (moveToNextStep) {\r\n          this.nextStep();\r\n        }\r\n        this.cd.markForCheck();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Failed to send OTP:', error);\r\n        this.isLoadingSendOtp = false;\r\n        this.otpErrorMessage = error?.error?.message || 'Failed to send verification code. Please try again.';\r\n        this.cd.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  checkOTP() {\r\n    if (!this.isVerificationCodeValid()) {\r\n      const verificationCodeArray = this.forgotPasswordForm.get('verificationCode') as FormArray;\r\n      verificationCodeArray.controls.forEach(control => control.markAsTouched());\r\n      return;\r\n    }\r\n\r\n    this.isLoadingCheckOtp = true;\r\n    this.otpErrorMessage = '';\r\n    const input = this.forgotPasswordForm.get('input')?.value?.trim();\r\n    const codeArray = this.forgotPasswordForm.get('verificationCode')?.value;\r\n    const otp = codeArray.join('');\r\n\r\n    let params: { email?: string; phone?: string; otp?: number } = {};\r\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(input);\r\n    const isPhone = /^[0-9+\\-\\s]{7,15}$/.test(input);\r\n\r\n    if (isEmail) {\r\n      params.email = input;\r\n    } else if (isPhone) {\r\n      params.phone = input;\r\n    }\r\n\r\n    params.otp = otp;\r\n\r\n    this.authenticationService.checkOtp(params).subscribe({\r\n      next: (response: any) => {\r\n        console.log('OTP checked:', response);\r\n        this.isLoadingCheckOtp = false;\r\n        this.nextStep();\r\n        this.cd.markForCheck();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Failed to check OTP:', error);\r\n        this.isLoadingCheckOtp = false;\r\n        this.otpErrorMessage = error?.error?.message || 'Invalid verification code. Please try again.';\r\n        this.cd.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  startCountdown() {\r\n    this.showResendButton = false;\r\n    this.countdown = 25;\r\n\r\n    const intervalId = setInterval(() => {\r\n      this.countdown--;\r\n      if (this.countdown === 0) {\r\n        clearInterval(intervalId);\r\n        this.showResendButton = true;\r\n      }\r\n      this.cd.markForCheck();\r\n    }, 1000);\r\n  }\r\n\r\n  autoFocusNext(event: any, index: number): void {\r\n    const input = event.target;\r\n    if (input.value && index < 5) {\r\n      const nextInput =\r\n        input.parentElement?.nextElementSibling?.querySelector('input');\r\n      nextInput?.focus();\r\n    }\r\n  }\r\n\r\n  onResendCode() {\r\n    this.clearOtpInputs();\r\n    this.otpErrorMessage = '';\r\n    this.sendVerificationCode(false);\r\n  }\r\n}\r\n", "<div class=\"register-container\">\r\n  <!-- Registration Form Card -->\r\n  <div class=\"register-card\">\r\n    <!-- Step 1: Login Form -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"registration-stepper-step\">\r\n      <div class=\"login-stepper\">\r\n        <div class=\"step-content\">\r\n          <h1 class=\"step-title\">Login to your account</h1>\r\n\r\n          <form [formGroup]=\"loginForm\" (ngSubmit)=\"login()\">\r\n            <!-- phone -->\r\n            <div class=\"form-group\">\r\n              <label for=\"phone\" class=\"form-label\">\r\n                <i class=\"ki-outline ki-user\"></i>\r\n                Phone <span class=\"required\"></span>\r\n              </label>\r\n              <input type=\"tel\" id=\"phone\" formControlName=\"phone\" class=\"form-control\"\r\n                [class.is-invalid]=\"isFieldInvalid('phone')\" placeholder=\"01xxxxxxxxx\" required autocomplete=\"tel\"\r\n                (blur)=\"markFieldAsTouched('phone')\" />\r\n              <div *ngIf=\"isFieldInvalid('phone')\" class=\"invalid-feedback\">\r\n                {{ getFieldError(\"phone\") }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Password -->\r\n            <div class=\"form-group\">\r\n              <label for=\"password\" class=\"form-label\">\r\n                <i class=\"ki-outline ki-lock\"></i>\r\n                Password <span class=\"required\"></span>\r\n              </label>\r\n              <input type=\"password\" id=\"password\" formControlName=\"password\" class=\"form-control\"\r\n                [class.is-invalid]=\"isFieldInvalid('password')\" placeholder=\"Enter your password\" required\r\n                autocomplete=\"current-password\" (blur)=\"markFieldAsTouched('password')\" />\r\n              <div *ngIf=\"isFieldInvalid('password')\" class=\"invalid-feedback\">\r\n                {{ getFieldError(\"password\") }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Forgot Password & Remember Me Row -->\r\n            <div class=\"form-row\">\r\n              <!-- Forgot Password Link (Left) -->\r\n              <div class=\"forgot-password-link\">\r\n                <a (click)=\"goToForgotPassword()\" class=\"forgot-link\" style=\"cursor: pointer\">Forgot your password?</a>\r\n              </div>\r\n\r\n              <!-- Remember Me (Right) -->\r\n              <div class=\"form-check\">\r\n                <input type=\"checkbox\" id=\"rememberMe\" formControlName=\"rememberMe\" class=\"form-check-input\" />\r\n                <label for=\"rememberMe\" class=\"form-check-label\">\r\n                  Remember me\r\n                </label>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Login Error Message -->\r\n            <div *ngIf=\"loginErrorMessage\" class=\"alert alert-danger mt-3\" role=\"alert\">\r\n              {{ loginErrorMessage }}\r\n            </div>\r\n\r\n            <!-- Login Button -->\r\n            <button type=\"submit\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingLogin\"\r\n              [disabled]=\"!isLoginFormValid() || isLoadingLogin\" (click)=\"login()\">\r\n              <span *ngIf=\"isLoadingLogin\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n              {{ isLoadingLogin ? \"Logging in...\" : \"Log In\" }}\r\n            </button>\r\n          </form>\r\n\r\n          <!-- Help Text -->\r\n          <div class=\"help-text register-section\">\r\n            <div class=\"register-text\">\r\n              <a routerLink=\"../register\" class=\"register-link\">\r\n                <span class=\"register-message\">Don't have an account?\r\n                  <span class=\"create-link\">Create one</span></span>\r\n                <i class=\"ki-outline ki-arrow-right\"></i>\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"help-text\">\r\n            Need help? <span class=\"contact-link\">Contact us</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 2: Forgot Password Form -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"registration-stepper-step\">\r\n      <div class=\"login-stepper\">\r\n        <div class=\"step-content\">\r\n          <h1 class=\"step-title\">Forgot Password</h1>\r\n          <p class=\"step-subtitle\">\r\n            Don't worry, we'll send you password recovery instructions\r\n          </p>\r\n\r\n          <form [formGroup]=\"forgotPasswordForm\">\r\n            <!-- Email Input -->\r\n            <div class=\"form-group\">\r\n              <label for=\"forgot-email\" class=\"form-label\">\r\n                <i class=\"ki-outline ki-phone\"></i>\r\n                Email or Mobile Number <span class=\"required\"></span>\r\n              </label>\r\n              <input type=\"text\" id=\"forgot-email\" formControlName=\"input\" class=\"form-control\"\r\n                [class.is-invalid]=\"isFieldInvalid('input', forgotPasswordForm)\"\r\n                placeholder=\"Enter your email or mobile number\" required\r\n                (blur)=\"markFieldAsTouched('input', forgotPasswordForm)\" />\r\n              <div *ngIf=\"isFieldInvalid('input', forgotPasswordForm)\" class=\"invalid-feedback\">\r\n                {{ getFieldError(\"input\", forgotPasswordForm) }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Error Message -->\r\n            <div *ngIf=\"otpErrorMessage\" class=\"alert alert-danger mt-3\" role=\"alert\">\r\n              {{ otpErrorMessage }}\r\n            </div>\r\n\r\n            <!-- Send Verification Button -->\r\n            <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingSendOtp\"\r\n              [disabled]=\"!isForgotPasswordFormValid() || isLoadingSendOtp\" (click)=\"handleNextStepAndSendCode()\">\r\n              <span *ngIf=\"isLoadingSendOtp\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n              {{ isLoadingSendOtp ? \"Sending...\" : \"Send Verification Code\" }}\r\n            </button>\r\n          </form>\r\n\r\n          <!-- Back to Login -->\r\n          <div class=\"help-text\">\r\n            <a (click)=\"backToLogin()\" class=\"back-link\" style=\"cursor: pointer\">\r\n              <i class=\"ki-outline ki-arrow-left\"></i>\r\n              Back to Login\r\n            </a>\r\n          </div>\r\n\r\n          <!-- Need Help -->\r\n          <div class=\"help-text\">\r\n            Need help? <a href=\"#\" class=\"contact-link\">Contact us</a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 3: Verification Code -->\r\n    <div *ngIf=\"currentStep === 3\" class=\"registration-stepper-step\">\r\n      <div class=\"login-stepper\">\r\n        <div class=\"step-content\">\r\n          <h1 class=\"step-title\">Enter Verification Code</h1>\r\n\r\n          <!-- Verification Code Input -->\r\n          <div class=\"verification-code-section\" [formGroup]=\"forgotPasswordForm\">\r\n            <div formArrayName=\"verificationCode\" class=\"verification-inputs\">\r\n              <div class=\"code-input\" *ngFor=\"let ctrl of verificationCodeControls; let i = index\">\r\n                <input type=\"text\" maxlength=\"1\" class=\"verification-input\" [formControlName]=\"i\"\r\n                  [class.is-invalid]=\"ctrl.invalid && ctrl.touched\" (input)=\"autoFocusNext($event, i)\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Countdown Timer -->\r\n          <div class=\"countdown-section\">\r\n            <span class=\"countdown-text\" *ngIf=\"!showResendButton\">\r\n              Resend in\r\n              <span class=\"countdown-timer\">\r\n                0:{{ countdown < 10 ? \"0\" + countdown : countdown }} </span>\r\n              </span>\r\n\r\n              <button *ngIf=\"showResendButton\" class=\"btn btn-link\" (click)=\"onResendCode()\">\r\n                Resend Code\r\n              </button>\r\n          </div>\r\n\r\n          <!-- OTP Error Message -->\r\n          <div *ngIf=\"otpErrorMessage\" class=\"alert alert-danger mt-3\" role=\"alert\">\r\n            {{ otpErrorMessage }}\r\n          </div>\r\n\r\n          <!-- Next Button -->\r\n          <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingCheckOtp\"\r\n            [disabled]=\"!isVerificationCodeValid() || isLoadingCheckOtp\" (click)=\"checkOTP()\">\r\n            <span *ngIf=\"isLoadingCheckOtp\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n            {{ isLoadingCheckOtp ? \"Verifying...\" : \"Verified - Next\" }}\r\n          </button>\r\n\r\n          <!-- Back to Forgot Password -->\r\n          <div class=\"help-text\">\r\n            <a (click)=\"backToForgotPassword()\" class=\"back-link\" style=\"cursor: pointer\">\r\n              <i class=\"ki-outline ki-arrow-left\"></i>\r\n              Back to Forgot Password\r\n            </a>\r\n          </div>\r\n\r\n          <!-- Help Text -->\r\n          <div class=\"help-text\">\r\n            Need help? <span class=\"contact-link\">Contact us</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 4: Reset Password Form -->\r\n    <div *ngIf=\"currentStep === 4\" class=\"registration-stepper-step\">\r\n      <div class=\"login-stepper\">\r\n        <div class=\"step-content\">\r\n          <h1 class=\"step-title\">Reset Password</h1>\r\n          <p class=\"step-subtitle\">Enter your new password for your account</p>\r\n\r\n          <form [formGroup]=\"resetPasswordForm\">\r\n            <!-- New Password -->\r\n            <div class=\"form-group\">\r\n              <label for=\"new-password\" class=\"form-label\">\r\n                <i class=\"ki-outline ki-lock\"></i>\r\n                New Password <span class=\"required\"></span>\r\n              </label>\r\n              <input type=\"password\" id=\"new-password\" formControlName=\"newPassword\" class=\"form-control\"\r\n                [class.is-invalid]=\"isFieldInvalid('newPassword', resetPasswordForm)\"\r\n                placeholder=\"Enter your new password\" minlength=\"8\" pattern=\"^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$\"\r\n                title=\"Password must be at least 8 characters with uppercase, lowercase and number\" required\r\n                autocomplete=\"new-password\" (blur)=\"markFieldAsTouched('newPassword', resetPasswordForm)\" />\r\n              <div *ngIf=\"isFieldInvalid('newPassword', resetPasswordForm)\" class=\"invalid-feedback\">\r\n                {{ getFieldError(\"newPassword\", resetPasswordForm) }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Confirm Password -->\r\n            <div class=\"form-group\">\r\n              <label for=\"confirm-password\" class=\"form-label\">\r\n                <i class=\"ki-outline ki-lock\"></i>\r\n                Confirm Password <span class=\"required\"></span>\r\n              </label>\r\n              <input type=\"password\" id=\"confirm-password\" formControlName=\"password_confirmation\" class=\"form-control\"\r\n                [class.is-invalid]=\"isFieldInvalid('password_confirmation', resetPasswordForm) || getFormError(resetPasswordForm)\"\r\n                placeholder=\"Re-enter your password\" required autocomplete=\"new-password\"\r\n                (blur)=\"markFieldAsTouched('password_confirmation', resetPasswordForm)\" />\r\n              <div *ngIf=\"isFieldInvalid('password_confirmation', resetPasswordForm)\" class=\"invalid-feedback\">\r\n                {{ getFieldError(\"password_confirmation\", resetPasswordForm) }}\r\n              </div>\r\n              <div *ngIf=\"getFormError(resetPasswordForm)\" class=\"invalid-feedback\">\r\n                {{ getFormError(resetPasswordForm) }}\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Reset Password Error Message -->\r\n            <div *ngIf=\"resetPasswordErrorMessage\" class=\"alert alert-danger mt-3\" role=\"alert\">\r\n              {{ resetPasswordErrorMessage }}\r\n            </div>\r\n\r\n            <!-- Submit Button -->\r\n            <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingResetPassword\"\r\n              [disabled]=\"!isResetPasswordFormValid() || isLoadingResetPassword\" (click)=\"submitResetPassword()\">\r\n              <span *ngIf=\"isLoadingResetPassword\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n              {{ isLoadingResetPassword ? \"Saving...\" : \"Save New Password\" }}\r\n            </button>\r\n          </form>\r\n\r\n          <!-- Back to Verification Code -->\r\n          <div class=\"help-text\">\r\n            <a (click)=\"backToVerificationCode()\" class=\"back-link\" style=\"cursor: pointer\">\r\n              <i class=\"ki-outline ki-arrow-left\"></i>\r\n              Back to Verification Code\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": ";AACA,SAA4CA,UAAU,QAAQ,gBAAgB;;;;;;;;ICkBhEC,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,aAAA,eACF;;;;;IAYAP,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,aAAA,kBACF;;;;;IAoBFP,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAE,iBAAA,MACF;;;;;IAKER,EAAA,CAAAS,SAAA,eAAgG;;;;;;IAvDpGT,EAHN,CAAAC,cAAA,aAAiE,aACpC,aACC,YACD;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEjDH,EAAA,CAAAC,cAAA,cAAmD;IAArBD,EAAA,CAAAU,UAAA,sBAAAC,uDAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAYT,MAAA,CAAAU,KAAA,EAAO;IAAA,EAAC;IAG9ChB,EADF,CAAAC,cAAA,aAAwB,eACgB;IACpCD,EAAA,CAAAS,SAAA,YAAkC;IAClCT,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAS,SAAA,gBAA8B;IACtCT,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAEyC;IAAvCD,EAAA,CAAAU,UAAA,kBAAAO,qDAAA;MAAAjB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAQT,MAAA,CAAAY,kBAAA,CAAmB,OAAO,CAAC;IAAA,EAAC;IAFtClB,EAAA,CAAAG,YAAA,EAEyC;IACzCH,EAAA,CAAAmB,UAAA,KAAAC,oCAAA,kBAA8D;IAGhEpB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAAwB,iBACmB;IACvCD,EAAA,CAAAS,SAAA,aAAkC;IAClCT,EAAA,CAAAE,MAAA,kBAAS;IAAAF,EAAA,CAAAS,SAAA,gBAA8B;IACzCT,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAE4E;IAA1CD,EAAA,CAAAU,UAAA,kBAAAW,qDAAA;MAAArB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAQT,MAAA,CAAAY,kBAAA,CAAmB,UAAU,CAAC;IAAA,EAAC;IAFzElB,EAAA,CAAAG,YAAA,EAE4E;IAC5EH,EAAA,CAAAmB,UAAA,KAAAG,oCAAA,kBAAiE;IAGnEtB,EAAA,CAAAG,YAAA,EAAM;IAMFH,EAHJ,CAAAC,cAAA,eAAsB,eAEc,aAC8C;IAA3ED,EAAA,CAAAU,UAAA,mBAAAa,kDAAA;MAAAvB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAkB,kBAAA,EAAoB;IAAA,EAAC;IAA6CxB,EAAA,CAAAE,MAAA,6BAAqB;IACrGF,EADqG,CAAAG,YAAA,EAAI,EACnG;IAGNH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAS,SAAA,iBAA+F;IAC/FT,EAAA,CAAAC,cAAA,iBAAiD;IAC/CD,EAAA,CAAAE,MAAA,qBACF;IAEJF,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACF;IAGNH,EAAA,CAAAmB,UAAA,KAAAM,oCAAA,kBAA4E;IAK5EzB,EAAA,CAAAC,cAAA,kBACuE;IAAlBD,EAAA,CAAAU,UAAA,mBAAAgB,uDAAA;MAAA1B,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAU,KAAA,EAAO;IAAA,EAAC;IACpEhB,EAAA,CAAAmB,UAAA,KAAAQ,qCAAA,mBAAyF;IACzF3B,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACJ;IAMDH,EAHN,CAAAC,cAAA,eAAwC,eACX,aACyB,gBACjB;IAAAD,EAAA,CAAAE,MAAA,+BAC7B;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAO;IACpDH,EAAA,CAAAS,SAAA,aAAyC;IAG/CT,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;IAENH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAIxDF,EAJwD,CAAAG,YAAA,EAAO,EACnD,EACF,EACF,EACF;;;;IA1EMH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAAuB,SAAA,CAAuB;IAQvB7B,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAA8B,WAAA,eAAAxB,MAAA,CAAAyB,cAAA,UAA4C;IAExC/B,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAyB,cAAA,UAA6B;IAYjC/B,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAA8B,WAAA,eAAAxB,MAAA,CAAAyB,cAAA,aAA+C;IAE3C/B,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAyB,cAAA,aAAgC;IAsBlC/B,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAE,iBAAA,CAAuB;IAKkCR,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAA8B,WAAA,YAAAxB,MAAA,CAAA0B,cAAA,CAAgC;IAC7FhC,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAA2B,gBAAA,MAAA3B,MAAA,CAAA0B,cAAA,CAAkD;IAC3ChC,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA0B,cAAA,CAAoB;IAC3BhC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA0B,cAAA,mCACF;;;;;IAyCEhC,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,aAAA,UAAAD,MAAA,CAAA4B,kBAAA,OACF;;;;;IAIFlC,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA6B,eAAA,MACF;;;;;IAKEnC,EAAA,CAAAS,SAAA,eAAkG;;;;;;IA7BtGT,EAHN,CAAAC,cAAA,aAAiE,aACpC,aACC,YACD;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAAyB;IACvBD,EAAA,CAAAE,MAAA,mEACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKAH,EAHJ,CAAAC,cAAA,eAAuC,aAEb,gBACuB;IAC3CD,EAAA,CAAAS,SAAA,aAAmC;IACnCT,EAAA,CAAAE,MAAA,gCAAuB;IAAAF,EAAA,CAAAS,SAAA,gBAA8B;IACvDT,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAG6D;IAA3DD,EAAA,CAAAU,UAAA,kBAAA0B,qDAAA;MAAApC,EAAA,CAAAY,aAAA,CAAAyB,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAQT,MAAA,CAAAY,kBAAA,CAAmB,OAAO,EAAAZ,MAAA,CAAA4B,kBAAA,CAAqB;IAAA,EAAC;IAH1DlC,EAAA,CAAAG,YAAA,EAG6D;IAC7DH,EAAA,CAAAmB,UAAA,KAAAmB,oCAAA,kBAAkF;IAGpFtC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAmB,UAAA,KAAAoB,oCAAA,kBAA0E;IAK1EvC,EAAA,CAAAC,cAAA,kBACsG;IAAtCD,EAAA,CAAAU,UAAA,mBAAA8B,uDAAA;MAAAxC,EAAA,CAAAY,aAAA,CAAAyB,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAmC,yBAAA,EAA2B;IAAA,EAAC;IACnGzC,EAAA,CAAAmB,UAAA,KAAAuB,qCAAA,mBAA2F;IAC3F1C,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACJ;IAILH,EADF,CAAAC,cAAA,eAAuB,aACgD;IAAlED,EAAA,CAAAU,UAAA,mBAAAiC,kDAAA;MAAA3C,EAAA,CAAAY,aAAA,CAAAyB,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAsC,WAAA,EAAa;IAAA,EAAC;IACxB5C,EAAA,CAAAS,SAAA,aAAwC;IACxCT,EAAA,CAAAE,MAAA,uBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAGNH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAI9DF,EAJ8D,CAAAG,YAAA,EAAI,EACtD,EACF,EACF,EACF;;;;IA3CMH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAA4B,kBAAA,CAAgC;IAQhClC,EAAA,CAAAI,SAAA,GAAgE;IAAhEJ,EAAA,CAAA8B,WAAA,eAAAxB,MAAA,CAAAyB,cAAA,UAAAzB,MAAA,CAAA4B,kBAAA,EAAgE;IAG5DlC,EAAA,CAAAI,SAAA,EAAiD;IAAjDJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAyB,cAAA,UAAAzB,MAAA,CAAA4B,kBAAA,EAAiD;IAMnDlC,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA6B,eAAA,CAAqB;IAKoCnC,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAA8B,WAAA,YAAAxB,MAAA,CAAAuC,gBAAA,CAAkC;IAC/F7C,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAAwC,yBAAA,MAAAxC,MAAA,CAAAuC,gBAAA,CAA6D;IACtD7C,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAuC,gBAAA,CAAsB;IAC7B7C,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuC,gBAAA,gDACF;;;;;;IA6BI7C,EADF,CAAAC,cAAA,cAAqF,gBAEM;IAArCD,EAAA,CAAAU,UAAA,mBAAAqC,2DAAAC,MAAA;MAAA,MAAAC,IAAA,GAAAjD,EAAA,CAAAY,aAAA,CAAAsC,GAAA,EAAAC,KAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA8C,aAAA,CAAAJ,MAAA,EAAAC,IAAA,CAAwB;IAAA,EAAC;IACxFjD,EAFE,CAAAG,YAAA,EACyF,EACrF;;;;;IADFH,EAAA,CAAAI,SAAA,EAAiD;IAAjDJ,EAAA,CAAA8B,WAAA,eAAAuB,OAAA,CAAAC,OAAA,IAAAD,OAAA,CAAAE,OAAA,CAAiD;IADSvD,EAAA,CAAA4B,UAAA,oBAAAqB,IAAA,CAAqB;;;;;IAQrFjD,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAE,MAAA,kBACA;IAAAF,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAE,MAAA,GAAqD;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACvD;;;;IADLH,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,kBAAA,QAAAC,MAAA,CAAAkD,SAAA,cAAAlD,MAAA,CAAAkD,SAAA,GAAAlD,MAAA,CAAAkD,SAAA,MAAqD;;;;;;IAGvDxD,EAAA,CAAAC,cAAA,iBAA+E;IAAzBD,EAAA,CAAAU,UAAA,mBAAA+C,gEAAA;MAAAzD,EAAA,CAAAY,aAAA,CAAA8C,GAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAqD,YAAA,EAAc;IAAA,EAAC;IAC5E3D,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAIbH,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA6B,eAAA,MACF;;;;;IAKEnC,EAAA,CAAAS,SAAA,eAAmG;;;;;;IAjCrGT,EAHN,CAAAC,cAAA,aAAiE,aACpC,aACC,YACD;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIjDH,EADF,CAAAC,cAAA,cAAwE,cACJ;IAChED,EAAA,CAAAmB,UAAA,IAAAyC,mCAAA,kBAAqF;IAKzF5D,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,cAA+B;IAO3BD,EANF,CAAAmB,UAAA,IAAA0C,oCAAA,mBAAuD,KAAAC,uCAAA,qBAM0B;IAGnF9D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAmB,UAAA,KAAA4C,oCAAA,kBAA0E;IAK1E/D,EAAA,CAAAC,cAAA,kBACoF;IAArBD,EAAA,CAAAU,UAAA,mBAAAsD,uDAAA;MAAAhE,EAAA,CAAAY,aAAA,CAAAqD,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA4D,QAAA,EAAU;IAAA,EAAC;IACjFlE,EAAA,CAAAmB,UAAA,KAAAgD,qCAAA,mBAA4F;IAC5FnE,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAIPH,EADF,CAAAC,cAAA,eAAuB,aACyD;IAA3ED,EAAA,CAAAU,UAAA,mBAAA0D,kDAAA;MAAApE,EAAA,CAAAY,aAAA,CAAAqD,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA+D,oBAAA,EAAsB;IAAA,EAAC;IACjCrE,EAAA,CAAAS,SAAA,aAAwC;IACxCT,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAGNH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAIxDF,EAJwD,CAAAG,YAAA,EAAO,EACnD,EACF,EACF,EACF;;;;IAhDuCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAA4B,kBAAA,CAAgC;IAE1BlC,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAgE,wBAAA,CAA6B;IAS1CtE,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAiE,gBAAA,CAAuB;IAM1CvE,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAiE,gBAAA,CAAsB;IAM7BvE,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA6B,eAAA,CAAqB;IAKoCnC,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAA8B,WAAA,YAAAxB,MAAA,CAAAkE,iBAAA,CAAmC;IAChGxE,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAAmE,uBAAA,MAAAnE,MAAA,CAAAkE,iBAAA,CAA4D;IACrDxE,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAkE,iBAAA,CAAuB;IAC9BxE,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAkE,iBAAA,2CACF;;;;;IAqCIxE,EAAA,CAAAC,cAAA,cAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,aAAA,gBAAAD,MAAA,CAAAoE,iBAAA,OACF;;;;;IAaA1E,EAAA,CAAAC,cAAA,cAAiG;IAC/FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,aAAA,0BAAAD,MAAA,CAAAoE,iBAAA,OACF;;;;;IACA1E,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAqE,YAAA,CAAArE,MAAA,CAAAoE,iBAAA,OACF;;;;;IAIF1E,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAsE,yBAAA,MACF;;;;;IAKE5E,EAAA,CAAAS,SAAA,eAAwG;;;;;;IA9C5GT,EAHN,CAAAC,cAAA,aAAiE,aACpC,aACC,YACD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKjEH,EAHJ,CAAAC,cAAA,eAAsC,aAEZ,gBACuB;IAC3CD,EAAA,CAAAS,SAAA,aAAkC;IAClCT,EAAA,CAAAE,MAAA,sBAAa;IAAAF,EAAA,CAAAS,SAAA,gBAA8B;IAC7CT,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAI8F;IAAhED,EAAA,CAAAU,UAAA,kBAAAmE,qDAAA;MAAA7E,EAAA,CAAAY,aAAA,CAAAkE,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAQT,MAAA,CAAAY,kBAAA,CAAmB,aAAa,EAAAZ,MAAA,CAAAoE,iBAAA,CAAoB;IAAA,EAAC;IAJ3F1E,EAAA,CAAAG,YAAA,EAI8F;IAC9FH,EAAA,CAAAmB,UAAA,KAAA4D,oCAAA,kBAAuF;IAGzF/E,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAAwB,iBAC2B;IAC/CD,EAAA,CAAAS,SAAA,aAAkC;IAClCT,EAAA,CAAAE,MAAA,0BAAiB;IAAAF,EAAA,CAAAS,SAAA,gBAA8B;IACjDT,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAG4E;IAA1ED,EAAA,CAAAU,UAAA,kBAAAsE,qDAAA;MAAAhF,EAAA,CAAAY,aAAA,CAAAkE,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAQT,MAAA,CAAAY,kBAAA,CAAmB,uBAAuB,EAAAZ,MAAA,CAAAoE,iBAAA,CAAoB;IAAA,EAAC;IAHzE1E,EAAA,CAAAG,YAAA,EAG4E;IAI5EH,EAHA,CAAAmB,UAAA,KAAA8D,oCAAA,kBAAiG,KAAAC,oCAAA,kBAG3B;IAGxElF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAmB,UAAA,KAAAgE,oCAAA,kBAAoF;IAKpFnF,EAAA,CAAAC,cAAA,kBACqG;IAAhCD,EAAA,CAAAU,UAAA,mBAAA0E,uDAAA;MAAApF,EAAA,CAAAY,aAAA,CAAAkE,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAA+E,mBAAA,EAAqB;IAAA,EAAC;IAClGrF,EAAA,CAAAmB,UAAA,KAAAmE,qCAAA,mBAAiG;IACjGtF,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACJ;IAILH,EADF,CAAAC,cAAA,eAAuB,aAC2D;IAA7ED,EAAA,CAAAU,UAAA,mBAAA6E,kDAAA;MAAAvF,EAAA,CAAAY,aAAA,CAAAkE,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAkF,sBAAA,EAAwB;IAAA,EAAC;IACnCxF,EAAA,CAAAS,SAAA,aAAwC;IACxCT,EAAA,CAAAE,MAAA,mCACF;IAIRF,EAJQ,CAAAG,YAAA,EAAI,EACA,EACF,EACF,EACF;;;;IAzDMH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAAoE,iBAAA,CAA+B;IAQ/B1E,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAA8B,WAAA,eAAAxB,MAAA,CAAAyB,cAAA,gBAAAzB,MAAA,CAAAoE,iBAAA,EAAqE;IAIjE1E,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAyB,cAAA,gBAAAzB,MAAA,CAAAoE,iBAAA,EAAsD;IAY1D1E,EAAA,CAAAI,SAAA,GAAkH;IAAlHJ,EAAA,CAAA8B,WAAA,eAAAxB,MAAA,CAAAyB,cAAA,0BAAAzB,MAAA,CAAAoE,iBAAA,KAAApE,MAAA,CAAAqE,YAAA,CAAArE,MAAA,CAAAoE,iBAAA,EAAkH;IAG9G1E,EAAA,CAAAI,SAAA,EAAgE;IAAhEJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAyB,cAAA,0BAAAzB,MAAA,CAAAoE,iBAAA,EAAgE;IAGhE1E,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAqE,YAAA,CAAArE,MAAA,CAAAoE,iBAAA,EAAqC;IAMvC1E,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAsE,yBAAA,CAA+B;IAK0B5E,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAA8B,WAAA,YAAAxB,MAAA,CAAAmF,sBAAA,CAAwC;IACrGzF,EAAA,CAAA4B,UAAA,cAAAtB,MAAA,CAAAoF,wBAAA,MAAApF,MAAA,CAAAmF,sBAAA,CAAkE;IAC3DzF,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAmF,sBAAA,CAA4B;IACnCzF,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAmF,sBAAA,0CACF;;;AD9OZ,OAAM,MAAOE,cAAc;EA4BfC,EAAA;EACAC,MAAA;EACAC,qBAAA;EACAC,EAAA;EA9BVC,WAAW,GAAG,CAAC;EACfnE,SAAS;EACTK,kBAAkB;EAClBwC,iBAAiB;EACjBuB,SAAS,GAAG,KAAK;EACjBC,kBAAkB,GAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAEnD1C,SAAS,GAAW,EAAE;EACtBe,gBAAgB,GAAY,KAAK;EAEjC;EACAvC,cAAc,GAAY,KAAK;EAC/Ba,gBAAgB,GAAY,KAAK;EACjC2B,iBAAiB,GAAY,KAAK;EAClCiB,sBAAsB,GAAY,KAAK;EAEvC;EACAjF,iBAAiB,GAAW,EAAE;EAC9B2B,eAAe,GAAW,EAAE;EAC5ByC,yBAAyB,GAAW,EAAE;EAEtC;EACA,OAAOuB,YAAY,GAAGpG,UAAU,CAACqG,OAAO,CAAC,wDAAwD,CAAC;EAClG,OAAOC,mBAAmB,GAAGtG,UAAU,CAACqG,OAAO,CAAC,iFAAiF,CAAC;EAClI,OAAOE,eAAe,GAAGvG,UAAU,CAACqG,OAAO,CAAC,iCAAiC,CAAC;EAE9EG,YACUX,EAAe,EACfC,MAAc,EACdC,qBAA4C,EAC5CC,EAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,EAAE,GAAFA,EAAE;IAEV,IAAI,CAAClE,SAAS,GAAG,IAAI,CAAC2E,eAAe,EAAE;IACvC,IAAI,CAACtE,kBAAkB,GAAG,IAAI,CAACuE,wBAAwB,EAAE;IACzD,IAAI,CAAC/B,iBAAiB,GAAG,IAAI,CAACgC,uBAAuB,EAAE;EACzD;EAEQF,eAAeA,CAAA;IACrB,OAAO,IAAI,CAACZ,EAAE,CAACe,KAAK,CAAC;MACnBC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC7G,UAAU,CAAC8G,QAAQ,EAAElB,cAAc,CAACQ,YAAY,CAAC,CAAC;MAC/DW,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC/G,UAAU,CAAC8G,QAAQ,EAAE9G,UAAU,CAACgH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEQP,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACb,EAAE,CAACe,KAAK,CAAC;MACnBM,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClH,UAAU,CAAC8G,QAAQ,EAAElB,cAAc,CAACU,mBAAmB,CAAC,CAAC;MACtEa,gBAAgB,EAAE,IAAI,CAACtB,EAAE,CAACuB,KAAK,CAC7BC,KAAK,CAAC,CAAC,CAAC,CACLC,IAAI,CAAC,EAAE,CAAC,CACRC,GAAG,CAAC,MACH,IAAI,CAAC1B,EAAE,CAAC2B,OAAO,CAAC,EAAE,EAAE,CAClBxH,UAAU,CAAC8G,QAAQ,EACnB9G,UAAU,CAACqG,OAAO,CAAC,OAAO,CAAC,CAC5B,CAAC,CACH;KAEN,CAAC;EACJ;EAEQM,uBAAuBA,CAAA;IAC7B,OAAO,IAAI,CAACd,EAAE,CAACe,KAAK,CAAC;MACnBa,WAAW,EAAE,CAAC,EAAE,EAAE,CAACzH,UAAU,CAAC8G,QAAQ,EAAE9G,UAAU,CAACgH,SAAS,CAAC,CAAC,CAAC,EAAEpB,cAAc,CAACW,eAAe,CAAC,CAAC;MACjGmB,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAAC1H,UAAU,CAAC8G,QAAQ,CAAC;KAClD,CAAC;EACJ;EAEAa,QAAQA,CAAA,GAAU;EAElB;EACA3F,cAAcA,CAAC4F,SAAiB,EAAEC,SAAqB;IACrD,MAAMC,IAAI,GAAGD,SAAS,IAAI,IAAI,CAAC/F,SAAS;IACxC,MAAMiG,KAAK,GAAGD,IAAI,CAACE,GAAG,CAACJ,SAAS,CAAC;IACjC,OAAO,CAAC,EAAEG,KAAK,IAAIA,KAAK,CAACxE,OAAO,KAAKwE,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACvE,OAAO,CAAC,CAAC;EACrE;EAEArC,kBAAkBA,CAACyG,SAAiB,EAAEC,SAAqB;IACzD,MAAMC,IAAI,GAAGD,SAAS,IAAI,IAAI,CAAC/F,SAAS;IACxCgG,IAAI,CAACE,GAAG,CAACJ,SAAS,CAAC,EAAEM,aAAa,EAAE;EACtC;EAEA1H,aAAaA,CAACoH,SAAiB,EAAEC,SAAqB;IACpD,MAAMC,IAAI,GAAGD,SAAS,IAAI,IAAI,CAAC/F,SAAS;IACxC,MAAMiG,KAAK,GAAGD,IAAI,CAACE,GAAG,CAACJ,SAAS,CAAC;IACjC,IAAI,CAACG,KAAK,EAAEI,MAAM,EAAE,OAAO,EAAE;IAE7B,MAAMA,MAAM,GAAGJ,KAAK,CAACI,MAAM;IAC3B,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,wBAAwB;IACvD,IAAIA,MAAM,CAAC,SAAS,CAAC,IAAIP,SAAS,KAAK,OAAO,EAAE,OAAO,0BAA0B;IACjF,IAAIO,MAAM,CAAC,SAAS,CAAC,IAAIP,SAAS,KAAK,OAAO,EAAE,OAAO,mCAAmC;IAC1F,IAAIO,MAAM,CAAC,SAAS,CAAC,IAAIP,SAAS,KAAK,aAAa,EAAE,OAAO,oCAAoC;IACjG,IAAIO,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,OAAOA,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc,QAAQ;IAEjF,OAAO,eAAe;EACxB;EAEAxD,YAAYA,CAACiD,SAAoB;IAC/B,MAAMd,QAAQ,GAAGc,SAAS,CAACG,GAAG,CAAC,aAAa,CAAC,EAAEK,KAAK;IACpD,MAAMC,eAAe,GAAGT,SAAS,CAACG,GAAG,CAAC,uBAAuB,CAAC,EAAEK,KAAK;IAErE,IAAItB,QAAQ,IAAIuB,eAAe,IAAIvB,QAAQ,KAAKuB,eAAe,EAAE;MAC/D,OAAO,wBAAwB;IACjC;IACA,OAAO,EAAE;EACX;EAEA;EACApG,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACJ,SAAS,CAACyG,KAAK;EAC7B;EAEAxF,yBAAyBA,CAAA;IACvB,MAAMmE,KAAK,GAAG,IAAI,CAAC/E,kBAAkB,CAAC6F,GAAG,CAAC,OAAO,CAAC;IAClD,OAAO,CAAC,CAACd,KAAK,EAAEqB,KAAK;EACvB;EAEA7D,uBAAuBA,CAAA;IACrB,MAAMyC,gBAAgB,GAAG,IAAI,CAAChF,kBAAkB,CAAC6F,GAAG,CAAC,kBAAkB,CAAc;IACrF,OAAOb,gBAAgB,CAACoB,KAAK;EAC/B;EAEA5C,wBAAwBA,CAAA;IACtB,MAAM8B,WAAW,GAAG,IAAI,CAAC9C,iBAAiB,CAACqD,GAAG,CAAC,aAAa,CAAC;IAC7D,MAAMQ,oBAAoB,GAAG,IAAI,CAAC7D,iBAAiB,CAACqD,GAAG,CAAC,uBAAuB,CAAC;IAChF,MAAMS,cAAc,GAAGhB,WAAW,EAAEY,KAAK,KAAKG,oBAAoB,EAAEH,KAAK;IAEzE,OAAO,CAAC,EAAEZ,WAAW,EAAEc,KAAK,IAAIC,oBAAoB,EAAED,KAAK,IAAIE,cAAc,CAAC;EAChF;EAEAxH,KAAKA,CAAA;IACH,IAAI,CAAC,IAAI,CAACiB,gBAAgB,EAAE,EAAE;MAC5B,IAAI,CAACf,kBAAkB,CAAC,OAAO,CAAC;MAChC,IAAI,CAACA,kBAAkB,CAAC,UAAU,CAAC;MACnC;IACF;IAEA,IAAI,CAACc,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACxB,iBAAiB,GAAG,EAAE;IAC3B,IAAIiI,MAAM,GAAG,IAAI,CAAC5G,SAAS,CAACuG,KAAK;IAEjC,IAAI,CAACtC,qBAAqB,CAAC9E,KAAK,CAACyH,MAAM,CAAC,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAIC,IAAI,GAAGD,QAAQ,CAACE,IAAI;QACxBC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEH,IAAI,CAACI,SAAS,CAAC;QACjD,IAAI,CAACnD,qBAAqB,CAACoD,cAAc,CAACN,QAAQ,CAACE,IAAI,CAAC;QACxDK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAER,QAAQ,CAAC;QAC5C,IAAI,CAAC5G,cAAc,GAAG,KAAK;QAE3B,IAAI6G,IAAI,CAACQ,IAAI,IAAI,WAAW,EAAE;UAC5B,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;QACjD,CAAC,MAAM,IAAIT,IAAI,CAACQ,IAAI,IAAI,QAAQ,EAAE;UAChC,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;QAC7C,CAAC,MAAM,IAAIT,IAAI,CAACQ,IAAI,IAAI,QAAQ,EAAE;UAChC,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC,MAAM,IAAIT,IAAI,CAACQ,IAAI,IAAI,OAAO,EAAE;UAC/B,IAAI,CAACxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;QAClD;QACA,IAAI,CAACvD,EAAE,CAACwD,YAAY,EAAE;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBL,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxC,IAAI,CAACxH,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACxB,iBAAiB,GAAGgJ,KAAK,EAAEA,KAAK,EAAEC,OAAO,IAAI,8CAA8C;QAChG,IAAI,CAAC1D,EAAE,CAACwD,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEA,IAAIjF,wBAAwBA,CAAA;IAC1B,OAAQ,IAAI,CAACpC,kBAAkB,CAAC6F,GAAG,CAAC,kBAAkB,CAAe,CAClE2B,QAAQ;EACb;EAEAlI,kBAAkBA,CAAA;IAChB,IAAI,CAACwE,WAAW,GAAG,CAAC;EACtB;EACApD,WAAWA,CAAA;IACT,IAAI,CAACoD,WAAW,GAAG,CAAC;EACtB;EACA3B,oBAAoBA,CAAA;IAClB,IAAI,CAAC2B,WAAW,GAAG,CAAC;EACtB;EACAR,sBAAsBA,CAAA;IACpB,IAAI,CAACQ,WAAW,GAAG,CAAC;EACtB;EAEA2D,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC3D,WAAW,GAAG,CAAC,EAAE;MACxB;MACA,IAAI,IAAI,CAACA,WAAW,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAC7D,eAAe,GAAG,EAAE;MAC3B,CAAC,MAAM,IAAI,IAAI,CAAC6D,WAAW,KAAK,CAAC,EAAE;QACjC,IAAI,CAAC7D,eAAe,GAAG,EAAE;MAC3B;MACA,IAAI,CAAC6D,WAAW,EAAE;IACpB;EACF;EAEAX,mBAAmBA,CAAA;IAAA,IAAAuE,KAAA;IACjB,IAAI,CAAC,IAAI,CAAClE,wBAAwB,EAAE,EAAE;MACpC,IAAI,CAACxE,kBAAkB,CAAC,aAAa,EAAE,IAAI,CAACwD,iBAAiB,CAAC;MAC9D,IAAI,CAACxD,kBAAkB,CAAC,uBAAuB,EAAE,IAAI,CAACwD,iBAAiB,CAAC;MACxE;IACF;IAEA,IAAI,CAACe,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACb,yBAAyB,GAAG,EAAE;IACnC,MAAMqC,KAAK,GAAG,IAAI,CAAC/E,kBAAkB,CAAC6F,GAAG,CAAC,OAAO,CAAC,EAAEK,KAAK,EAAEyB,IAAI,EAAE;IAEjE,IAAIpB,MAAM,GAKN,EAAE;IACN,MAAMqB,OAAO,GAAG,4BAA4B,CAACC,IAAI,CAAC9C,KAAK,CAAC;IACxD,MAAM+C,OAAO,GAAG,oBAAoB,CAACD,IAAI,CAAC9C,KAAK,CAAC;IAEhD,IAAI6C,OAAO,EAAE;MACXrB,MAAM,CAACwB,KAAK,GAAGhD,KAAK;IACtB,CAAC,MAAM,IAAI+C,OAAO,EAAE;MAClBvB,MAAM,CAAC7B,KAAK,GAAGK,KAAK;IACtB;IACAwB,MAAM,CAAC3B,QAAQ,GAAG,IAAI,CAACpC,iBAAiB,CAACqD,GAAG,CAAC,aAAa,CAAC,EAAEK,KAAK,EAAEyB,IAAI,EAAE;IAC1EpB,MAAM,CAAChB,qBAAqB,GAAG,IAAI,CAAC/C,iBAAiB,CAClDqD,GAAG,CAAC,uBAAuB,CAAC,EAC3BK,KAAK,EAAEyB,IAAI,EAAE;IAEjB,IAAI,CAAC/D,qBAAqB,CAACoE,aAAa,CAACzB,MAAM,CAAC,CAACC,SAAS,CAAC;MACzDC,IAAI;QAAA,IAAAwB,IAAA,GAAAC,iBAAA,CAAE,WAAOxB,QAAQ,EAAI;UACvBO,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAER,QAAQ,CAAC;UAChDgB,KAAI,CAACnE,sBAAsB,GAAG,KAAK;UACnC;UACAmE,KAAI,CAAC5D,WAAW,GAAG,CAAC;UACpB4D,KAAI,CAACpJ,iBAAiB,GAAG,mEAAmE;UAC5FoJ,KAAI,CAAC7D,EAAE,CAACwD,YAAY,EAAE;QACxB,CAAC;QAAA,gBAPDZ,IAAIA,CAAA0B,EAAA;UAAA,OAAAF,IAAA,CAAAG,KAAA,OAAAC,SAAA;QAAA;MAAA,GAOH;MACDf,KAAK,EAAGgB,GAAG,IAAI;QACbrB,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEgB,GAAG,CAAC;QACtC,IAAI,CAAC/E,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAACb,yBAAyB,GAAG4F,GAAG,EAAEhB,KAAK,EAAEC,OAAO,IAAI,6CAA6C;QACrG,IAAI,CAAC1D,EAAE,CAACwD,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEQkB,cAAcA,CAAA;IACpB,IAAI,CAACvE,kBAAkB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC9C,MAAMwE,qBAAqB,GAAG,IAAI,CAACxI,kBAAkB,CAAC6F,GAAG,CAAC,kBAAkB,CAAc;IAC1F2C,qBAAqB,CAAChB,QAAQ,CAACiB,OAAO,CAAEpD,OAAO,IAAI;MACjDA,OAAO,CAACqD,QAAQ,CAAC,EAAE,CAAC;MACpBrD,OAAO,CAACsD,eAAe,EAAE;MACzBtD,OAAO,CAACuD,cAAc,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEArI,yBAAyBA,CAAA;IACvB,IAAI,CAACsI,oBAAoB,CAAC,IAAI,CAAC;EACjC;EAEAA,oBAAoBA,CAACC,cAAA,GAA0B,KAAK;IAClD,IAAI,CAAC,IAAI,CAAClI,yBAAyB,EAAE,EAAE;MACrC,IAAI,CAAC5B,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAACgB,kBAAkB,CAAC;MACzD;IACF;IAEA,IAAI,CAACW,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACV,eAAe,GAAG,EAAE;IACzB,MAAM8E,KAAK,GAAG,IAAI,CAAC/E,kBAAkB,CAAC6F,GAAG,CAAC,OAAO,CAAC,EAAEK,KAAK,EAAEyB,IAAI,EAAE;IAEjE,IAAIpB,MAAM,GAAuC,EAAE;IACnD,MAAMqB,OAAO,GAAG,4BAA4B,CAACC,IAAI,CAAC9C,KAAK,CAAC;IACxD,MAAM+C,OAAO,GAAG,oBAAoB,CAACD,IAAI,CAAC9C,KAAK,CAAC;IAEhD,IAAI6C,OAAO,EAAE;MACXrB,MAAM,CAACwB,KAAK,GAAGhD,KAAK;IACtB,CAAC,MAAM,IAAI+C,OAAO,EAAE;MAClBvB,MAAM,CAAC7B,KAAK,GAAGK,KAAK;IACtB;IAEA,IAAI,CAACnB,qBAAqB,CAACmF,OAAO,CAACxC,MAAM,CAAC,CAACC,SAAS,CAAC;MACnDC,IAAI,EAAGC,QAAa,IAAI;QACtBO,OAAO,CAACC,GAAG,CAAC,WAAW,EAAER,QAAQ,CAAC;QAClC,IAAI,CAAC/F,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACqI,cAAc,EAAE;QACrB,IAAIF,cAAc,EAAE;UAClB,IAAI,CAACrB,QAAQ,EAAE;QACjB;QACA,IAAI,CAAC5D,EAAE,CAACwD,YAAY,EAAE;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBL,OAAO,CAACK,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAAC3G,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACV,eAAe,GAAGqH,KAAK,EAAEA,KAAK,EAAEC,OAAO,IAAI,qDAAqD;QACrG,IAAI,CAAC1D,EAAE,CAACwD,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEArF,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACO,uBAAuB,EAAE,EAAE;MACnC,MAAMiG,qBAAqB,GAAG,IAAI,CAACxI,kBAAkB,CAAC6F,GAAG,CAAC,kBAAkB,CAAc;MAC1F2C,qBAAqB,CAAChB,QAAQ,CAACiB,OAAO,CAACpD,OAAO,IAAIA,OAAO,CAACU,aAAa,EAAE,CAAC;MAC1E;IACF;IAEA,IAAI,CAACzD,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACrC,eAAe,GAAG,EAAE;IACzB,MAAM8E,KAAK,GAAG,IAAI,CAAC/E,kBAAkB,CAAC6F,GAAG,CAAC,OAAO,CAAC,EAAEK,KAAK,EAAEyB,IAAI,EAAE;IACjE,MAAMsB,SAAS,GAAG,IAAI,CAACjJ,kBAAkB,CAAC6F,GAAG,CAAC,kBAAkB,CAAC,EAAEK,KAAK;IACxE,MAAMgD,GAAG,GAAGD,SAAS,CAACE,IAAI,CAAC,EAAE,CAAC;IAE9B,IAAI5C,MAAM,GAAqD,EAAE;IACjE,MAAMqB,OAAO,GAAG,4BAA4B,CAACC,IAAI,CAAC9C,KAAK,CAAC;IACxD,MAAM+C,OAAO,GAAG,oBAAoB,CAACD,IAAI,CAAC9C,KAAK,CAAC;IAEhD,IAAI6C,OAAO,EAAE;MACXrB,MAAM,CAACwB,KAAK,GAAGhD,KAAK;IACtB,CAAC,MAAM,IAAI+C,OAAO,EAAE;MAClBvB,MAAM,CAAC7B,KAAK,GAAGK,KAAK;IACtB;IAEAwB,MAAM,CAAC2C,GAAG,GAAGA,GAAG;IAEhB,IAAI,CAACtF,qBAAqB,CAACwF,QAAQ,CAAC7C,MAAM,CAAC,CAACC,SAAS,CAAC;MACpDC,IAAI,EAAGC,QAAa,IAAI;QACtBO,OAAO,CAACC,GAAG,CAAC,cAAc,EAAER,QAAQ,CAAC;QACrC,IAAI,CAACpE,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACmF,QAAQ,EAAE;QACf,IAAI,CAAC5D,EAAE,CAACwD,YAAY,EAAE;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAChF,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACrC,eAAe,GAAGqH,KAAK,EAAEA,KAAK,EAAEC,OAAO,IAAI,8CAA8C;QAC9F,IAAI,CAAC1D,EAAE,CAACwD,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEA2B,cAAcA,CAAA;IACZ,IAAI,CAAC3G,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACf,SAAS,GAAG,EAAE;IAEnB,MAAM+H,UAAU,GAAGC,WAAW,CAAC,MAAK;MAClC,IAAI,CAAChI,SAAS,EAAE;MAChB,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;QACxBiI,aAAa,CAACF,UAAU,CAAC;QACzB,IAAI,CAAChH,gBAAgB,GAAG,IAAI;MAC9B;MACA,IAAI,CAACwB,EAAE,CAACwD,YAAY,EAAE;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAnG,aAAaA,CAACsI,KAAU,EAAEvI,KAAa;IACrC,MAAM8D,KAAK,GAAGyE,KAAK,CAACC,MAAM;IAC1B,IAAI1E,KAAK,CAACmB,KAAK,IAAIjF,KAAK,GAAG,CAAC,EAAE;MAC5B,MAAMyI,SAAS,GACb3E,KAAK,CAAC4E,aAAa,EAAEC,kBAAkB,EAAEC,aAAa,CAAC,OAAO,CAAC;MACjEH,SAAS,EAAEI,KAAK,EAAE;IACpB;EACF;EAEArI,YAAYA,CAAA;IACV,IAAI,CAAC8G,cAAc,EAAE;IACrB,IAAI,CAACtI,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC4I,oBAAoB,CAAC,KAAK,CAAC;EAClC;;qCAjXWpF,cAAc,EAAA3F,EAAA,CAAAiM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnM,EAAA,CAAAiM,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAArM,EAAA,CAAAiM,iBAAA,CAAAK,EAAA,CAAAC,qBAAA,GAAAvM,EAAA,CAAAiM,iBAAA,CAAAjM,EAAA,CAAAwM,iBAAA;EAAA;;UAAd7G,cAAc;IAAA8G,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRzB/M,EAFF,CAAAC,cAAA,aAAgC,aAEH;QAmMzBD,EAjMA,CAAAmB,UAAA,IAAA8L,6BAAA,mBAAiE,IAAAC,6BAAA,mBAkFA,IAAAC,6BAAA,mBAsDA,IAAAC,6BAAA,mBAyDA;QAiErEpN,EADE,CAAAG,YAAA,EAAM,EACF;;;QAlQIH,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAA4B,UAAA,SAAAoL,GAAA,CAAAhH,WAAA,OAAuB;QAkFvBhG,EAAA,CAAAI,SAAA,EAAuB;QAAvBJ,EAAA,CAAA4B,UAAA,SAAAoL,GAAA,CAAAhH,WAAA,OAAuB;QAsDvBhG,EAAA,CAAAI,SAAA,EAAuB;QAAvBJ,EAAA,CAAA4B,UAAA,SAAAoL,GAAA,CAAAhH,WAAA,OAAuB;QAyDvBhG,EAAA,CAAAI,SAAA,EAAuB;QAAvBJ,EAAA,CAAA4B,UAAA,SAAAoL,GAAA,CAAAhH,WAAA,OAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}