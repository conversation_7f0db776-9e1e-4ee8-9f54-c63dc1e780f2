<div class="table-responsive mb-5">
  <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
    <thead>
      <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
        <!-- <th class="w-25px ps-4 rounded-start">
          <div class="form-check form-check-sm form-check-custom form-check-solid">
            <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
              data-kt-check-target=".widget-13-check" />
          </div>
        </th> -->
        <!-- Always visible columns -->
        <th class="min-w-150px cursor-pointer ps-4 rounded-start" (click)="sortData('type')">
          Unit
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('type') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('city_id')">
          City
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('city_id') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('area_id')">
          Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('area_id') }}</span>
        </th>
        <th class="min-w-150px">
          Location on map
        </th>

        <!-- Conditional columns -->
        <th *ngIf="shouldShowColumn('compoundName')" class="min-w-150px cursor-pointer"
          (click)="sortData('compound_name')">
          Compound Name
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('compound_name') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('mallName')" class="min-w-150px cursor-pointer" (click)="sortData('mall_name')">
          Mall Name
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('mall_name') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('buildingNumber')" class="min-w-150px cursor-pointer"
          (click)="sortData('building_number')">
          Property number
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('building_number') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('unitNumber')" class="min-w-150px cursor-pointer" (click)="sortData('unit_number')">
          Unit Number
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_number') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('floor')" class="min-w-150px cursor-pointer" (click)="sortData('floor')">
          Floor
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('floor') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('unitArea')" class="min-w-150px cursor-pointer" (click)="sortData('unit_area')">
          Unit Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('unit_area') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('numberOfRooms')" class="min-w-150px cursor-pointer"
          (click)="sortData('number_of_rooms')">
          Rooms
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('number_of_rooms') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('numberOfBathrooms')" class="min-w-150px cursor-pointer"
          (click)="sortData('number_of_bathrooms')">
          Bathrooms
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('number_of_bathrooms') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('view')" class="min-w-150px cursor-pointer" (click)="sortData('view')">
          View
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('view') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('finishingType')" class="min-w-150px cursor-pointer"
          (click)="sortData('finishing_type')">
          Finishing state
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('finishing_type') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('deliveryDate')" class="min-w-150px cursor-pointer"
          (click)="sortData('delivery_date')">
          Delivery date
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('delivery_date') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('rentRecurrence')" class="min-w-150px cursor-pointer"
          (click)="sortData('rent_recurrence')">
          Rent Recurrence
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('rent_recurrence') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('dailyRent')" class="min-w-150px cursor-pointer" (click)="sortData('daily_rent')">
          Daily Rent
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('daily_rent') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('monthlyRent')" class="min-w-150px cursor-pointer"
          (click)="sortData('monthly_rent')">
          Monthly Rent
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('monthly_rent') }}</span>
        </th>

        <!-- Always visible columns -->
        <th class="min-w-150px cursor-pointer" (click)="sortData('status')">
          Status
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('status') }}</span>
        </th>
        <th *ngIf="shouldShowColumn('unitPlan')" class="min-w-150px">
          Unit plan
        </th>
        <th *ngIf="shouldShowColumn('otherAccessories')" class="min-w-200px cursor-pointer"
          (click)="sortData('other_accessories')">
          Other accessories
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('other_accessories') }}</span>
        </th>
        <th class="min-w-50px text-end rounded-end pe-4">Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let property of rows">
        <!-- Always visible columns -->
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6 ps-4">
            {{ property.type }}
          </span>
        </td>
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.city.name_en }}
          </span>
        </td>
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.area.name_en }}
          </span>
        </td>
        <td>
          <button class="btn btn-icon btn-sm btn-light-primary" data-bs-toggle="tooltip" title="View on map"
            (click)="showImageModal(property.location)">
            <i class="fa-solid fa-map-location-dot"></i>
          </button>
        </td>

        <!-- Conditional columns -->
        <td *ngIf="shouldShowColumn('compoundName')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.compoundName }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('mallName')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.mallName }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('buildingNumber')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.buildingNumber }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('unitNumber')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitNumber }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('floor')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.floor }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('unitArea')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.unitArea }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('numberOfRooms')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.numberOfRooms }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('numberOfBathrooms')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.numberOfBathrooms }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('view')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.view }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('finishingType')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.finishingType }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('deliveryDate')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.deliveryDate | date : "dd/MM/yyyy" }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('rentRecurrence')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.rentRecurrence }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('dailyRent')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.dailyRent | currency:'EGP':'symbol':'1.0-0' }}
          </span>
        </td>
        <td *ngIf="shouldShowColumn('monthlyRent')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.monthlyRent | currency:'EGP':'symbol':'1.0-0' }}
          </span>
        </td>

        <!-- Always visible columns -->
        <td>
          <span class="badge badge-dark-blue">{{ property.status }}</span>
        </td>
        <td *ngIf="shouldShowColumn('unitPlan')">
          <button class="btn btn-sm btn-light-info" (click)="showUnitPlanModal(property.diagram)">
            <i class="fa-solid fa-file-image me-1"></i> View Plan
          </button>
        </td>
        <td *ngIf="shouldShowColumn('otherAccessories')">
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.otherAccessories }}
          </span>
        </td>
        <td class="text-end pe-4">
          <div class="dropdown">
            <button class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" type="button"
              data-bs-toggle="dropdown">
              <i class="fa-solid fa-ellipsis-vertical"></i>
            </button>
            <ul class="dropdown-menu">
              <li>
                <button class="dropdown-item" (click)="viewProperty(property)">
                  <i class="fa-solid fa-eye me-2"></i> View unit Details
                </button>
              </li>
            </ul>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  <div class="m-2">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.size" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>
</div>

<app-view-apartment-model [selectedUnitPlanImage]="selectedUnitPlanImage"></app-view-apartment-model>