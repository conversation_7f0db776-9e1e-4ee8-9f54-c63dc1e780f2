<div class="table-responsive mb-5">
  <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5">
    <thead>
      <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
        <!-- <th class="w-25px ps-4 rounded-start">
          <div class="form-check form-check-sm form-check-custom form-check-solid">
            <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
              data-kt-check-target=".widget-13-check" />
          </div>
        </th> -->
        <th class="min-w-150px cursor-pointer ps-4 rounded-start" (click)="sortData('type')">
          Unit
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('type') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('city_id')">
          City
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('city_id') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('area_id')">
          Area
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('area_id') }}</span>
        </th>
        <th class="min-w-150px">
          Location on map
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('building_number')">
          Property number
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('building_number') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('view')">
          View
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('view') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('floor')">
          Floor
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('floor') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('delivery_date')">
          Delivery date
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('delivery_date') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('finishing_type')">
          Finishing state
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('finishing_type') }}</span>
        </th>
        <th class="min-w-150px cursor-pointer" (click)="sortData('status')">
          Status
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('status') }}</span>
        </th>
        <th class="min-w-150px">
          Unit plan
        </th>
        <th class="min-w-200px cursor-pointer" (click)="sortData('other_accessories')">
          Other accessories
          <span class="ms-1 text-primary fw-bold">{{ getSortArrow('other_accessories') }}</span>
        </th>
        <th class="min-w-50px text-end rounded-end pe-4">Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let property of rows">
        <!-- <td class="ps-4">
          <div class="form-check form-check-sm form-check-custom form-check-solid">
            <input class="form-check-input widget-13-check" type="checkbox" value="1" />
          </div>
        </td> -->
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6 ps-4">
            {{ property.type }}
          </span>
        </td>
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.city.name_en }}
          </span>
        </td>
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.area.name_en }}
          </span>
        </td>

        <td>
          <button class="btn btn-icon btn-sm btn-light-primary" data-bs-toggle="tooltip" title="View on map"
            (click)="showImageModal(property.location)">
            <i class="fa-solid fa-map-location-dot"></i>
          </button>
        </td>

        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.buildingNumber }}
          </span>
        </td>
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.view }}
          </span>
        </td>

        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.floor }}
          </span>
        </td>
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.deliveryDate | date : "dd/MM/yyyy" }}
          </span>
        </td>
        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.finishingType }}
          </span>
        </td>
        <td>
          <span class="badge badge-dark-blue">{{ property.status }}</span>
        </td>
        <td>
          <button class="btn btn-sm btn-light-info" (click)="showUnitPlanModal(property.diagram)">
            <i class="fa-solid fa-file-image me-1"></i> View Plan
          </button>
        </td>

        <td>
          <span class="text-gray-900 fw-bold d-block mb-1 fs-6">
            {{ property.otherAccessories }}
          </span>
        </td>
        <td class="text-end pe-4">
          <div class="dropdown">
            <button class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" type="button"
              data-bs-toggle="dropdown">
              <i class="fa-solid fa-ellipsis-vertical"></i>
            </button>
            <ul class="dropdown-menu">
              <li>
                <button class="dropdown-item" (click)="viewProperty(property)">
                  <i class="fa-solid fa-eye me-2"></i> View unit Details
                </button>
              </li>
            </ul>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
  <div class="m-2">
    <app-pagination [totalItems]="page.totalElements" [itemsPerPage]="page.size" [currentPage]="page.pageNumber"
      (pageChange)="onPageChange($event)">
    </app-pagination>
  </div>
</div>

<app-view-apartment-model [selectedUnitPlanImage]="selectedUnitPlanImage"></app-view-apartment-model>
