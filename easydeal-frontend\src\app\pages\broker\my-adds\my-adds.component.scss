.image-navigation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.position-relative:hover .image-navigation {
  opacity: 1;
}

.image-nav-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 0.8;
  transition: opacity 0.3s, transform 0.3s;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }

  i {
    font-size: 12px;
  }
}

.prev-btn {
  margin-right: auto;
}

.next-btn {
  margin-left: auto;
}

.image-counter {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  z-index: 10;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 0.25rem;

  i {
    font-size: 40px;
    color: white;
    opacity: 0.9;
  }
}

.gallery-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(253, 0, 0, 0.08);
  border-radius: 5%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;

  i {
    font-size: 14px;
    color: white;
  }
}

.media-container:hover .gallery-overlay {
  opacity: 1;
}

.media-container {
  position: relative;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.02);
  }

  img {
    transition: filter 0.2s;
  }

  &:hover img {
    filter: brightness(1.1);
  }

  // Add a subtle overlay to indicate it's clickable
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      transparent 0%,
      rgba(0, 123, 255, 0.1) 50%,
      transparent 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: 0.25rem;
  }

  &:hover::after {
    opacity: 1;
  }
}

// Card hover effects
.card.cursor-pointer {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
}

// Styles for the media modal
::ng-deep .media-modal {
  .modal-dialog {
    max-width: 800px;
  }

  .modal-content {
    background-color: #fff;
  }

  .modal-header {
    border-bottom-color: #333;
  }

  .btn-close {
    filter: invert(1);
  }

  video {
    width: 100%;
    max-height: 70vh;
    outline: none;
  }
}
