import { Injectable } from '@angular/core';
import { <PERSON>ttpRe<PERSON>, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  intercept(request: HttpRequest<any>, next: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>): Observable<HttpEvent<any>> {
    // Get token from localStorage or a service
    const authToken = localStorage.getItem('authToken');
    console.log(localStorage.getItem('currentUser'));
    const publicEndpoints = ['/login', '/register', '/send-otp', '/check-otp', '/reset-password'];
    const isPublic = publicEndpoints.some(url => request.url.includes(url));

    // const headersConfig: { [key: string]: string } = {
    //   Accept: 'application/json',
    //   'Content-Type': 'application/json',
    // };

    if (authToken && !isPublic) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${authToken}`
        }
      });
    }

    // request = request.clone({ setHeaders: headersConfig });

    return next.handle(request);
  }
}
