{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/property.service\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"@angular/common\";\nfunction PropertyDetailsComponent_div_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 48);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const item_r2 = ctx_r0.$implicit;\n    const i_r3 = ctx_r0.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r2.url || \"assets/media/auth/404-error.png\", i0.ɵɵsanitizeUrl)(\"alt\", \"Unit Image \" + (i_r3 + 1));\n  }\n}\nfunction PropertyDetailsComponent_div_10_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"video\", 49);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r2.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PropertyDetailsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, PropertyDetailsComponent_div_10_ng_container_1_Template, 2, 2, \"ng-container\", 28)(2, PropertyDetailsComponent_div_10_ng_container_2_Template, 2, 1, \"ng-container\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r3 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.type === \"video\");\n  }\n}\nfunction PropertyDetailsComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"button\", 50);\n  }\n  if (rf & 2) {\n    const i_r4 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r4 === 0);\n    i0.ɵɵattribute(\"data-bs-slide-to\", i_r4);\n  }\n}\nfunction PropertyDetailsComponent_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"It spans an area of \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \".\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(4, 1, ctx_r4.unitDetails == null ? null : ctx_r4.unitDetails.unitArea, \"1.0-0\"), \" m\\u00B2\");\n  }\n}\nfunction PropertyDetailsComponent_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"The price per meter in cash is \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \",\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(4, 1, ctx_r4.unitDetails == null ? null : ctx_r4.unitDetails.pricePerMeterInCash, \"1.0-0\"), \" EGP\");\n  }\n}\nfunction PropertyDetailsComponent_span_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" while the price per meter in installments is \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \".\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(4, 1, ctx_r4.unitDetails == null ? null : ctx_r4.unitDetails.pricePerMeterInInstallment, \"1.0-0\"), \" EGP\");\n  }\n}\nfunction PropertyDetailsComponent_span_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"The total cash price is \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \",\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(4, 1, ctx_r4.unitDetails == null ? null : ctx_r4.unitDetails.totalPriceInCash, \"1.0-0\"), \" EGP\");\n  }\n}\nfunction PropertyDetailsComponent_span_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" and the total installment price is \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \".\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(4, 1, ctx_r4.unitDetails == null ? null : ctx_r4.unitDetails.totalPriceInInstallment, \"1.0-0\"), \" EGP\");\n  }\n}\nfunction PropertyDetailsComponent_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵelement(2, \"input\", 53);\n    i0.ɵɵelementStart(3, \"label\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const feature_r6 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r4.isFeatureEnabled(feature_r6.value));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r6.name);\n  }\n}\nexport class PropertyDetailsComponent {\n  route;\n  router;\n  propertyService;\n  modalService;\n  sanitizer;\n  cd;\n  property = null;\n  isLoading = true;\n  propertyId;\n  // Media modal properties\n  selectedMediaUrl = null;\n  selectedMediaType = 'image';\n  safeVideoUrl = null;\n  modalMediaItems = [];\n  currentModalIndex = 0;\n  constructor(route, router, propertyService, modalService, sanitizer, cd) {\n    this.route = route;\n    this.router = router;\n    this.propertyService = propertyService;\n    this.modalService = modalService;\n    this.sanitizer = sanitizer;\n    this.cd = cd;\n  }\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      this.propertyId = +params['id'];\n      if (this.propertyId) {\n        this.loadPropertyDetails();\n      }\n    });\n  }\n  loadPropertyDetails() {\n    this.isLoading = true;\n    this.propertyService.getById(this.propertyId).subscribe({\n      next: response => {\n        this.property = response.data || response;\n        this.isLoading = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading property details:', error);\n        this.isLoading = false;\n        this.cd.detectChanges();\n      }\n    });\n  }\n  // Navigate back to my adds\n  goBack() {\n    this.router.navigate(['/broker/Adds']);\n  }\n  // Get all media items from gallery\n  getAllImagesFromGallery(property) {\n    if (property.gallery && property.gallery.length > 0) {\n      const mediaItems = property.gallery.filter(item => item.url);\n      if (mediaItems.length > 0) {\n        return mediaItems;\n      }\n    }\n    return [{\n      url: './assets/media/auth/404-error.png',\n      type: 'image'\n    }];\n  }\n  // Open media modal\n  openMediaModal(content) {\n    if (!this.property) return;\n    this.modalMediaItems = this.getAllImagesFromGallery(this.property);\n    this.currentModalIndex = 0;\n    this.updateModalMedia();\n    this.modalService.open(content, {\n      centered: true,\n      size: 'lg',\n      windowClass: 'media-modal',\n      backdrop: 'static'\n    });\n  }\n  // Update modal media based on current index\n  updateModalMedia() {\n    if (this.modalMediaItems.length > 0 && this.currentModalIndex >= 0 && this.currentModalIndex < this.modalMediaItems.length) {\n      const currentItem = this.modalMediaItems[this.currentModalIndex];\n      this.selectedMediaUrl = currentItem.url;\n      this.selectedMediaType = currentItem.type;\n      if (this.selectedMediaType === 'video' && this.selectedMediaUrl) {\n        this.safeVideoUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.selectedMediaUrl);\n      } else {\n        this.safeVideoUrl = null;\n      }\n    }\n  }\n  // Navigate to next media in modal\n  nextModalMedia() {\n    if (this.modalMediaItems.length > 1) {\n      this.currentModalIndex = (this.currentModalIndex + 1) % this.modalMediaItems.length;\n      this.updateModalMedia();\n    }\n  }\n  // Navigate to previous media in modal\n  prevModalMedia() {\n    if (this.modalMediaItems.length > 1) {\n      this.currentModalIndex = this.currentModalIndex > 0 ? this.currentModalIndex - 1 : this.modalMediaItems.length - 1;\n      this.updateModalMedia();\n    }\n  }\n  // Check if modal has multiple media items\n  hasMultipleModalMedia() {\n    return this.modalMediaItems.length > 1;\n  }\n  // Get main image for display\n  getMainImage() {\n    if (this.property && this.property.gallery && this.property.gallery.length > 0) {\n      const firstImage = this.property.gallery.find(item => item.url && item.type === 'image');\n      if (firstImage) {\n        return firstImage.url;\n      }\n    }\n    return './assets/media/auth/404-error.png';\n  }\n  // Check if property has gallery\n  hasGallery() {\n    return this.property && this.property.gallery && this.property.gallery.length > 0;\n  }\n  // Format price\n  formatPrice(price) {\n    if (!price) return 'Price not available';\n    return new Intl.NumberFormat('en-US').format(price) + ' EGP';\n  }\n  // Get property type display name\n  getPropertyTypeDisplay() {\n    if (!this.property?.type) return 'N/A';\n    return this.property.type.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  }\n  static ɵfac = function PropertyDetailsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PropertyDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.PropertyService), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.DomSanitizer), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PropertyDetailsComponent,\n    selectors: [[\"app-property-details\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 113,\n    vars: 28,\n    consts: [[1, \"mb-5\", \"mt-0\"], [1, \"row\"], [1, \"col-lg-8\"], [1, \"card\", \"mb-5\"], [1, \"card-body\", \"p-0\"], [1, \"position-relative\", \"unit-image-container\"], [1, \"unit-image-background\"], [1, \"h-100\"], [\"id\", \"unitImagesCarousel\", \"data-bs-ride\", \"carousel\", \"data-bs-interval\", \"3000\", 1, \"carousel\", \"slide\", \"h-100\"], [1, \"carousel-inner\", \"h-100\"], [\"class\", \"carousel-item h-100\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"carousel-indicators\"], [\"type\", \"button\", \"data-bs-target\", \"#unitImagesCarousel\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"position-absolute\", \"top-0\", \"start-0\", \"m-3\"], [1, \"badge\", \"bg-pink\", \"text-white\", \"fs-7\", \"px-3\", \"py-2\", \"rounded-pill\"], [1, \"position-absolute\", \"bottom-0\", \"start-0\", \"end-0\", \"unit-info-overlay\"], [1, \"p-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-2\"], [1, \"text-white\", \"fw-bold\", \"mb-0\", \"me-3\"], [1, \"fa-solid\", \"fa-check-circle\", \"text-success\", \"fa-lg\"], [1, \"text-white\", \"mb-0\"], [1, \"fa-solid\", \"fa-location-dot\", \"me-2\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"card-body\"], [1, \"property-details-text\"], [1, \"mb-3\", \"fs-5\"], [4, \"ngIf\"], [1, \"col-lg-4\"], [1, \"card\", \"mb-4\"], [1, \"card-body\", \"text-center\"], [1, \"text-success\", \"fw-bold\", \"mb-2\"], [1, \"d-flex\", \"justify-content-center\", \"gap-2\", \"mb-3\"], [1, \"badge\", \"bg-light-primary\", \"text-primary\"], [1, \"badge\", \"bg-light-success\", \"text-success\"], [1, \"row\", \"g-3\"], [1, \"col-6\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fa-solid\", \"fa-bed\", \"text-primary\", \"me-2\"], [1, \"text-muted\", \"d-block\"], [1, \"fw-semibold\"], [1, \"fa-solid\", \"fa-bath\", \"text-primary\", \"me-2\"], [1, \"fa-solid\", \"fa-ruler-combined\", \"text-primary\", \"me-2\"], [1, \"fa-solid\", \"fa-building\", \"text-primary\", \"me-2\"], [1, \"row\", \"g-2\"], [\"class\", \"col-12\", 4, \"ngFor\", \"ngForOf\"], [1, \"carousel-item\", \"h-100\"], [1, \"d-block\", \"w-100\", \"h-100\", \"cursor-pointer\", 2, \"object-fit\", \"cover\", \"border-radius\", \"0.5rem\", 3, \"src\", \"alt\"], [\"controls\", \"\", 1, \"d-block\", \"w-100\", \"h-100\", \"cursor-pointer\", 2, \"object-fit\", \"cover\", \"border-radius\", \"0.5rem\", 3, \"src\"], [\"type\", \"button\", \"data-bs-target\", \"#unitImagesCarousel\"], [1, \"col-12\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"disabled\", \"\", 1, \"form-check-input\", 3, \"checked\"], [1, \"form-check-label\", \"text-dark\"]],\n    template: function PropertyDetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9);\n        i0.ɵɵtemplate(10, PropertyDetailsComponent_div_10_Template, 3, 4, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 11);\n        i0.ɵɵtemplate(12, PropertyDetailsComponent_button_12_Template, 1, 3, \"button\", 12);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(13, \"div\", 13)(14, \"span\", 14);\n        i0.ɵɵtext(15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 15)(17, \"div\", 16)(18, \"div\", 17)(19, \"h2\", 18);\n        i0.ɵɵtext(20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(21, \"i\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"p\", 20);\n        i0.ɵɵelement(23, \"i\", 21);\n        i0.ɵɵtext(24);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(25, \"div\", 22)(26, \"div\", 23)(27, \"h5\", 24);\n        i0.ɵɵtext(28, \"Property details\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(29, \"div\", 25)(30, \"div\", 26)(31, \"p\", 27);\n        i0.ɵɵtext(32, \" This is a \");\n        i0.ɵɵelementStart(33, \"strong\");\n        i0.ɵɵtext(34);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(35, \" unit located in \");\n        i0.ɵɵelementStart(36, \"strong\");\n        i0.ɵɵtext(37);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(38, \". The unit is situated in building number \");\n        i0.ɵɵelementStart(39, \"strong\");\n        i0.ɵɵtext(40);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(41, \" on the \");\n        i0.ɵɵelementStart(42, \"strong\");\n        i0.ɵɵtext(43);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(44, \" floor. \");\n        i0.ɵɵtemplate(45, PropertyDetailsComponent_span_45_Template, 6, 4, \"span\", 28);\n        i0.ɵɵtext(46, \" The unit features \");\n        i0.ɵɵelementStart(47, \"strong\");\n        i0.ɵɵtext(48);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(49, \" finishing and is available with \");\n        i0.ɵɵelementStart(50, \"strong\");\n        i0.ɵɵtext(51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(52, \" payment options. \");\n        i0.ɵɵtemplate(53, PropertyDetailsComponent_span_53_Template, 6, 4, \"span\", 28)(54, PropertyDetailsComponent_span_54_Template, 6, 4, \"span\", 28)(55, PropertyDetailsComponent_span_55_Template, 6, 4, \"span\", 28)(56, PropertyDetailsComponent_span_56_Template, 6, 4, \"span\", 28);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(57, \"div\", 29)(58, \"div\", 30)(59, \"div\", 31)(60, \"h3\", 32);\n        i0.ɵɵtext(61);\n        i0.ɵɵpipe(62, \"number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(63, \"div\", 33)(64, \"span\", 34);\n        i0.ɵɵtext(65, \"Compound\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(66, \"span\", 35);\n        i0.ɵɵtext(67);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(68, \"div\", 30)(69, \"div\", 23)(70, \"h5\", 24);\n        i0.ɵɵtext(71, \"Unit Information\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(72, \"div\", 25)(73, \"div\", 36)(74, \"div\", 37)(75, \"div\", 38);\n        i0.ɵɵelement(76, \"i\", 39);\n        i0.ɵɵelementStart(77, \"div\")(78, \"small\", 40);\n        i0.ɵɵtext(79, \"Bedrooms\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(80, \"span\", 41);\n        i0.ɵɵtext(81);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(82, \"div\", 37)(83, \"div\", 38);\n        i0.ɵɵelement(84, \"i\", 42);\n        i0.ɵɵelementStart(85, \"div\")(86, \"small\", 40);\n        i0.ɵɵtext(87, \"Bathrooms\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(88, \"span\", 41);\n        i0.ɵɵtext(89);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(90, \"div\", 37)(91, \"div\", 38);\n        i0.ɵɵelement(92, \"i\", 43);\n        i0.ɵɵelementStart(93, \"div\")(94, \"small\", 40);\n        i0.ɵɵtext(95, \"Area\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(96, \"span\", 41);\n        i0.ɵɵtext(97);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(98, \"div\", 37)(99, \"div\", 38);\n        i0.ɵɵelement(100, \"i\", 44);\n        i0.ɵɵelementStart(101, \"div\")(102, \"small\", 40);\n        i0.ɵɵtext(103, \"Floor\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(104, \"span\", 41);\n        i0.ɵɵtext(105);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(106, \"div\", 30)(107, \"div\", 23)(108, \"h5\", 24);\n        i0.ɵɵtext(109, \"Features\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(110, \"div\", 25)(111, \"div\", 45);\n        i0.ɵɵtemplate(112, PropertyDetailsComponent_div_112_Template, 5, 2, \"div\", 46);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngForOf\", ctx.AllImages);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.AllImages);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.unitDetails == null ? null : ctx.unitDetails.type) || \"unknown\", \" \");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate2(\" \", (ctx.unitDetails == null ? null : ctx.unitDetails.modelCode) || \"unknown unit code\", \" - \", (ctx.unitDetails == null ? null : ctx.unitDetails.numberOfRooms) || \"unknown\", \" Rooms \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.unitDetails.city == null ? null : ctx.unitDetails.city.name_en) || \"unknown\", \" \");\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.type);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate2(\"\", ctx.unitDetails == null ? null : ctx.unitDetails.area == null ? null : ctx.unitDetails.area.name_en, \", \", ctx.unitDetails == null ? null : ctx.unitDetails.city == null ? null : ctx.unitDetails.city.name_en, \"\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.buildingNumber);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.floor);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.unitDetails == null ? null : ctx.unitDetails.unitArea);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.finishingType);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.paymentSystem);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.unitDetails == null ? null : ctx.unitDetails.pricePerMeterInCash);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.unitDetails == null ? null : ctx.unitDetails.pricePerMeterInInstallment);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.unitDetails == null ? null : ctx.unitDetails.totalPriceInCash);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.unitDetails == null ? null : ctx.unitDetails.totalPriceInInstallment);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(62, 25, ctx.unitDetails == null ? null : ctx.unitDetails.totalPriceInCash, \"1.0-0\"), \" EGP \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.status);\n        i0.ɵɵadvance(14);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.numberOfRooms);\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.numberOfBathrooms);\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate1(\"\", ctx.unitDetails == null ? null : ctx.unitDetails.unitArea, \" m\\u00B2\");\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.unitDetails == null ? null : ctx.unitDetails.floor);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngForOf\", ctx.features);\n      }\n    },\n    dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DecimalPipe],\n    styles: [\".property-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  border-radius: 0.5rem;\\n}\\n.property-image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.property-image-container[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: rgba(var(--bs-primary-rgb), 0.1);\\n  color: var(--bs-primary);\\n}\\n\\n.price-display[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n  .media-modal .modal-dialog {\\n  max-width: 900px;\\n}\\n  .media-modal .modal-content {\\n  background-color: #000;\\n  color: #fff;\\n}\\n  .media-modal .modal-header {\\n  border-bottom-color: #333;\\n}\\n  .media-modal .modal-footer {\\n  border-top-color: #333;\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n  .media-modal .btn-close {\\n  filter: invert(1);\\n}\\n  .media-modal video {\\n  width: 100%;\\n  max-height: 70vh;\\n  outline: none;\\n}\\n  .media-modal .modal-navigation .btn {\\n  background-color: rgba(255, 255, 255, 0.2);\\n  border: none;\\n  color: white;\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n}\\n  .media-modal .modal-navigation .btn:hover {\\n  background-color: rgba(255, 255, 255, 0.4);\\n  transform: scale(1.1);\\n}\\n  .media-modal .modal-navigation .btn i {\\n  font-size: 18px;\\n}\\n  .media-modal .modal-footer .btn {\\n  transition: all 0.3s ease;\\n}\\n  .media-modal .modal-footer .btn.btn-primary {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n  .media-modal .modal-footer .btn.btn-light {\\n  background-color: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n  .media-modal .modal-footer .btn.btn-light:hover {\\n  background-color: rgba(255, 255, 255, 0.5);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n@media (max-width: 768px) {\\n  .property-details-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .feature-grid[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n}\\n.loading-spinner[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.card[_ngcontent-%COMP%] {\\n  transition: box-shadow 0.3s ease;\\n}\\n.card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);\\n}\\n\\n.btn-back[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.btn-back[_ngcontent-%COMP%]:hover {\\n  transform: translateX(-2px);\\n}\\n\\n.gallery-overlay-btn[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.7);\\n  border: none;\\n  transition: all 0.3s ease;\\n}\\n.gallery-overlay-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-2px);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "item_r2", "url", "ɵɵsanitizeUrl", "i_r3", "ɵɵelementStart", "ɵɵtemplate", "PropertyDetailsComponent_div_10_ng_container_1_Template", "PropertyDetailsComponent_div_10_ng_container_2_Template", "ɵɵelementEnd", "ɵɵclassProp", "type", "i_r4", "ɵɵtext", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r4", "unitDetails", "unitArea", "pricePerMeterInCash", "pricePerMeterInInstallment", "totalPriceInCash", "totalPriceInInstallment", "isFeatureEnabled", "feature_r6", "value", "ɵɵtextInterpolate", "name", "PropertyDetailsComponent", "route", "router", "propertyService", "modalService", "sanitizer", "cd", "property", "isLoading", "propertyId", "selectedMediaUrl", "selectedMediaType", "safeVideoUrl", "modalMediaItems", "currentModalIndex", "constructor", "ngOnInit", "params", "subscribe", "loadPropertyDetails", "getById", "next", "response", "data", "detectChanges", "error", "console", "goBack", "navigate", "getAllImagesFromGallery", "gallery", "length", "mediaItems", "filter", "item", "openMediaModal", "content", "updateModalMedia", "open", "centered", "size", "windowClass", "backdrop", "currentItem", "bypassSecurityTrustResourceUrl", "nextModalMedia", "prevModalMedia", "hasMultipleModalMedia", "getMainImage", "firstImage", "find", "hasGallery", "formatPrice", "price", "Intl", "NumberFormat", "format", "getPropertyTypeDisplay", "replace", "l", "toUpperCase", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "PropertyService", "i3", "NgbModal", "i4", "Dom<PERSON><PERSON><PERSON>zer", "ChangeDetectorRef", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PropertyDetailsComponent_Template", "rf", "ctx", "PropertyDetailsComponent_div_10_Template", "PropertyDetailsComponent_button_12_Template", "PropertyDetailsComponent_span_45_Template", "PropertyDetailsComponent_span_53_Template", "PropertyDetailsComponent_span_54_Template", "PropertyDetailsComponent_span_55_Template", "PropertyDetailsComponent_span_56_Template", "PropertyDetailsComponent_div_112_Template", "AllImages", "ɵɵtextInterpolate2", "modelCode", "numberOfRooms", "city", "name_en", "area", "buildingNumber", "floor", "finishingType", "paymentSystem", "status", "numberOfBathrooms", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\property-details\\property-details.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\property-details\\property-details.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { PropertyService } from '../services/property.service';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-property-details',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './property-details.component.html',\r\n  styleUrl: './property-details.component.scss',\r\n})\r\nexport class PropertyDetailsComponent implements OnInit {\r\n  property: any = null;\r\n  isLoading = true;\r\n  propertyId: number;\r\n\r\n  // Media modal properties\r\n  selectedMediaUrl: string | null = null;\r\n  selectedMediaType: string = 'image';\r\n  safeVideoUrl: SafeResourceUrl | null = null;\r\n  modalMediaItems: any[] = [];\r\n  currentModalIndex: number = 0;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private propertyService: PropertyService,\r\n    private modalService: NgbModal,\r\n    private sanitizer: DomSanitizer,\r\n    private cd: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.route.params.subscribe((params) => {\r\n      this.propertyId = +params['id'];\r\n      if (this.propertyId) {\r\n        this.loadPropertyDetails();\r\n      }\r\n    });\r\n  }\r\n\r\n  loadPropertyDetails(): void {\r\n    this.isLoading = true;\r\n    this.propertyService.getById(this.propertyId).subscribe({\r\n      next: (response) => {\r\n        this.property = response.data || response;\r\n        this.isLoading = false;\r\n        this.cd.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading property details:', error);\r\n        this.isLoading = false;\r\n        this.cd.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  // Navigate back to my adds\r\n  goBack(): void {\r\n    this.router.navigate(['/broker/Adds']);\r\n  }\r\n\r\n  // Get all media items from gallery\r\n  getAllImagesFromGallery(property: any): any[] {\r\n    if (property.gallery && property.gallery.length > 0) {\r\n      const mediaItems = property.gallery.filter((item: any) => item.url);\r\n      if (mediaItems.length > 0) {\r\n        return mediaItems;\r\n      }\r\n    }\r\n    return [{ url: './assets/media/auth/404-error.png', type: 'image' }];\r\n  }\r\n\r\n  // Open media modal\r\n  openMediaModal(content: any): void {\r\n    if (!this.property) return;\r\n\r\n    this.modalMediaItems = this.getAllImagesFromGallery(this.property);\r\n    this.currentModalIndex = 0;\r\n    this.updateModalMedia();\r\n\r\n    this.modalService.open(content, {\r\n      centered: true,\r\n      size: 'lg',\r\n      windowClass: 'media-modal',\r\n      backdrop: 'static',\r\n    });\r\n  }\r\n\r\n  // Update modal media based on current index\r\n  updateModalMedia(): void {\r\n    if (\r\n      this.modalMediaItems.length > 0 &&\r\n      this.currentModalIndex >= 0 &&\r\n      this.currentModalIndex < this.modalMediaItems.length\r\n    ) {\r\n      const currentItem = this.modalMediaItems[this.currentModalIndex];\r\n      this.selectedMediaUrl = currentItem.url;\r\n      this.selectedMediaType = currentItem.type;\r\n\r\n      if (this.selectedMediaType === 'video' && this.selectedMediaUrl) {\r\n        this.safeVideoUrl = this.sanitizer.bypassSecurityTrustResourceUrl(\r\n          this.selectedMediaUrl\r\n        );\r\n      } else {\r\n        this.safeVideoUrl = null;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Navigate to next media in modal\r\n  nextModalMedia(): void {\r\n    if (this.modalMediaItems.length > 1) {\r\n      this.currentModalIndex =\r\n        (this.currentModalIndex + 1) % this.modalMediaItems.length;\r\n      this.updateModalMedia();\r\n    }\r\n  }\r\n\r\n  // Navigate to previous media in modal\r\n  prevModalMedia(): void {\r\n    if (this.modalMediaItems.length > 1) {\r\n      this.currentModalIndex =\r\n        this.currentModalIndex > 0\r\n          ? this.currentModalIndex - 1\r\n          : this.modalMediaItems.length - 1;\r\n      this.updateModalMedia();\r\n    }\r\n  }\r\n\r\n  // Check if modal has multiple media items\r\n  hasMultipleModalMedia(): boolean {\r\n    return this.modalMediaItems.length > 1;\r\n  }\r\n\r\n  // Get main image for display\r\n  getMainImage(): string {\r\n    if (this.property && this.property.gallery && this.property.gallery.length > 0) {\r\n      const firstImage = this.property.gallery.find((item: any) => item.url && item.type === 'image');\r\n      if (firstImage) {\r\n        return firstImage.url;\r\n      }\r\n    }\r\n    return './assets/media/auth/404-error.png';\r\n  }\r\n\r\n  // Check if property has gallery\r\n  hasGallery(): boolean {\r\n    return this.property && this.property.gallery && this.property.gallery.length > 0;\r\n  }\r\n\r\n  // Format price\r\n  formatPrice(price: any): string {\r\n    if (!price) return 'Price not available';\r\n    return new Intl.NumberFormat('en-US').format(price) + ' EGP';\r\n  }\r\n\r\n  // Get property type display name\r\n  getPropertyTypeDisplay(): string {\r\n    if (!this.property?.type) return 'N/A';\r\n    return this.property.type.replace(/_/g, ' ').replace(/\\b\\w/g, (l: string) => l.toUpperCase());\r\n  }\r\n}\r\n", "<!-- Header -->\r\n<div class=\"mb-5 mt-0\">\r\n\r\n</div>\r\n\r\n<!-- Unit Details Content -->\r\n<div class=\"row\">\r\n  <!-- Left Column - Main Image and Gallery -->\r\n  <div class=\"col-lg-8\">\r\n    <!-- Main Unit Image -->\r\n    <div class=\"card mb-5\">\r\n      <div class=\"card-body p-0\">\r\n        <div class=\"position-relative unit-image-container\">\r\n          <!-- Background with gradient -->\r\n          <div class=\"unit-image-background\">\r\n            <!-- Bootstrap Carousel -->\r\n            <div class=\"h-100\">\r\n              <div\r\n                id=\"unitImagesCarousel\"\r\n                class=\"carousel slide h-100\"\r\n                data-bs-ride=\"carousel\"\r\n                data-bs-interval=\"3000\"\r\n              >\r\n                <!-- Carousel Inner -->\r\n                <div class=\"carousel-inner h-100\">\r\n                  <!-- <div class=\"carousel-item h-100\" *ngFor=\"let image of unitImages; let i = index\"\r\n                    [class.active]=\"i === 0\">\r\n                    <img [src]=\"image.url || 'assets/media/auth/404-error.png'\" [alt]=\"'Unit Image ' + (i + 1)\"\r\n                      class=\"d-block w-100 h-100 cursor-pointer\" style=\"object-fit: cover; border-radius: 0.5rem\" />\r\n                  </div> -->\r\n\r\n                  <!-- Gallery Items -->\r\n                  <div\r\n                    class=\"carousel-item h-100\"\r\n                    *ngFor=\"let item of AllImages; let i = index\"\r\n                    [class.active]=\"i === 0\"\r\n                  >\r\n                    <ng-container *ngIf=\"item.type === 'image'\">\r\n                      <img\r\n                        [src]=\"item.url || 'assets/media/auth/404-error.png'\"\r\n                        [alt]=\"'Unit Image ' + (i + 1)\"\r\n                        class=\"d-block w-100 h-100 cursor-pointer\"\r\n                        style=\"object-fit: cover; border-radius: 0.5rem\"\r\n                      />\r\n                    </ng-container>\r\n                    <ng-container *ngIf=\"item.type === 'video'\">\r\n                      <video\r\n                        [src]=\"item.url\"\r\n                        controls\r\n                        class=\"d-block w-100 h-100 cursor-pointer\"\r\n                        style=\"object-fit: cover; border-radius: 0.5rem\"\r\n                      ></video>\r\n                    </ng-container>\r\n                  </div>\r\n                  <!-- <div class=\"carousel-item h-100\" *ngIf=\"unitDetails?.diagram\">\r\n                    <img [src]=\"unitDetails?.diagram || 'assets/media/auth/404-error.png'\" [alt]=\"'Unit Diagram'\"\r\n                      class=\"d-block w-100 h-100 cursor-pointer\" style=\"object-fit: cover; border-radius: 0.5rem\" />\r\n                  </div> -->\r\n                  <!-- Location in Master Plan Item -->\r\n                  <!-- <div class=\"carousel-item h-100\" *ngIf=\"unitDetails?.locationInMasterPlan\">\r\n                    <img [src]=\" unitDetails?.locationInMasterPlan|| 'assets/media/auth/404-error.png'\"\r\n                      alt=\"Location in Master Plan\" class=\"d-block w-100 h-100 cursor-pointer\"\r\n                      style=\"object-fit: cover; border-radius: 0.5rem\" />\r\n                  </div> -->\r\n                </div>\r\n\r\n                <!-- Carousel Indicators -->\r\n                <div class=\"carousel-indicators\">\r\n                  <button\r\n                    type=\"button\"\r\n                    data-bs-target=\"#unitImagesCarousel\"\r\n                    *ngFor=\"let image of AllImages; let i = index\"\r\n                    [attr.data-bs-slide-to]=\"i\"\r\n                    [class.active]=\"i === 0\"\r\n                  ></button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Status Badge -->\r\n          <div class=\"position-absolute top-0 start-0 m-3\">\r\n            <span class=\"badge bg-pink text-white fs-7 px-3 py-2 rounded-pill\">\r\n              {{ unitDetails?.type || \"unknown\" }}\r\n            </span>\r\n          </div>\r\n\r\n          <!-- Unit Information Overlay -->\r\n          <div\r\n            class=\"position-absolute bottom-0 start-0 end-0 unit-info-overlay\"\r\n          >\r\n            <div class=\"p-4\">\r\n              <div class=\"d-flex align-items-center mb-2\">\r\n                <h2 class=\"text-white fw-bold mb-0 me-3\">\r\n                  {{ unitDetails?.modelCode || \"unknown unit code\" }} -\r\n                  {{ unitDetails?.numberOfRooms || \"unknown\" }} Rooms\r\n                </h2>\r\n                <i class=\"fa-solid fa-check-circle text-success fa-lg\"></i>\r\n              </div>\r\n              <p class=\"text-white mb-0\">\r\n                <i class=\"fa-solid fa-location-dot me-2\"></i>\r\n                {{ unitDetails.city?.name_en || \"unknown\" }}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Property Details Section -->\r\n    <div class=\"card\">\r\n      <div class=\"card-header\">\r\n        <h5 class=\"card-title mb-0\">Property details</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <div class=\"property-details-text\">\r\n          <p class=\"mb-3 fs-5\">\r\n            This is a <strong>{{ unitDetails?.type }}</strong> unit located in\r\n            <strong\r\n              >{{ unitDetails?.area?.name_en }},\r\n              {{ unitDetails?.city?.name_en }}</strong\r\n            >. The unit is situated in building number\r\n            <strong>{{ unitDetails?.buildingNumber }}</strong> on the\r\n            <strong>{{ unitDetails?.floor }}</strong> floor.\r\n\r\n            <span *ngIf=\"unitDetails?.unitArea\"\r\n              >It spans an area of\r\n              <strong>{{ unitDetails?.unitArea | number : \"1.0-0\" }} m²</strong\r\n              >.</span\r\n            >\r\n\r\n            The unit features\r\n            <strong>{{ unitDetails?.finishingType }}</strong> finishing and is\r\n            available with\r\n            <strong>{{ unitDetails?.paymentSystem }}</strong> payment options.\r\n\r\n            <span *ngIf=\"unitDetails?.pricePerMeterInCash\"\r\n              >The price per meter in cash is\r\n              <strong\r\n                >{{\r\n                  unitDetails?.pricePerMeterInCash | number : \"1.0-0\"\r\n                }}\r\n                EGP</strong\r\n              >,</span\r\n            >\r\n            <span *ngIf=\"unitDetails?.pricePerMeterInInstallment\">\r\n              while the price per meter in installments is\r\n              <strong\r\n                >{{\r\n                  unitDetails?.pricePerMeterInInstallment | number : \"1.0-0\"\r\n                }}\r\n                EGP</strong\r\n              >.</span\r\n            >\r\n\r\n            <span *ngIf=\"unitDetails?.totalPriceInCash\"\r\n              >The total cash price is\r\n              <strong\r\n                >{{\r\n                  unitDetails?.totalPriceInCash | number : \"1.0-0\"\r\n                }}\r\n                EGP</strong\r\n              >,</span\r\n            >\r\n            <span *ngIf=\"unitDetails?.totalPriceInInstallment\">\r\n              and the total installment price is\r\n              <strong\r\n                >{{\r\n                  unitDetails?.totalPriceInInstallment | number : \"1.0-0\"\r\n                }}\r\n                EGP</strong\r\n              >.</span\r\n            >\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Right Column - Unit Information -->\r\n  <div class=\"col-lg-4\">\r\n    <!-- Price Card -->\r\n    <div class=\"card mb-4\">\r\n      <div class=\"card-body text-center\">\r\n        <h3 class=\"text-success fw-bold mb-2\">\r\n          {{ unitDetails?.totalPriceInCash | number : \"1.0-0\" }} EGP\r\n        </h3>\r\n        <div class=\"d-flex justify-content-center gap-2 mb-3\">\r\n          <span class=\"badge bg-light-primary text-primary\">Compound</span>\r\n          <span class=\"badge bg-light-success text-success\">{{\r\n            unitDetails?.status\r\n          }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Unit Information -->\r\n    <div class=\"card mb-4\">\r\n      <div class=\"card-header\">\r\n        <h5 class=\"card-title mb-0\">Unit Information</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <div class=\"row g-3\">\r\n          <div class=\"col-6\">\r\n            <div class=\"d-flex align-items-center\">\r\n              <i class=\"fa-solid fa-bed text-primary me-2\"></i>\r\n              <div>\r\n                <small class=\"text-muted d-block\">Bedrooms</small>\r\n                <span class=\"fw-semibold\">{{\r\n                  unitDetails?.numberOfRooms\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-6\">\r\n            <div class=\"d-flex align-items-center\">\r\n              <i class=\"fa-solid fa-bath text-primary me-2\"></i>\r\n              <div>\r\n                <small class=\"text-muted d-block\">Bathrooms</small>\r\n                <span class=\"fw-semibold\">{{\r\n                  unitDetails?.numberOfBathrooms\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-6\">\r\n            <div class=\"d-flex align-items-center\">\r\n              <i class=\"fa-solid fa-ruler-combined text-primary me-2\"></i>\r\n              <div>\r\n                <small class=\"text-muted d-block\">Area</small>\r\n                <span class=\"fw-semibold\">{{ unitDetails?.unitArea }} m²</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-6\">\r\n            <div class=\"d-flex align-items-center\">\r\n              <i class=\"fa-solid fa-building text-primary me-2\"></i>\r\n              <div>\r\n                <small class=\"text-muted d-block\">Floor</small>\r\n                <span class=\"fw-semibold\">{{ unitDetails?.floor }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Features -->\r\n    <div class=\"card mb-4\">\r\n      <div class=\"card-header\">\r\n        <h5 class=\"card-title mb-0\">Features</h5>\r\n      </div>\r\n      <div class=\"card-body\">\r\n        <div class=\"row g-2\">\r\n          <div class=\"col-12\" *ngFor=\"let feature of features\">\r\n            <div class=\"form-check\">\r\n              <input\r\n                class=\"form-check-input\"\r\n                type=\"checkbox\"\r\n                [checked]=\"isFeatureEnabled(feature.value)\"\r\n                disabled\r\n              />\r\n              <label class=\"form-check-label text-dark\">{{\r\n                feature.name\r\n              }}</label>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;;;;ICoC1BC,EAAA,CAAAC,uBAAA,GAA4C;IAC1CD,EAAA,CAAAE,SAAA,cAKE;;;;;;;IAJAF,EAAA,CAAAG,SAAA,EAAqD;IACrDH,EADA,CAAAI,UAAA,QAAAC,OAAA,CAAAC,GAAA,uCAAAN,EAAA,CAAAO,aAAA,CAAqD,yBAAAC,IAAA,MACtB;;;;;IAKnCR,EAAA,CAAAC,uBAAA,GAA4C;IAC1CD,EAAA,CAAAE,SAAA,gBAKS;;;;;IAJPF,EAAA,CAAAG,SAAA,EAAgB;IAAhBH,EAAA,CAAAI,UAAA,QAAAC,OAAA,CAAAC,GAAA,EAAAN,EAAA,CAAAO,aAAA,CAAgB;;;;;IAftBP,EAAA,CAAAS,cAAA,cAIC;IASCT,EARA,CAAAU,UAAA,IAAAC,uDAAA,2BAA4C,IAAAC,uDAAA,2BAQA;IAQ9CZ,EAAA,CAAAa,YAAA,EAAM;;;;;IAlBJb,EAAA,CAAAc,WAAA,WAAAN,IAAA,OAAwB;IAETR,EAAA,CAAAG,SAAA,EAA2B;IAA3BH,EAAA,CAAAI,UAAA,SAAAC,OAAA,CAAAU,IAAA,aAA2B;IAQ3Bf,EAAA,CAAAG,SAAA,EAA2B;IAA3BH,EAAA,CAAAI,UAAA,SAAAC,OAAA,CAAAU,IAAA,aAA2B;;;;;IAuB5Cf,EAAA,CAAAE,SAAA,iBAMU;;;;IADRF,EAAA,CAAAc,WAAA,WAAAE,IAAA,OAAwB;;;;;;IAoDhChB,EAAA,CAAAS,cAAA,WACG;IAAAT,EAAA,CAAAiB,MAAA,2BACD;IAAAjB,EAAA,CAAAS,cAAA,aAAQ;IAAAT,EAAA,CAAAiB,MAAA,GAAiD;;IAAAjB,EAAA,CAAAa,YAAA,EACxD;IAAAb,EAAA,CAAAiB,MAAA,QAAC;IAAAjB,EAAA,CAAAa,YAAA,EACH;;;;IAFSb,EAAA,CAAAG,SAAA,GAAiD;IAAjDH,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAmB,WAAA,OAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,QAAA,uBAAiD;;;;;IAS3DtB,EAAA,CAAAS,cAAA,WACG;IAAAT,EAAA,CAAAiB,MAAA,sCACD;IAAAjB,EAAA,CAAAS,cAAA,aACG;IAAAT,EAAA,CAAAiB,MAAA,GAGE;;IAAAjB,EAAA,CAAAa,YAAA,EACJ;IAAAb,EAAA,CAAAiB,MAAA,QAAC;IAAAjB,EAAA,CAAAa,YAAA,EACH;;;;IALIb,EAAA,CAAAG,SAAA,GAGE;IAHFH,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAmB,WAAA,OAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAE,mBAAA,mBAGE;;;;;IAGPvB,EAAA,CAAAS,cAAA,WAAsD;IACpDT,EAAA,CAAAiB,MAAA,qDACA;IAAAjB,EAAA,CAAAS,cAAA,aACG;IAAAT,EAAA,CAAAiB,MAAA,GAGE;;IAAAjB,EAAA,CAAAa,YAAA,EACJ;IAAAb,EAAA,CAAAiB,MAAA,QAAC;IAAAjB,EAAA,CAAAa,YAAA,EACH;;;;IALIb,EAAA,CAAAG,SAAA,GAGE;IAHFH,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAmB,WAAA,OAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAG,0BAAA,mBAGE;;;;;IAIPxB,EAAA,CAAAS,cAAA,WACG;IAAAT,EAAA,CAAAiB,MAAA,+BACD;IAAAjB,EAAA,CAAAS,cAAA,aACG;IAAAT,EAAA,CAAAiB,MAAA,GAGE;;IAAAjB,EAAA,CAAAa,YAAA,EACJ;IAAAb,EAAA,CAAAiB,MAAA,QAAC;IAAAjB,EAAA,CAAAa,YAAA,EACH;;;;IALIb,EAAA,CAAAG,SAAA,GAGE;IAHFH,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAmB,WAAA,OAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAI,gBAAA,mBAGE;;;;;IAGPzB,EAAA,CAAAS,cAAA,WAAmD;IACjDT,EAAA,CAAAiB,MAAA,2CACA;IAAAjB,EAAA,CAAAS,cAAA,aACG;IAAAT,EAAA,CAAAiB,MAAA,GAGE;;IAAAjB,EAAA,CAAAa,YAAA,EACJ;IAAAb,EAAA,CAAAiB,MAAA,QAAC;IAAAjB,EAAA,CAAAa,YAAA,EACH;;;;IALIb,EAAA,CAAAG,SAAA,GAGE;IAHFH,EAAA,CAAAkB,kBAAA,KAAAlB,EAAA,CAAAmB,WAAA,OAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAK,uBAAA,mBAGE;;;;;IAqFP1B,EADF,CAAAS,cAAA,cAAqD,cAC3B;IACtBT,EAAA,CAAAE,SAAA,gBAKE;IACFF,EAAA,CAAAS,cAAA,gBAA0C;IAAAT,EAAA,CAAAiB,MAAA,GAExC;IAENjB,EAFM,CAAAa,YAAA,EAAQ,EACN,EACF;;;;;IAPAb,EAAA,CAAAG,SAAA,GAA2C;IAA3CH,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAO,gBAAA,CAAAC,UAAA,CAAAC,KAAA,EAA2C;IAGH7B,EAAA,CAAAG,SAAA,GAExC;IAFwCH,EAAA,CAAA8B,iBAAA,CAAAF,UAAA,CAAAG,IAAA,CAExC;;;AD1PhB,OAAM,MAAOC,wBAAwB;EAazBC,KAAA;EACAC,MAAA;EACAC,eAAA;EACAC,YAAA;EACAC,SAAA;EACAC,EAAA;EAjBVC,QAAQ,GAAQ,IAAI;EACpBC,SAAS,GAAG,IAAI;EAChBC,UAAU;EAEV;EACAC,gBAAgB,GAAkB,IAAI;EACtCC,iBAAiB,GAAW,OAAO;EACnCC,YAAY,GAA2B,IAAI;EAC3CC,eAAe,GAAU,EAAE;EAC3BC,iBAAiB,GAAW,CAAC;EAE7BC,YACUd,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,YAAsB,EACtBC,SAAuB,EACvBC,EAAqB;IALrB,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,EAAE,GAAFA,EAAE;EACT;EAEHU,QAAQA,CAAA;IACN,IAAI,CAACf,KAAK,CAACgB,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MACrC,IAAI,CAACR,UAAU,GAAG,CAACQ,MAAM,CAAC,IAAI,CAAC;MAC/B,IAAI,IAAI,CAACR,UAAU,EAAE;QACnB,IAAI,CAACU,mBAAmB,EAAE;MAC5B;IACF,CAAC,CAAC;EACJ;EAEAA,mBAAmBA,CAAA;IACjB,IAAI,CAACX,SAAS,GAAG,IAAI;IACrB,IAAI,CAACL,eAAe,CAACiB,OAAO,CAAC,IAAI,CAACX,UAAU,CAAC,CAACS,SAAS,CAAC;MACtDG,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACf,QAAQ,GAAGe,QAAQ,CAACC,IAAI,IAAID,QAAQ;QACzC,IAAI,CAACd,SAAS,GAAG,KAAK;QACtB,IAAI,CAACF,EAAE,CAACkB,aAAa,EAAE;MACzB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACjB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACF,EAAE,CAACkB,aAAa,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;EACAG,MAAMA,CAAA;IACJ,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;EAEA;EACAC,uBAAuBA,CAACtB,QAAa;IACnC,IAAIA,QAAQ,CAACuB,OAAO,IAAIvB,QAAQ,CAACuB,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACnD,MAAMC,UAAU,GAAGzB,QAAQ,CAACuB,OAAO,CAACG,MAAM,CAAEC,IAAS,IAAKA,IAAI,CAAC5D,GAAG,CAAC;MACnE,IAAI0D,UAAU,CAACD,MAAM,GAAG,CAAC,EAAE;QACzB,OAAOC,UAAU;MACnB;IACF;IACA,OAAO,CAAC;MAAE1D,GAAG,EAAE,mCAAmC;MAAES,IAAI,EAAE;IAAO,CAAE,CAAC;EACtE;EAEA;EACAoD,cAAcA,CAACC,OAAY;IACzB,IAAI,CAAC,IAAI,CAAC7B,QAAQ,EAAE;IAEpB,IAAI,CAACM,eAAe,GAAG,IAAI,CAACgB,uBAAuB,CAAC,IAAI,CAACtB,QAAQ,CAAC;IAClE,IAAI,CAACO,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACuB,gBAAgB,EAAE;IAEvB,IAAI,CAACjC,YAAY,CAACkC,IAAI,CAACF,OAAO,EAAE;MAC9BG,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,IAAI;MACVC,WAAW,EAAE,aAAa;MAC1BC,QAAQ,EAAE;KACX,CAAC;EACJ;EAEA;EACAL,gBAAgBA,CAAA;IACd,IACE,IAAI,CAACxB,eAAe,CAACkB,MAAM,GAAG,CAAC,IAC/B,IAAI,CAACjB,iBAAiB,IAAI,CAAC,IAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAACkB,MAAM,EACpD;MACA,MAAMY,WAAW,GAAG,IAAI,CAAC9B,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAChE,IAAI,CAACJ,gBAAgB,GAAGiC,WAAW,CAACrE,GAAG;MACvC,IAAI,CAACqC,iBAAiB,GAAGgC,WAAW,CAAC5D,IAAI;MAEzC,IAAI,IAAI,CAAC4B,iBAAiB,KAAK,OAAO,IAAI,IAAI,CAACD,gBAAgB,EAAE;QAC/D,IAAI,CAACE,YAAY,GAAG,IAAI,CAACP,SAAS,CAACuC,8BAA8B,CAC/D,IAAI,CAAClC,gBAAgB,CACtB;MACH,CAAC,MAAM;QACL,IAAI,CAACE,YAAY,GAAG,IAAI;MAC1B;IACF;EACF;EAEA;EACAiC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAChC,eAAe,CAACkB,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAACjB,iBAAiB,GACpB,CAAC,IAAI,CAACA,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAACD,eAAe,CAACkB,MAAM;MAC5D,IAAI,CAACM,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAS,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACjC,eAAe,CAACkB,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAACjB,iBAAiB,GACpB,IAAI,CAACA,iBAAiB,GAAG,CAAC,GACtB,IAAI,CAACA,iBAAiB,GAAG,CAAC,GAC1B,IAAI,CAACD,eAAe,CAACkB,MAAM,GAAG,CAAC;MACrC,IAAI,CAACM,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAU,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAClC,eAAe,CAACkB,MAAM,GAAG,CAAC;EACxC;EAEA;EACAiB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACzC,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACuB,OAAO,IAAI,IAAI,CAACvB,QAAQ,CAACuB,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MAC9E,MAAMkB,UAAU,GAAG,IAAI,CAAC1C,QAAQ,CAACuB,OAAO,CAACoB,IAAI,CAAEhB,IAAS,IAAKA,IAAI,CAAC5D,GAAG,IAAI4D,IAAI,CAACnD,IAAI,KAAK,OAAO,CAAC;MAC/F,IAAIkE,UAAU,EAAE;QACd,OAAOA,UAAU,CAAC3E,GAAG;MACvB;IACF;IACA,OAAO,mCAAmC;EAC5C;EAEA;EACA6E,UAAUA,CAAA;IACR,OAAO,IAAI,CAAC5C,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACuB,OAAO,IAAI,IAAI,CAACvB,QAAQ,CAACuB,OAAO,CAACC,MAAM,GAAG,CAAC;EACnF;EAEA;EACAqB,WAAWA,CAACC,KAAU;IACpB,IAAI,CAACA,KAAK,EAAE,OAAO,qBAAqB;IACxC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,GAAG,MAAM;EAC9D;EAEA;EACAI,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAClD,QAAQ,EAAExB,IAAI,EAAE,OAAO,KAAK;IACtC,OAAO,IAAI,CAACwB,QAAQ,CAACxB,IAAI,CAAC2E,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAGC,CAAS,IAAKA,CAAC,CAACC,WAAW,EAAE,CAAC;EAC/F;;qCAtJW5D,wBAAwB,EAAAhC,EAAA,CAAA6F,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/F,EAAA,CAAA6F,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAhG,EAAA,CAAA6F,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAlG,EAAA,CAAA6F,iBAAA,CAAAM,EAAA,CAAAC,QAAA,GAAApG,EAAA,CAAA6F,iBAAA,CAAAQ,EAAA,CAAAC,YAAA,GAAAtG,EAAA,CAAA6F,iBAAA,CAAA7F,EAAA,CAAAuG,iBAAA;EAAA;;UAAxBvE,wBAAwB;IAAAwE,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA1G,EAAA,CAAA2G,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbrCjH,EAAA,CAAAE,SAAA,aAEM;QAqBUF,EAlBhB,CAAAS,cAAA,aAAiB,aAEO,aAEG,aACM,aAC2B,aAEf,aAEd,aAMhB,aAEmC;QAQhCT,EAAA,CAAAU,UAAA,KAAAyG,wCAAA,kBAIC;QA4BHnH,EAAA,CAAAa,YAAA,EAAM;QAGNb,EAAA,CAAAS,cAAA,eAAiC;QAC/BT,EAAA,CAAAU,UAAA,KAAA0G,2CAAA,qBAMC;QAITpH,EAHM,CAAAa,YAAA,EAAM,EACF,EACF,EACF;QAIJb,EADF,CAAAS,cAAA,eAAiD,gBACoB;QACjET,EAAA,CAAAiB,MAAA,IACF;QACFjB,EADE,CAAAa,YAAA,EAAO,EACH;QAQAb,EALN,CAAAS,cAAA,eAEC,eACkB,eAC6B,cACD;QACvCT,EAAA,CAAAiB,MAAA,IAEF;QAAAjB,EAAA,CAAAa,YAAA,EAAK;QACLb,EAAA,CAAAE,SAAA,aAA2D;QAC7DF,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAS,cAAA,aAA2B;QACzBT,EAAA,CAAAE,SAAA,aAA6C;QAC7CF,EAAA,CAAAiB,MAAA,IACF;QAKVjB,EALU,CAAAa,YAAA,EAAI,EACA,EACF,EACF,EACF,EACF;QAKFb,EAFJ,CAAAS,cAAA,eAAkB,eACS,cACK;QAAAT,EAAA,CAAAiB,MAAA,wBAAgB;QAC9CjB,EAD8C,CAAAa,YAAA,EAAK,EAC7C;QAGFb,EAFJ,CAAAS,cAAA,eAAuB,eACc,aACZ;QACnBT,EAAA,CAAAiB,MAAA,mBAAU;QAAAjB,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAiB,MAAA,IAAuB;QAAAjB,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAiB,MAAA,yBACnD;QAAAjB,EAAA,CAAAS,cAAA,cACG;QAAAT,EAAA,CAAAiB,MAAA,IAC+B;QAAAjB,EAAA,CAAAa,YAAA,EACjC;QAAAb,EAAA,CAAAiB,MAAA,kDACD;QAAAjB,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAiB,MAAA,IAAiC;QAAAjB,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAiB,MAAA,gBACnD;QAAAjB,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAiB,MAAA,IAAwB;QAAAjB,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAiB,MAAA,gBAE1C;QAAAjB,EAAA,CAAAU,UAAA,KAAA2G,yCAAA,mBACG;QAKHrH,EAAA,CAAAiB,MAAA,2BACA;QAAAjB,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAiB,MAAA,IAAgC;QAAAjB,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAiB,MAAA,yCAElD;QAAAjB,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAiB,MAAA,IAAgC;QAAAjB,EAAA,CAAAa,YAAA,EAAS;QAACb,EAAA,CAAAiB,MAAA,0BAElD;QA4BAjB,EA5BA,CAAAU,UAAA,KAAA4G,yCAAA,mBACG,KAAAC,yCAAA,mBAQmD,KAAAC,yCAAA,mBAWnD,KAAAC,yCAAA,mBAQgD;QAa7DzH,EAJQ,CAAAa,YAAA,EAAI,EACA,EACF,EACF,EACF;QAOAb,EAJN,CAAAS,cAAA,eAAsB,eAEG,eACc,cACK;QACpCT,EAAA,CAAAiB,MAAA,IACF;;QAAAjB,EAAA,CAAAa,YAAA,EAAK;QAEHb,EADF,CAAAS,cAAA,eAAsD,gBACF;QAAAT,EAAA,CAAAiB,MAAA,gBAAQ;QAAAjB,EAAA,CAAAa,YAAA,EAAO;QACjEb,EAAA,CAAAS,cAAA,gBAAkD;QAAAT,EAAA,CAAAiB,MAAA,IAEhD;QAGRjB,EAHQ,CAAAa,YAAA,EAAO,EACL,EACF,EACF;QAKFb,EAFJ,CAAAS,cAAA,eAAuB,eACI,cACK;QAAAT,EAAA,CAAAiB,MAAA,wBAAgB;QAC9CjB,EAD8C,CAAAa,YAAA,EAAK,EAC7C;QAIAb,EAHN,CAAAS,cAAA,eAAuB,eACA,eACA,eACsB;QACrCT,EAAA,CAAAE,SAAA,aAAiD;QAE/CF,EADF,CAAAS,cAAA,WAAK,iBAC+B;QAAAT,EAAA,CAAAiB,MAAA,gBAAQ;QAAAjB,EAAA,CAAAa,YAAA,EAAQ;QAClDb,EAAA,CAAAS,cAAA,gBAA0B;QAAAT,EAAA,CAAAiB,MAAA,IAExB;QAGRjB,EAHQ,CAAAa,YAAA,EAAO,EACL,EACF,EACF;QAEJb,EADF,CAAAS,cAAA,eAAmB,eACsB;QACrCT,EAAA,CAAAE,SAAA,aAAkD;QAEhDF,EADF,CAAAS,cAAA,WAAK,iBAC+B;QAAAT,EAAA,CAAAiB,MAAA,iBAAS;QAAAjB,EAAA,CAAAa,YAAA,EAAQ;QACnDb,EAAA,CAAAS,cAAA,gBAA0B;QAAAT,EAAA,CAAAiB,MAAA,IAExB;QAGRjB,EAHQ,CAAAa,YAAA,EAAO,EACL,EACF,EACF;QAEJb,EADF,CAAAS,cAAA,eAAmB,eACsB;QACrCT,EAAA,CAAAE,SAAA,aAA4D;QAE1DF,EADF,CAAAS,cAAA,WAAK,iBAC+B;QAAAT,EAAA,CAAAiB,MAAA,YAAI;QAAAjB,EAAA,CAAAa,YAAA,EAAQ;QAC9Cb,EAAA,CAAAS,cAAA,gBAA0B;QAAAT,EAAA,CAAAiB,MAAA,IAA8B;QAG9DjB,EAH8D,CAAAa,YAAA,EAAO,EAC3D,EACF,EACF;QAEJb,EADF,CAAAS,cAAA,eAAmB,eACsB;QACrCT,EAAA,CAAAE,SAAA,cAAsD;QAEpDF,EADF,CAAAS,cAAA,YAAK,kBAC+B;QAAAT,EAAA,CAAAiB,MAAA,cAAK;QAAAjB,EAAA,CAAAa,YAAA,EAAQ;QAC/Cb,EAAA,CAAAS,cAAA,iBAA0B;QAAAT,EAAA,CAAAiB,MAAA,KAAwB;QAM9DjB,EAN8D,CAAAa,YAAA,EAAO,EACrD,EACF,EACF,EACF,EACF,EACF;QAKFb,EAFJ,CAAAS,cAAA,gBAAuB,gBACI,eACK;QAAAT,EAAA,CAAAiB,MAAA,iBAAQ;QACtCjB,EADsC,CAAAa,YAAA,EAAK,EACrC;QAEJb,EADF,CAAAS,cAAA,gBAAuB,gBACA;QACnBT,EAAA,CAAAU,UAAA,MAAAgH,yCAAA,kBAAqD;QAiB/D1H,EAJQ,CAAAa,YAAA,EAAM,EACF,EACF,EACF,EACF;;;QA7O+Bb,EAAA,CAAAG,SAAA,IAAc;QAAdH,EAAA,CAAAI,UAAA,YAAA8G,GAAA,CAAAS,SAAA,CAAc;QAqCb3H,EAAA,CAAAG,SAAA,GAAc;QAAdH,EAAA,CAAAI,UAAA,YAAA8G,GAAA,CAAAS,SAAA,CAAc;QAYtC3H,EAAA,CAAAG,SAAA,GACF;QADEH,EAAA,CAAAkB,kBAAA,OAAAgG,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAN,IAAA,oBACF;QAUMf,EAAA,CAAAG,SAAA,GAEF;QAFEH,EAAA,CAAA4H,kBAAA,OAAAV,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAwG,SAAA,kCAAAX,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAyG,aAAA,0BAEF;QAKA9H,EAAA,CAAAG,SAAA,GACF;QADEH,EAAA,CAAAkB,kBAAA,OAAAgG,GAAA,CAAA7F,WAAA,CAAA0G,IAAA,kBAAAb,GAAA,CAAA7F,WAAA,CAAA0G,IAAA,CAAAC,OAAA,oBACF;QAegBhI,EAAA,CAAAG,SAAA,IAAuB;QAAvBH,EAAA,CAAA8B,iBAAA,CAAAoF,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAN,IAAA,CAAuB;QAEtCf,EAAA,CAAAG,SAAA,GAC+B;QAD/BH,EAAA,CAAA4H,kBAAA,KAAAV,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAA4G,IAAA,kBAAAf,GAAA,CAAA7F,WAAA,CAAA4G,IAAA,CAAAD,OAAA,QAAAd,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAA0G,IAAA,kBAAAb,GAAA,CAAA7F,WAAA,CAAA0G,IAAA,CAAAC,OAAA,KAC+B;QAE1BhI,EAAA,CAAAG,SAAA,GAAiC;QAAjCH,EAAA,CAAA8B,iBAAA,CAAAoF,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAA6G,cAAA,CAAiC;QACjClI,EAAA,CAAAG,SAAA,GAAwB;QAAxBH,EAAA,CAAA8B,iBAAA,CAAAoF,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAA8G,KAAA,CAAwB;QAEzBnI,EAAA,CAAAG,SAAA,GAA2B;QAA3BH,EAAA,CAAAI,UAAA,SAAA8G,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAC,QAAA,CAA2B;QAO1BtB,EAAA,CAAAG,SAAA,GAAgC;QAAhCH,EAAA,CAAA8B,iBAAA,CAAAoF,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAA+G,aAAA,CAAgC;QAEhCpI,EAAA,CAAAG,SAAA,GAAgC;QAAhCH,EAAA,CAAA8B,iBAAA,CAAAoF,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAgH,aAAA,CAAgC;QAEjCrI,EAAA,CAAAG,SAAA,GAAsC;QAAtCH,EAAA,CAAAI,UAAA,SAAA8G,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAE,mBAAA,CAAsC;QAStCvB,EAAA,CAAAG,SAAA,EAA6C;QAA7CH,EAAA,CAAAI,UAAA,SAAA8G,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAG,0BAAA,CAA6C;QAU7CxB,EAAA,CAAAG,SAAA,EAAmC;QAAnCH,EAAA,CAAAI,UAAA,SAAA8G,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAI,gBAAA,CAAmC;QASnCzB,EAAA,CAAAG,SAAA,EAA0C;QAA1CH,EAAA,CAAAI,UAAA,SAAA8G,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAK,uBAAA,CAA0C;QAqBnD1B,EAAA,CAAAG,SAAA,GACF;QADEH,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAmB,WAAA,SAAA+F,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAI,gBAAA,oBACF;QAGoDzB,EAAA,CAAAG,SAAA,GAEhD;QAFgDH,EAAA,CAAA8B,iBAAA,CAAAoF,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAiH,MAAA,CAEhD;QAiB8BtI,EAAA,CAAAG,SAAA,IAExB;QAFwBH,EAAA,CAAA8B,iBAAA,CAAAoF,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAyG,aAAA,CAExB;QASwB9H,EAAA,CAAAG,SAAA,GAExB;QAFwBH,EAAA,CAAA8B,iBAAA,CAAAoF,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAkH,iBAAA,CAExB;QASwBvI,EAAA,CAAAG,SAAA,GAA8B;QAA9BH,EAAA,CAAAkB,kBAAA,KAAAgG,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAAC,QAAA,aAA8B;QAS9BtB,EAAA,CAAAG,SAAA,GAAwB;QAAxBH,EAAA,CAAA8B,iBAAA,CAAAoF,GAAA,CAAA7F,WAAA,kBAAA6F,GAAA,CAAA7F,WAAA,CAAA8G,KAAA,CAAwB;QAehBnI,EAAA,CAAAG,SAAA,GAAW;QAAXH,EAAA,CAAAI,UAAA,YAAA8G,GAAA,CAAAR,QAAA,CAAW;;;mBDpPjD3G,YAAY,EAAAyI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}