{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/taskes/New folder/easydeal-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/property.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../shared/broker-title/broker-title.component\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction AddPropertyComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h2\")(2, \"span\", 19);\n    i0.ɵɵtext(3, \"Add Property - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 20);\n    i0.ɵɵtext(5, \"Property Category \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h2\")(2, \"span\", 19);\n    i0.ɵɵtext(3, \"Add Unit - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 20);\n    i0.ɵɵtext(5, \"Location Information \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h2\")(2, \"span\", 19);\n    i0.ɵɵtext(3, \"Add Unit - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 20);\n    i0.ɵɵtext(5, \"Unit Information \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h2\")(2, \"span\", 19);\n    i0.ɵɵtext(3, \"Add Unit - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 20);\n    i0.ɵɵtext(5, \"Payment Details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h2\")(2, \"span\", 19);\n    i0.ɵɵtext(3, \"Add Property - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 20);\n    i0.ɵɵtext(5, \"Media & Documents\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h2\")(2, \"span\", 19);\n    i0.ɵɵtext(3, \"Add Property - \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 20);\n    i0.ɵɵtext(5, \"Review & Submit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_19_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prevStep());\n    });\n    i0.ɵɵtext(1, \" Back to previous step \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_23_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_23_li_10_Template_a_click_1_listener() {\n      const option_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectStep0Value(\"compoundType\", option_r4.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r4.key);\n  }\n}\nfunction AddPropertyComponent_div_23_li_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_23_li_20_Template_a_click_1_listener() {\n      const option_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectStep0Value(\"propertyType\", option_r6.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r6.key);\n  }\n}\nfunction AddPropertyComponent_div_23_li_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_23_li_30_Template_a_click_1_listener() {\n      const unitType_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectStep0Value(\"type\", unitType_r8.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const unitType_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(unitType_r8.key);\n  }\n}\nfunction AddPropertyComponent_div_23_small_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 11);\n    i0.ɵɵtext(1, \" Please select compound type first \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"label\", 24);\n    i0.ɵɵtext(3, \"Compound Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 25)(5, \"button\", 26)(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"ul\", 28);\n    i0.ɵɵtemplate(10, AddPropertyComponent_div_23_li_10_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 23)(12, \"label\", 24);\n    i0.ɵɵtext(13, \"Property Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 25)(15, \"button\", 30)(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"ul\", 31);\n    i0.ɵɵtemplate(20, AddPropertyComponent_div_23_li_20_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 23)(22, \"label\", 24);\n    i0.ɵɵtext(23, \"Unit Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 25)(25, \"button\", 32)(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"ul\", 33);\n    i0.ɵɵtemplate(30, AddPropertyComponent_div_23_li_30_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, AddPropertyComponent_div_23_small_31_Template, 2, 0, \"small\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_4_0;\n    let tmp_7_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.step0Form);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getCompoundTypeText((tmp_2_0 = ctx_r1.step0Form.get(\"compoundType\")) == null ? null : tmp_2_0.value) || \" Select Compound Type\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.compoundOptions);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_4_0 = ctx_r1.step0Form.get(\"propertyType\")) == null ? null : tmp_4_0.value) || \"Select property type\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filterPropertyTypes());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.filteredUnitTypes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_7_0 = ctx_r1.step0Form.get(\"type\")) == null ? null : tmp_7_0.value) || \"Select Unit Type\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredUnitTypes);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filteredUnitTypes.length === 0);\n  }\n}\nfunction AddPropertyComponent_div_24_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Phone number is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_24_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter a valid Egyptian phone number (e.g., 01XXXXXXXXX). \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_24_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, AddPropertyComponent_div_24_div_10_div_1_Template, 2, 0, \"div\", 7)(2, AddPropertyComponent_div_24_div_10_div_2_Template, 2, 0, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.step1Form.get(\"ownerPhone\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.step1Form.get(\"ownerPhone\")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors[\"pattern\"]);\n  }\n}\nfunction AddPropertyComponent_div_24_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Loading cities... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_24_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtext(1, \" No cities available \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_24_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Loading... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_div_24_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedCityName || \"Select City\", \" \");\n  }\n}\nfunction AddPropertyComponent_div_24_ng_container_26_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 62)(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_24_ng_container_26_li_1_Template_a_click_1_listener() {\n      const city_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectCity(city_r10.id, city_r10.name_en));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const city_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", city_r10.name_en, \" \");\n  }\n}\nfunction AddPropertyComponent_div_24_ng_container_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AddPropertyComponent_div_24_ng_container_26_li_1_Template, 3, 1, \"li\", 61);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.cities);\n  }\n}\nfunction AddPropertyComponent_div_24_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 63);\n    i0.ɵɵtext(2, \" No cities available \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AddPropertyComponent_div_24_li_38_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_24_li_38_a_1_Template_a_click_0_listener() {\n      const area_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectArea(area_r12.id, area_r12.name_en));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(area_r12.name_en);\n  }\n}\nfunction AddPropertyComponent_div_24_li_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, AddPropertyComponent_div_24_li_38_a_1_Template, 2, 1, \"a\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.areas);\n  }\n}\nfunction AddPropertyComponent_div_24_a_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 65);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_24_a_51_Template_a_click_0_listener() {\n      const subArea_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectSubArea(subArea_r14.id, subArea_r14.name_en));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const subArea_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", subArea_r14.name_en, \" \");\n  }\n}\nfunction AddPropertyComponent_div_24_div_57_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Mall name cannot exceed 255 characters. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_24_div_57_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, AddPropertyComponent_div_24_div_57_div_4_div_1_Template, 2, 0, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.step1Form.get(\"mallName\")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors[\"maxlength\"]);\n  }\n}\nfunction AddPropertyComponent_div_24_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \" Mall Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 66);\n    i0.ɵɵtemplate(4, AddPropertyComponent_div_24_div_57_div_4_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_3_0 = ctx_r1.step1Form.get(\"mallName\")) == null ? null : tmp_3_0.invalid) && (((tmp_3_0 = ctx_r1.step1Form.get(\"mallName\")) == null ? null : tmp_3_0.touched) || ((tmp_3_0 = ctx_r1.step1Form.get(\"mallName\")) == null ? null : tmp_3_0.dirty))));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r1.step1Form.get(\"mallName\")) == null ? null : tmp_4_0.invalid) && (((tmp_4_0 = ctx_r1.step1Form.get(\"mallName\")) == null ? null : tmp_4_0.touched) || ((tmp_4_0 = ctx_r1.step1Form.get(\"mallName\")) == null ? null : tmp_4_0.dirty)));\n  }\n}\nfunction AddPropertyComponent_div_24_div_58_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Compound name cannot exceed 255 characters. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_24_div_58_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, AddPropertyComponent_div_24_div_58_div_4_div_1_Template, 2, 0, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.step1Form.get(\"compoundName\")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors[\"maxlength\"]);\n  }\n}\nfunction AddPropertyComponent_div_24_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \" Compound Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 67);\n    i0.ɵɵtemplate(4, AddPropertyComponent_div_24_div_58_div_4_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_3_0 = ctx_r1.step1Form.get(\"compoundName\")) == null ? null : tmp_3_0.invalid) && (((tmp_3_0 = ctx_r1.step1Form.get(\"compoundName\")) == null ? null : tmp_3_0.touched) || ((tmp_3_0 = ctx_r1.step1Form.get(\"compoundName\")) == null ? null : tmp_3_0.dirty))));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r1.step1Form.get(\"compoundName\")) == null ? null : tmp_4_0.invalid) && (((tmp_4_0 = ctx_r1.step1Form.get(\"compoundName\")) == null ? null : tmp_4_0.touched) || ((tmp_4_0 = ctx_r1.step1Form.get(\"compoundName\")) == null ? null : tmp_4_0.dirty)));\n  }\n}\nfunction AddPropertyComponent_div_24_div_63_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Google Maps link is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_24_div_63_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter a valid URL (e.g., https://maps.google.com/...). \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_24_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, AddPropertyComponent_div_24_div_63_div_1_Template, 2, 0, \"div\", 7)(2, AddPropertyComponent_div_24_div_63_div_2_Template, 2, 0, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.step1Form.get(\"location\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.step1Form.get(\"location\")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors[\"pattern\"]);\n  }\n}\nfunction AddPropertyComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 36)(2, \"div\", 37)(3, \"label\", 24);\n    i0.ɵɵtext(4, \" Owner Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 37)(7, \"label\", 24);\n    i0.ɵɵtext(8, \" Owner Phone \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"input\", 39);\n    i0.ɵɵtemplate(10, AddPropertyComponent_div_24_div_10_Template, 3, 2, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 36)(12, \"div\", 37)(13, \"label\", 24);\n    i0.ɵɵtext(14, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, AddPropertyComponent_div_24_div_15_Template, 2, 0, \"div\", 41)(16, AddPropertyComponent_div_24_div_16_Template, 2, 0, \"div\", 42);\n    i0.ɵɵelementStart(17, \"div\", 25)(18, \"button\", 43)(19, \"span\");\n    i0.ɵɵtemplate(20, AddPropertyComponent_div_24_ng_container_20_Template, 2, 0, \"ng-container\", 7)(21, AddPropertyComponent_div_24_ng_container_21_Template, 2, 1, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"ul\", 44)(24, \"li\", 45);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, AddPropertyComponent_div_24_ng_container_26_Template, 2, 1, \"ng-container\", 46)(27, AddPropertyComponent_div_24_ng_template_27_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 37)(30, \"label\", 24);\n    i0.ɵɵtext(31, \"Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 25)(33, \"button\", 47)(34, \"span\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"ul\", 48);\n    i0.ɵɵtemplate(38, AddPropertyComponent_div_24_li_38_Template, 2, 1, \"li\", 7);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(39, \"div\", 49)(40, \"div\", 37)(41, \"div\", 23)(42, \"label\", 24);\n    i0.ɵɵtext(43, \" Sub Area \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 25)(45, \"button\", 50)(46, \"span\", 51);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(48, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"ul\", 52)(50, \"li\", 53);\n    i0.ɵɵtemplate(51, AddPropertyComponent_div_24_a_51_Template, 2, 1, \"a\", 54);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(52, \"div\", 37)(53, \"div\", 23)(54, \"label\", 24);\n    i0.ɵɵtext(55, \" Detailed Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"input\", 55);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(57, AddPropertyComponent_div_24_div_57_Template, 5, 4, \"div\", 56)(58, AddPropertyComponent_div_24_div_58_Template, 5, 4, \"div\", 56);\n    i0.ɵɵelementStart(59, \"div\", 23)(60, \"label\", 24);\n    i0.ɵɵtext(61, \" Google Maps Link \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(62, \"input\", 57);\n    i0.ɵɵtemplate(63, AddPropertyComponent_div_24_div_63_Template, 3, 2, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_19_0;\n    let tmp_20_0;\n    const noCities_r15 = i0.ɵɵreference(28);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.step1Form);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ((tmp_3_0 = ctx_r1.step1Form.get(\"ownerPhone\")) == null ? null : tmp_3_0.invalid) && (((tmp_3_0 = ctx_r1.step1Form.get(\"ownerPhone\")) == null ? null : tmp_3_0.touched) || ((tmp_3_0 = ctx_r1.step1Form.get(\"ownerPhone\")) == null ? null : tmp_3_0.dirty))));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r1.step1Form.get(\"ownerPhone\")) == null ? null : tmp_4_0.invalid) && (((tmp_4_0 = ctx_r1.step1Form.get(\"ownerPhone\")) == null ? null : tmp_4_0.touched) || ((tmp_4_0 = ctx_r1.step1Form.get(\"ownerPhone\")) == null ? null : tmp_4_0.dirty)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCities);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingCities && ctx_r1.cities.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoadingCities);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCities);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingCities);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Total - Cities: \", ctx_r1.cities.length, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cities && ctx_r1.cities.length > 0)(\"ngIfElse\", noCities_r15);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedAreaName || \"Select Area\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.areas.length > 0);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedSubAreaName || \"Select sub area\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.subAreas);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"mallName\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"compoundName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c0, ((tmp_19_0 = ctx_r1.step1Form.get(\"location\")) == null ? null : tmp_19_0.invalid) && (((tmp_19_0 = ctx_r1.step1Form.get(\"location\")) == null ? null : tmp_19_0.touched) || ((tmp_19_0 = ctx_r1.step1Form.get(\"location\")) == null ? null : tmp_19_0.dirty))));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = ctx_r1.step1Form.get(\"location\")) == null ? null : tmp_20_0.invalid) && (((tmp_20_0 = ctx_r1.step1Form.get(\"location\")) == null ? null : tmp_20_0.touched) || ((tmp_20_0 = ctx_r1.step1Form.get(\"location\")) == null ? null : tmp_20_0.dirty)));\n  }\n}\nfunction AddPropertyComponent_div_25_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Building Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 71);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_25_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Unit Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 72);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_25_div_5_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_5_li_9_Template_a_click_1_listener() {\n      const floor_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"floor\", floor_r17.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const floor_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(floor_r17.key);\n  }\n}\nfunction AddPropertyComponent_div_25_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Floor\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 73)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 74);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_5_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"floor\")) == null ? null : tmp_2_0.value) || \"Select floor\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.floorTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Unit Area (sqm)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 75);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_25_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Building Area (sqm)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 76);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_25_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Ground Area (sqm)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 77);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_25_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Number of Rooms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 78);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_25_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Number of Bathrooms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 79);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_25_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Number of Floors\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 80);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_25_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Facing Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 81)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 82)(9, \"li\")(10, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_14_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"unitFacing\", \"right_of_facade\"));\n    });\n    i0.ɵɵtext(11, \"Right Of Facade\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"li\")(13, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_14_Template_a_click_13_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"unitFacing\", \"left_of_facade\"));\n    });\n    i0.ɵɵtext(14, \" Left Of Facade \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"li\")(16, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_14_Template_a_click_16_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"unitFacing\", \"side_view\"));\n    });\n    i0.ɵɵtext(17, \" Side View \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\")(19, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_14_Template_a_click_19_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"unitFacing\", \"rear_view\"));\n    });\n    i0.ɵɵtext(20, \" Rear View \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"unitFacing\")) == null ? null : tmp_2_0.value) || \"Select apartment location\");\n  }\n}\nfunction AddPropertyComponent_div_25_div_15_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Description cannot exceed 1000 characters. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_25_div_15_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, AddPropertyComponent_div_25_div_15_div_5_div_1_Template, 2, 0, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.step2Form.get(\"unitDescription\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"maxlength\"]);\n  }\n}\nfunction AddPropertyComponent_div_25_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Unit Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"textarea\", 83);\n    i0.ɵɵtext(4, \"            \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AddPropertyComponent_div_25_div_15_div_5_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementStart(6, \"small\", 84);\n    i0.ɵɵtext(7, \" Maximum 1000 characters \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_2_0 = ctx_r1.step2Form.get(\"unitDescription\")) == null ? null : tmp_2_0.invalid) && (((tmp_2_0 = ctx_r1.step2Form.get(\"unitDescription\")) == null ? null : tmp_2_0.touched) || ((tmp_2_0 = ctx_r1.step2Form.get(\"unitDescription\")) == null ? null : tmp_2_0.dirty))));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r1.step2Form.get(\"unitDescription\")) == null ? null : tmp_3_0.invalid) && (((tmp_3_0 = ctx_r1.step2Form.get(\"unitDescription\")) == null ? null : tmp_3_0.touched) || ((tmp_3_0 = ctx_r1.step2Form.get(\"unitDescription\")) == null ? null : tmp_3_0.dirty)));\n  }\n}\nfunction AddPropertyComponent_div_25_div_16_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_16_li_9_Template_a_click_1_listener() {\n      const deadline_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"buildingDeadline\", deadline_r20.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const deadline_r20 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", deadline_r20.key, \" \");\n  }\n}\nfunction AddPropertyComponent_div_25_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Building Deadline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 85)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 86);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_16_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"buildingDeadline\")) == null ? null : tmp_2_0.value) || \"Select building deadline\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildingDeadlineTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_17_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_17_li_9_Template_a_click_1_listener() {\n      const view_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"view\", view_r22.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const view_r22 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", view_r22.key, \" \");\n  }\n}\nfunction AddPropertyComponent_div_25_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"View\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 87)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 88);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_17_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"view\")) == null ? null : tmp_2_0.value) || \"Select apartment view\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.viewTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_18_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_18_li_9_Template_a_click_1_listener() {\n      const status_r24 = i0.ɵɵrestoreView(_r23).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"finishingType\", status_r24.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const status_r24 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(status_r24.key);\n  }\n}\nfunction AddPropertyComponent_div_25_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Finishing Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 89)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 90);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_18_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"finishingType\")) == null ? null : tmp_2_0.value) || \"Select finishing status\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.finishingType);\n  }\n}\nfunction AddPropertyComponent_div_25_div_19_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_19_li_9_Template_a_click_1_listener() {\n      const condition_r26 = i0.ɵɵrestoreView(_r25).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"fitOutCondition\", condition_r26.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const condition_r26 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(condition_r26.key);\n  }\n}\nfunction AddPropertyComponent_div_25_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Fit Out Condition Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 91)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 92);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_19_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"fitOutCondition\")) == null ? null : tmp_2_0.value) || \"Select fit out condition\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.fitOutConditionTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_20_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_20_li_9_Template_a_click_1_listener() {\n      const furnishing_r28 = i0.ɵɵrestoreView(_r27).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"furnishingStatus\", furnishing_r28.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const furnishing_r28 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(furnishing_r28.key);\n  }\n}\nfunction AddPropertyComponent_div_25_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Furnishing Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 93)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 94);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_20_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"furnishingStatus\")) == null ? null : tmp_2_0.value) || \"Select furnishing status\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.furnishingStatusTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_21_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_21_li_9_Template_a_click_1_listener() {\n      const layout_r30 = i0.ɵɵrestoreView(_r29).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"groundLayoutStatus\", layout_r30.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const layout_r30 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(layout_r30.key);\n  }\n}\nfunction AddPropertyComponent_div_25_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Ground Layout Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 95)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 96);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_21_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"groundLayoutStatus\")) == null ? null : tmp_2_0.value) || \"Select ground layout status\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.groundLayoutStatusTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_22_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_22_li_9_Template_a_click_1_listener() {\n      const design_r32 = i0.ɵɵrestoreView(_r31).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"unitDesign\", design_r32.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const design_r32 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(design_r32.key);\n  }\n}\nfunction AddPropertyComponent_div_25_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Unit Design\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 97)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 98);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_22_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"unitDesign\")) == null ? null : tmp_2_0.value) || \"Select unit design\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.unitDesignTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_23_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_23_li_9_Template_a_click_1_listener() {\n      const activity_r34 = i0.ɵɵrestoreView(_r33).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"activity\", activity_r34.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const activity_r34 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r34.key);\n  }\n}\nfunction AddPropertyComponent_div_25_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Activity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 99)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 100);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_23_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"activity\")) == null ? null : tmp_2_0.value) || \"Select activity\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.activityTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_24_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_24_li_9_Template_a_click_1_listener() {\n      const delivery_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"deliveryStatus\", delivery_r36.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const delivery_r36 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r36.key);\n  }\n}\nfunction AddPropertyComponent_div_25_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Delivery Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 101)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 102);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_24_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"deliveryStatus\")) == null ? null : tmp_2_0.value) || \"Select delivery status\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.deliveryTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Delivery Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 103);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_25_div_26_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_26_li_9_Template_a_click_1_listener() {\n      const legal_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"legalStatus\", legal_r38.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const legal_r38 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(legal_r38.key);\n  }\n}\nfunction AddPropertyComponent_div_25_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \" Legal Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 104)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 105);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_26_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"legalStatus\")) == null ? null : tmp_2_0.value) || \"Select legal status\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.legalTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_27_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_25_div_27_li_9_Template_a_click_1_listener() {\n      const financial_r40 = i0.ɵɵrestoreView(_r39).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep2Value(\"financialStatus\", financial_r40.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const financial_r40 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(financial_r40.key);\n  }\n}\nfunction AddPropertyComponent_div_25_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \" Financial Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 106)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 107);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_25_div_27_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step2Form.get(\"financialStatus\")) == null ? null : tmp_2_0.value) || \"Select financial status\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.financialStatusTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_div_28_li_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"input\", 117);\n    i0.ɵɵlistener(\"change\", function AddPropertyComponent_div_25_div_28_li_15_div_1_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const accessory_r43 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleAccessory(accessory_r43.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 118);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const accessory_r43 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"accessory_\" + accessory_r43.value)(\"checked\", ctx_r1.isAccessorySelected(accessory_r43.value));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"accessory_\" + accessory_r43.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", accessory_r43.key, \" \");\n  }\n}\nfunction AddPropertyComponent_div_25_div_28_li_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 110);\n    i0.ɵɵtemplate(1, AddPropertyComponent_div_25_div_28_li_15_div_1_Template, 4, 4, \"div\", 116);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const accessory_r43 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", accessory_r43.value !== \"alltheabovearesuitable\");\n  }\n}\nfunction AddPropertyComponent_div_25_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \" Other Accessories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 108)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 109)(9, \"li\", 110)(10, \"div\", 111)(11, \"input\", 112);\n    i0.ɵɵlistener(\"change\", function AddPropertyComponent_div_25_div_28_Template_input_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAllAccessoriesChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"label\", 113);\n    i0.ɵɵtext(13, \" All The Above Are Suitable \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(14, \"hr\", 114);\n    i0.ɵɵtemplate(15, AddPropertyComponent_div_25_div_28_li_15_Template, 2, 1, \"li\", 115);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.getSelectedAccessoriesText() || \"Select additional amenities\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"checked\", ctx_r1.isAccessorySelected(\"all_the_above_are_suitable\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.otherAccessoriesTypes);\n  }\n}\nfunction AddPropertyComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 36);\n    i0.ɵɵtemplate(2, AddPropertyComponent_div_25_div_2_Template, 4, 0, \"div\", 68)(3, AddPropertyComponent_div_25_div_3_Template, 4, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 36);\n    i0.ɵɵtemplate(5, AddPropertyComponent_div_25_div_5_Template, 10, 2, \"div\", 68)(6, AddPropertyComponent_div_25_div_6_Template, 4, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 36);\n    i0.ɵɵtemplate(8, AddPropertyComponent_div_25_div_8_Template, 4, 0, \"div\", 69)(9, AddPropertyComponent_div_25_div_9_Template, 4, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 36);\n    i0.ɵɵtemplate(11, AddPropertyComponent_div_25_div_11_Template, 4, 0, \"div\", 68)(12, AddPropertyComponent_div_25_div_12_Template, 4, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AddPropertyComponent_div_25_div_13_Template, 4, 0, \"div\", 70)(14, AddPropertyComponent_div_25_div_14_Template, 21, 1, \"div\", 56)(15, AddPropertyComponent_div_25_div_15_Template, 8, 4, \"div\", 56)(16, AddPropertyComponent_div_25_div_16_Template, 10, 2, \"div\", 56)(17, AddPropertyComponent_div_25_div_17_Template, 10, 2, \"div\", 56)(18, AddPropertyComponent_div_25_div_18_Template, 10, 2, \"div\", 56)(19, AddPropertyComponent_div_25_div_19_Template, 10, 2, \"div\", 56)(20, AddPropertyComponent_div_25_div_20_Template, 10, 2, \"div\", 56)(21, AddPropertyComponent_div_25_div_21_Template, 10, 2, \"div\", 56)(22, AddPropertyComponent_div_25_div_22_Template, 10, 2, \"div\", 56)(23, AddPropertyComponent_div_25_div_23_Template, 10, 2, \"div\", 56)(24, AddPropertyComponent_div_25_div_24_Template, 10, 2, \"div\", 56)(25, AddPropertyComponent_div_25_div_25_Template, 4, 0, \"div\", 56)(26, AddPropertyComponent_div_25_div_26_Template, 10, 2, \"div\", 56)(27, AddPropertyComponent_div_25_div_27_Template, 10, 2, \"div\", 56)(28, AddPropertyComponent_div_25_div_28_Template, 16, 3, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.step2Form);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"buildingNumber\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"unitNumber\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"floor\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"unitArea\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"buildingArea\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"groundArea\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"numberOfRooms\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"numberOfBathrooms\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"numberOfFloors\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"unitFacing\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"unitDescription\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"buildingDeadline\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"view\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"finishingType\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"fitOutCondition\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowFurnishingStatusField() && ctx_r1.shouldShowField(\"furnishingStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowGroundLayoutStatusField() && ctx_r1.shouldShowField(\"groundLayoutStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowUnitDesignField() && ctx_r1.shouldShowField(\"unitDesign\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"activity\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"deliveryStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"deliveryDate\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"legalStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"financialStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"otherAccessories\"));\n  }\n}\nfunction AddPropertyComponent_div_26_div_1_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Requested over cannot exceed 255 characters. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_26_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, AddPropertyComponent_div_26_div_1_div_4_div_1_Template, 2, 0, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.step3Form.get(\"requestedOver\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"maxlength\"]);\n  }\n}\nfunction AddPropertyComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \" Requested Over \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 119);\n    i0.ɵɵtemplate(4, AddPropertyComponent_div_26_div_1_div_4_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_2_0 = ctx_r1.step3Form.get(\"requestedOver\")) == null ? null : tmp_2_0.invalid) && (((tmp_2_0 = ctx_r1.step3Form.get(\"requestedOver\")) == null ? null : tmp_2_0.touched) || ((tmp_2_0 = ctx_r1.step3Form.get(\"requestedOver\")) == null ? null : tmp_2_0.dirty))));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r1.step3Form.get(\"requestedOver\")) == null ? null : tmp_3_0.invalid) && (((tmp_3_0 = ctx_r1.step3Form.get(\"requestedOver\")) == null ? null : tmp_3_0.touched) || ((tmp_3_0 = ctx_r1.step3Form.get(\"requestedOver\")) == null ? null : tmp_3_0.dirty)));\n  }\n}\nfunction AddPropertyComponent_div_26_div_2_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_26_div_2_li_9_Template_a_click_1_listener() {\n      const payment_r45 = i0.ɵɵrestoreView(_r44).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.selectStep3Value(\"paymentSystem\", payment_r45.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const payment_r45 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(payment_r45.key);\n  }\n}\nfunction AddPropertyComponent_div_26_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \" Payment System \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"button\", 120)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ul\", 121);\n    i0.ɵɵtemplate(9, AddPropertyComponent_div_26_div_2_li_9_Template, 3, 1, \"li\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.formatUnitTypeKey((tmp_2_0 = ctx_r1.step3Form.get(\"paymentSystem\")) == null ? null : tmp_2_0.value) || \"Select payment system\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paymentTypes);\n  }\n}\nfunction AddPropertyComponent_div_26_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23)(2, \"label\", 24);\n    i0.ɵɵtext(3, \"Price Per Meter In Cash\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 122);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 23)(6, \"label\", 24);\n    i0.ɵɵtext(7, \"Total Price In Cash\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_div_26_ng_container_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Total Price In Installment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 125);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddPropertyComponent_div_26_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23)(2, \"label\", 24);\n    i0.ɵɵtext(3, \"Price Per Meter In Installment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AddPropertyComponent_div_26_ng_container_4_div_5_Template, 4, 0, \"div\", 56);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"totalPriceInInstallment\"));\n  }\n}\nfunction AddPropertyComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, AddPropertyComponent_div_26_div_1_Template, 5, 4, \"div\", 56)(2, AddPropertyComponent_div_26_div_2_Template, 10, 2, \"div\", 56)(3, AddPropertyComponent_div_26_ng_container_3_Template, 9, 0, \"ng-container\", 7)(4, AddPropertyComponent_div_26_ng_container_4_Template, 6, 1, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.step3Form);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"requestedOver\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowField(\"paymentSystem\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowCashFields() && ctx_r1.shouldShowField(\"pricePerMeterInCash\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowInstallmentFields() && ctx_r1.shouldShowField(\"pricePerMeterInInstallment\"));\n  }\n}\nfunction AddPropertyComponent_div_27_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 140);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"diagram\"), \" \");\n  }\n}\nfunction AddPropertyComponent_div_27_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 140);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"layout\"), \" \");\n  }\n}\nfunction AddPropertyComponent_div_27_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 140);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"videos\"), \" \");\n  }\n}\nfunction AddPropertyComponent_div_27_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 140);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"locationInMasterPlan\"), \" \");\n  }\n}\nfunction AddPropertyComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 126)(2, \"div\", 127)(3, \"label\", 128)(4, \"div\", 129);\n    i0.ɵɵelement(5, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 131);\n    i0.ɵɵtext(7, \" Upload image of main unit \");\n    i0.ɵɵtemplate(8, AddPropertyComponent_div_27_span_8_Template, 2, 1, \"span\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 133);\n    i0.ɵɵlistener(\"change\", function AddPropertyComponent_div_27_Template_input_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"diagram\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 127)(11, \"label\", 134)(12, \"div\", 129);\n    i0.ɵɵelement(13, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 131);\n    i0.ɵɵtext(15, \" Upload photos to the gallery \");\n    i0.ɵɵtemplate(16, AddPropertyComponent_div_27_span_16_Template, 2, 1, \"span\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 135);\n    i0.ɵɵlistener(\"change\", function AddPropertyComponent_div_27_Template_input_change_17_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"layout\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 127)(19, \"label\", 136)(20, \"div\", 129);\n    i0.ɵɵelement(21, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 131);\n    i0.ɵɵtext(23, \" Upload project videos \");\n    i0.ɵɵtemplate(24, AddPropertyComponent_div_27_span_24_Template, 2, 1, \"span\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 137);\n    i0.ɵɵlistener(\"change\", function AddPropertyComponent_div_27_Template_input_change_25_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"videos\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 127)(27, \"label\", 138)(28, \"div\", 129);\n    i0.ɵɵelement(29, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 131);\n    i0.ɵɵtext(31, \" Upload unit plan \");\n    i0.ɵɵtemplate(32, AddPropertyComponent_div_27_span_32_Template, 2, 1, \"span\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"input\", 139);\n    i0.ɵɵlistener(\"change\", function AddPropertyComponent_div_27_Template_input_change_33_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"locationInMasterPlan\"));\n    });\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.step4Form);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"diagram\") > 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"layout\") > 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"videos\") > 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"locationInMasterPlan\") > 0);\n  }\n}\nfunction AddPropertyComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 141)(2, \"div\", 142);\n    i0.ɵɵelement(3, \"i\", 143);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\", 144);\n    i0.ɵɵtext(5, \"Ready to Submit Property\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 145);\n    i0.ɵɵtext(7, \" Please review all the information you've entered and click submit to add the property. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 146)(9, \"strong\");\n    i0.ɵɵtext(10, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ul\", 147)(12, \"li\");\n    i0.ɵɵtext(13, \"\\u2022 Owner information is now collected in Step 1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"li\");\n    i0.ɵɵtext(15, \"\\u2022 Legal Status is now part of Step 2 (Unit Information)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"li\");\n    i0.ɵɵtext(17, \"\\u2022 All forms have been reorganized for better user experience\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 148)(19, \"button\", 149);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_28_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.submitForm(false));\n    });\n    i0.ɵɵelementStart(20, \"span\", 150);\n    i0.ɵɵtext(21, \" Add Property\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"button\", 151);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_28_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.submitForm(true));\n    });\n    i0.ɵɵelementStart(23, \"span\", 150);\n    i0.ɵɵelement(24, \"i\", 152);\n    i0.ɵɵtext(25, \" Add Property & Publish \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 153);\n    i0.ɵɵtext(27, \" Please wait... \");\n    i0.ɵɵelement(28, \"span\", 154);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.step5Form);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isCurrentFormValid());\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.step5Form.valid);\n  }\n}\nfunction AddPropertyComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 155)(1, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_29_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancel());\n    });\n    i0.ɵɵtext(2, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 157);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_29_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵelementStart(4, \"span\", 158);\n    i0.ɵɵtext(5, \" Next - Location Information \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isCurrentFormValid());\n  }\n}\nfunction AddPropertyComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 159)(1, \"button\", 157);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_30_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵelementStart(2, \"span\", 158);\n    i0.ɵɵtext(3, \" Next - Unit Data \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isCurrentFormValid());\n  }\n}\nfunction AddPropertyComponent_div_31_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Next - Payment Details \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_div_31_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Next - Media & Documents \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_div_31_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Next - Review & Submit \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AddPropertyComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 159)(1, \"button\", 157);\n    i0.ɵɵlistener(\"click\", function AddPropertyComponent_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵelementStart(2, \"span\", 158);\n    i0.ɵɵtemplate(3, AddPropertyComponent_div_31_ng_container_3_Template, 2, 0, \"ng-container\", 7)(4, AddPropertyComponent_div_31_ng_container_4_Template, 2, 0, \"ng-container\", 7)(5, AddPropertyComponent_div_31_ng_container_5_Template, 2, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isCurrentFormValid());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep === 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep === 3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep === 4);\n  }\n}\n/*\n\n    {  key: 'I Villa', value: 'i_villa' },\n    { key: 'Residential Buildings', value: 'residential_buildings' },\n    { key: 'Mixed Housings', value: ' mixed_housings' },\n    { key: 'cooperatives', value: ' cooperatives' },\n    { key: 'youth units', value: 'youth_units' },\n    { key: 'ganat misr', value: 'ganat_misr' },\n    { key: 'dar misr', value: 'dar_misr' },\n    { key: 'sakan misr', value: 'sakan_misr' },\n    { key: 'industrial_lands', value: 'industrial_lands' },\n    { key: ' cabin', value: ' cabin' },\n    { key: 'vacation villa', value: ' vacation_villa' },\n\n\n{ key: 'Hotels', value: 'hotels' },\n\n\n    { key: 'Commercial Administrative Buildings', value: 'commercial_administrative_buildings' },\n    { key: 'Commercial Stores', value: 'commercial_stores' },\n    { key: 'commercial_units', value: ' commercial_units' },\n    { key: 'shops', value: 'shops' },\n\n\n\n*/\nexport class AddPropertyComponent {\n  fb;\n  router;\n  propertyService;\n  cdr;\n  totalSteps = 5;\n  currentStep = 0;\n  selectedCityId;\n  selectedCityName;\n  selectedAreaName;\n  selectedSubAreaName;\n  selectedUnitType;\n  cities = [];\n  unitTypes = [];\n  areas = [];\n  subAreas = [];\n  isLoadingCities = false;\n  otherAccessoriesList = [{\n    key: 'GARAGE',\n    value: 'garage'\n  }, {\n    key: 'CLUBHOUSE',\n    value: 'clubhouse'\n  }, {\n    key: 'CLUB',\n    value: 'club'\n  }, {\n    key: 'STORAGE',\n    value: 'storage'\n  }, {\n    key: 'ELEVATOR',\n    value: 'elevator'\n  }, {\n    key: 'SWIMMING POOL',\n    value: 'swimming_pool'\n  }, {\n    key: 'ALL THE ABOVE',\n    value: 'all_the_above_are_suitable'\n  }];\n  //get brokerId from session\n  brokerId;\n  finishingType = [{\n    key: 'On Brick',\n    value: 'on_brick'\n  }, {\n    key: 'Semi Finished',\n    value: 'semi_finished'\n  }, {\n    key: 'Company Finished',\n    value: 'company_finished'\n  }, {\n    key: 'Super Lux',\n    value: 'super_lux'\n  }, {\n    key: 'Ultra Super Lux',\n    value: 'ultra_super_lux'\n  }];\n  floorTypes = [{\n    key: 'Ground',\n    value: 'ground'\n  }, {\n    key: 'Last Floor',\n    value: 'last_floor'\n  }, {\n    key: 'Repeated',\n    value: 'repeated'\n  }, {\n    key: 'All Of The Above',\n    value: 'all_the_above_are_suitable'\n  }];\n  viewTypes = [{\n    key: 'Water View',\n    value: 'water_view'\n  }, {\n    key: 'Gardens And Landscape',\n    value: 'gardens_and_landscape'\n  }, {\n    key: 'Street',\n    value: 'street'\n  }, {\n    key: 'Entertainment Area',\n    value: 'entertainment_area'\n  }, {\n    key: 'Garden  ',\n    value: 'garden'\n  }, {\n    key: 'Main Street',\n    value: ' main_street'\n  }, {\n    key: 'Square',\n    value: 'square'\n  }, {\n    key: 'Side Street',\n    value: 'side_street'\n  }, {\n    key: 'Rear View',\n    value: 'rear_view'\n  }];\n  deliveryTypes = [{\n    key: 'Immediate Delivery',\n    value: 'immediate_delivery'\n  }, {\n    key: 'Under Construction',\n    value: 'under_construction'\n  }];\n  activityTypes = [{\n    key: 'Administrative Only',\n    value: 'administrative_only'\n  }, {\n    key: 'Commercial Only',\n    value: 'commercial_only'\n  }, {\n    key: 'Medical Only',\n    value: 'medical_only'\n  }, {\n    key: 'Administrative And Commercial',\n    value: 'administrative_and_commercial'\n  }, {\n    key: 'Administrative Commercial And Medical',\n    value: 'administrative_commercial_and_medical'\n  }];\n  fitOutConditionTypes = [{\n    key: 'Unfitted',\n    value: 'unfitted'\n  }, {\n    key: 'Fully Fitted',\n    value: 'fully_fitted'\n  }, {\n    key: 'All The Above Are Suitable',\n    value: 'all_the_above_are_suitable'\n  }];\n  furnishingStatusTypes = [{\n    key: 'Unfurnished',\n    value: 'unfurnished'\n  }, {\n    key: 'Furnished With Air Conditioners',\n    value: 'furnished_with_air_conditioners'\n  }, {\n    key: 'Furnished Without Air Conditioners',\n    value: 'furnished_without_air_conditioners'\n  }];\n  groundLayoutStatusTypes = [{\n    key: 'Vacant Land',\n    value: 'vacant_land'\n  }, {\n    key: 'Under Construction',\n    value: 'under_construction'\n  }, {\n    key: 'Fully Built',\n    value: 'fully_built'\n  }, {\n    key: 'All Acceptable',\n    value: 'all_acceptable'\n  }];\n  unitDesignTypes = [{\n    key: 'Custom Design',\n    value: 'custom_design'\n  }, {\n    key: 'One Apartment Per Floor',\n    value: 'one_apartment_per_floor'\n  }, {\n    key: 'Two Apartments Per Floor',\n    value: 'two_apartments_per_floor'\n  }, {\n    key: 'More Than Two Apartments Per Floor',\n    value: 'more_than_two_apartments_per_floor'\n  }, {\n    key: 'All Acceptable',\n    value: 'all_acceptable'\n  }];\n  otherAccessoriesTypes = [{\n    key: 'Garage',\n    value: 'garage'\n  }, {\n    key: 'Clubhouse',\n    value: 'clubhouse'\n  }, {\n    key: 'Club',\n    value: 'club'\n  }, {\n    key: 'Storage',\n    value: 'storage'\n  }, {\n    key: 'Elevator',\n    value: 'elevator'\n  }, {\n    key: 'Swimming Pool',\n    value: 'swimming_pool'\n  }];\n  selectedAccessories = [];\n  paymentTypes = [{\n    key: 'Cash',\n    value: 'cash'\n  }, {\n    key: 'Installment',\n    value: 'installment'\n  }, {\n    key: 'All Of The Above Are Suitable ',\n    value: 'all_of_the_above_are_suitable'\n  }];\n  legalTypes = [{\n    key: 'Licensed',\n    value: 'licensed'\n  }, {\n    key: 'Reconciled',\n    value: 'reconciled'\n  }, {\n    key: 'Reconciliation Required',\n    value: 'reconciliation_required'\n  }];\n  financialStatusTypes = [{\n    key: 'paid_in_full ',\n    value: 'paid_in_full'\n  }, {\n    key: 'partially_paid_with_remaining_installments ',\n    value: 'partially_paid_with_remaining_installments'\n  }];\n  buildingDeadlineTypes = [{\n    key: 'Grace Period Allowed',\n    value: 'grace_period_allowed'\n  }, {\n    key: 'No Grace Period',\n    value: 'no_grace_period'\n  }];\n  // Step 0 options\n  compoundOptions = [{\n    key: 'Outside Compound',\n    value: 'outside_compound'\n  }, {\n    key: 'Inside Compound',\n    value: 'inside_compound'\n  }, {\n    key: 'village',\n    value: 'village'\n  }];\n  propertyTypeOptions = [{\n    key: 'Sale',\n    value: 'sale'\n  }, {\n    key: 'Rent',\n    value: 'rent_out'\n  }];\n  // All unit types for filtering\n  allUnitTypes = [];\n  // Unit types for outside compound\n  outsideCompoundUnitTypes = [\n  // Residential\n  {\n    key: 'Apartments',\n    value: 'apartments'\n  }, {\n    key: 'Duplexes',\n    value: 'duplexes'\n  }, {\n    key: 'Studios',\n    value: 'studios'\n  }, {\n    key: 'Penthouses',\n    value: 'penthouses'\n  }, {\n    key: 'Basement',\n    value: 'basement'\n  }, {\n    key: 'Roofs',\n    value: 'roofs'\n  },\n  // Villas\n  {\n    key: 'Villas',\n    value: 'villas'\n  }, {\n    key: 'Full Buildings',\n    value: 'full_buildings'\n  },\n  // Commercial/Administrative\n  {\n    key: 'Administrative Units',\n    value: 'administrative_units'\n  }, {\n    key: 'Medical Clinics',\n    value: 'medical_clinics'\n  }, {\n    key: 'Commercial Stores',\n    value: 'commercial_stores'\n  }, {\n    key: 'Pharmacies',\n    value: 'pharmacies'\n  },\n  // Industrial\n  {\n    key: 'Warehouses',\n    value: 'warehouses'\n  }, {\n    key: 'Factories',\n    value: 'factories'\n  },\n  // Lands\n  {\n    key: 'Residential Villa Lands',\n    value: 'residential_villa_lands'\n  }, {\n    key: 'Administrative lands',\n    value: 'administrative_lands'\n  }, {\n    key: 'Residential Lands',\n    value: 'residential_lands'\n  }, {\n    key: 'Commercial Administrative Lands',\n    value: 'commercial_administrative_lands'\n  }, {\n    key: 'Medical Lands',\n    value: 'medical_lands'\n  }, {\n    key: 'Mixed Lands',\n    value: 'mixed_lands'\n  }, {\n    key: 'Warehouses Land',\n    value: 'warehouses_land'\n  }, {\n    key: 'Factory Lands',\n    value: 'factory_lands'\n  }];\n  insideCompoundUnitTypes = [\n  // Residential\n  {\n    key: 'Apartments',\n    value: 'apartments'\n  }, {\n    key: 'Duplexes',\n    value: 'duplexes'\n  }, {\n    key: 'Studios',\n    value: 'studios'\n  }, {\n    key: 'Penthouses',\n    value: 'penthouses'\n  }, {\n    key: 'I Villa',\n    value: 'i_villa'\n  },\n  //villas\n  {\n    key: 'Standalone Villas',\n    value: 'standalone_villas'\n  }, {\n    key: 'Town Houses',\n    value: 'town_houses'\n  }, {\n    key: 'Twin Houses',\n    value: 'twin_houses'\n  }\n  // // Commercial/Administrative\n  // { key: 'Administrative Units', value: 'administrative_units' },\n  // { key: 'Medical Clinics', value: 'medical_clinics' },\n  // { key: 'Commercial Stores', value: 'commercial_stores' },\n  // { key: 'Pharmacies', value: 'pharmacies' },\n  ];\n  RentalUnitTypes = [\n  // Residential\n  {\n    key: 'Apartments',\n    value: 'apartments'\n  }, {\n    key: 'Duplexes',\n    value: 'duplexes'\n  }, {\n    key: 'Studios',\n    value: 'studios'\n  }, {\n    key: 'Penthouses',\n    value: 'penthouses'\n  }, {\n    key: 'Basement',\n    value: 'basement'\n  }, {\n    key: 'Roofs',\n    value: 'roofs'\n  },\n  // villas\n  {\n    key: 'I Villa',\n    value: 'i_villa'\n  }, {\n    key: 'Twin Houses',\n    value: 'twin_houses'\n  }, {\n    key: 'Town Houses',\n    value: 'town_houses'\n  }, {\n    key: 'Standalone Villas',\n    value: 'standalone_villas'\n  }, {\n    key: 'Villas',\n    value: 'villas'\n  }, {\n    key: 'Full Buildings',\n    value: 'full_buildings'\n  },\n  // Commercial/Administrative\n  {\n    key: 'Administrative Units',\n    value: 'administrative_units'\n  }, {\n    key: 'Medical Clinics',\n    value: 'medical_clinics'\n  }, {\n    key: 'Commercial Stores',\n    value: 'commercial_stores'\n  }, {\n    key: 'Pharmacies',\n    value: 'pharmacies'\n  },\n  //other\n  {\n    key: 'chalets',\n    value: 'chalets'\n  },\n  // Industrial\n  {\n    key: 'Warehouses',\n    value: 'warehouses'\n  }, {\n    key: 'Factories',\n    value: 'factories'\n  }];\n  // Filtered unit types based on compound selection\n  filteredUnitTypes = [];\n  step0Form;\n  step1Form;\n  step2Form;\n  step3Form;\n  step4Form;\n  step5Form;\n  constructor(fb, router, propertyService, cdr) {\n    this.fb = fb;\n    this.router = router;\n    this.propertyService = propertyService;\n    this.cdr = cdr;\n  }\n  ngOnInit() {\n    const user = localStorage.getItem('currentUser');\n    this.brokerId = user ? JSON.parse(user).brokerId : null;\n    this.initForms();\n    this.loadUnitTypes();\n    this.loadCities();\n    this.loadAreas();\n    this.filteredUnitTypes = [];\n  }\n  initForms() {\n    // Step 0: Property Category Selection\n    this.step0Form = this.fb.group({\n      compoundType: ['', [Validators.required]],\n      propertyType: ['', [Validators.required]],\n      type: ['', [Validators.required]]\n    });\n    // Step 1: Basic Property Settings\n    this.step1Form = this.fb.group({\n      cityId: ['', [Validators.required]],\n      areaId: ['', [Validators.required]],\n      subAreaId: [''],\n      // Sub area field (optional)\n      mallName: ['', [Validators.maxLength(255)]],\n      compoundName: ['', [Validators.maxLength(255)]],\n      detailedAddress: ['', [Validators.required, Validators.maxLength(255)]],\n      location: ['', [Validators.pattern('https?://.+')]],\n      ownerName: ['', Validators.required],\n      ownerPhone: ['', [Validators.required, Validators.pattern('^01[0-2,5]{1}[0-9]{8}$')]]\n    });\n    // Step 2: Unit Information\n    this.step2Form = this.fb.group({\n      buildingNumber: ['', [Validators.maxLength(50)]],\n      unitNumber: ['', [Validators.maxLength(50)]],\n      floor: ['', [Validators.required]],\n      unitArea: ['', [Validators.required, Validators.min(1), Validators.pattern('^[0-9]*$')]],\n      buildingArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],\n      groundArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],\n      numberOfRooms: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]*$')]],\n      numberOfBathrooms: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]*$')]],\n      numberOfFloors: ['', [Validators.required, Validators.min(0), Validators.pattern('^[0-9]*$')]],\n      unitFacing: [''],\n      view: ['', [Validators.required]],\n      finishingType: ['', [Validators.required]],\n      fitOutCondition: [''],\n      furnishingStatus: [''],\n      groundLayoutStatus: [''],\n      unitDesign: [''],\n      activity: [''],\n      deliveryStatus: ['', [Validators.required]],\n      deliveryDate: [''],\n      buildingDeadline: [''],\n      // Building deadline field\n      financialStatus: [''],\n      // Financial status field\n      otherAccessories: [''],\n      // Optional field - no validators to avoid blocking navigation\n      legalStatus: [''],\n      // Moved from step5Form\n      unitDescription: ['', [Validators.maxLength(1000)]] // Unit description field\n    });\n    // Step 3: Financial Information\n    this.step3Form = this.fb.group({\n      requestedOver: ['', [Validators.maxLength(255)]],\n      // Requested over field (optional)\n      paymentSystem: ['', Validators.required],\n      pricePerMeterInInstallment: ['', [Validators.min(0)]],\n      totalPriceInInstallment: ['', [Validators.min(0)]],\n      pricePerMeterInCash: ['', [Validators.min(0)]],\n      totalPriceInCash: ['', [Validators.min(0)]]\n    });\n    // Step 4: Project Documents\n    this.step4Form = this.fb.group({\n      diagram: [[]],\n      layout: [[]],\n      videos: [[]],\n      locationInMasterPlan: [[]]\n    });\n    // Step 5: Owner Information\n    this.step5Form = this.fb.group({\n      // legalStatus moved to step2Form\n    });\n  }\n  // Get current form based on step\n  getCurrentForm() {\n    switch (this.currentStep) {\n      case 0:\n        return this.step0Form;\n      case 1:\n        return this.step1Form;\n      case 2:\n        return this.step2Form;\n      case 3:\n        return this.step3Form;\n      case 4:\n        return this.step4Form;\n      case 5:\n        return this.step5Form;\n      default:\n        return this.step0Form;\n    }\n  }\n  loadUnitTypes() {\n    this.propertyService.getUnitTypes().subscribe({\n      next: response => {\n        this.allUnitTypes = Object.entries(response.data).map(([key, value]) => ({\n          key,\n          value: value\n        }));\n        this.insideCompoundUnitTypes = this.allUnitTypes; // API data for inside compound\n        console.log('Raw API Response:', this.allUnitTypes);\n      },\n      error: err => {\n        console.error('Error loading unitTypes:', err);\n      },\n      complete: () => {\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  filterUnitTypes() {\n    const compoundType = this.step0Form.get('compoundType')?.value;\n    if (compoundType === 'outside_compound') {\n      this.filteredUnitTypes = this.outsideCompoundUnitTypes;\n    } else if (compoundType === 'inside_compound') {\n      this.filteredUnitTypes = this.insideCompoundUnitTypes;\n    } else if (compoundType === 'village') {\n      // Use property type options for village\n      this.filteredUnitTypes = this.RentalUnitTypes;\n    } else {\n      this.filteredUnitTypes = [];\n    }\n    this.step0Form.patchValue({\n      type: ''\n    });\n    this.selectedUnitType = '';\n    this.cdr.detectChanges();\n  }\n  loadCities() {\n    this.isLoadingCities = true;\n    this.propertyService.getCities().subscribe({\n      next: response => {\n        if (response && response.data) {\n          this.cities = response.data;\n        } else {\n          console.warn('No cities data in response');\n          this.cities = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading cities:', err);\n      },\n      complete: () => {\n        this.isLoadingCities = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  loadAreas(cityId) {\n    this.propertyService.getAreas(cityId).subscribe({\n      next: response => {\n        if (response && response.data) {\n          this.areas = response.data;\n        } else {\n          console.warn('No areas data in response');\n          this.areas = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading areas:', err);\n        this.areas = [];\n      },\n      complete: () => {\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  loadSubAreas(areaId) {\n    this.propertyService.gitsubAreas(areaId).subscribe({\n      next: response => {\n        if (response && response.data) {\n          this.subAreas = response.data;\n        } else {\n          console.warn('No sub-areas data in response');\n          this.subAreas = [];\n        }\n      },\n      error: err => {\n        console.error('Error loading sub-areas:', err);\n        this.subAreas = [];\n      },\n      complete: () => {\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  //**************************************************************** */\n  // STEP 0\n  filterPropertyTypes() {\n    const compoundType = this.step0Form.get('compoundType')?.value;\n    if (compoundType === 'village') {\n      // Only show 'Rent' option for village compound type\n      return this.propertyTypeOptions.filter(option => option.value === 'rent_out');\n    }\n    // Show all property type options for other compound types\n    return this.propertyTypeOptions;\n  }\n  //******************** */\n  // STEP 2\n  getFieldsToShow() {\n    const compoundType = this.step0Form.get('compoundType')?.value;\n    const type = this.step0Form.get('type')?.value;\n    console.log(compoundType, type);\n    console.log(compoundType === 'outside_compound' && (type == 'villas' || type == 'full_buildings'));\n    // For outside compound apartments\n    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {\n      return ['buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryStatus', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type == 'villas' || type == 'full_buildings')) {\n      return ['buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\n      return ['mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition', 'deliveryStatus', 'activity', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type === 'warehouses' || type === 'factories')) {\n      return ['buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'outside_compound' && (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' || type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands')) {\n      return ['unitNumber', 'groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'legalStatus', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'inside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'i_villa')) {\n      return ['compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'inside_compound' && (type === 'standalone_villas' || type === 'twin_houses' || type === 'town_houses')) {\n      return ['compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    } else if (compoundType === 'inside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\n      return ['compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'deliveryStatus', 'fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\n    }\n    return [];\n  }\n  // Check if a specific field should be shown\n  shouldShowField(fieldName) {\n    return this.getFieldsToShow().includes(fieldName);\n  }\n  // /*/***************************** */\n  selectCity(cityId, cityName) {\n    this.selectedCityId = cityId;\n    this.selectedCityName = cityName;\n    this.step1Form.patchValue({\n      cityId: cityId\n    });\n    this.loadAreas(cityId);\n  }\n  selectUnitType(UnitValue) {\n    this.selectedUnitType = UnitValue;\n    this.step0Form.patchValue({\n      type: UnitValue\n    });\n    // Clear unitFacing field if unit type doesn't require it\n    const unitTypesWithFacing = ['apartments', 'duplexes', 'studios', 'penthouses'];\n    if (!unitTypesWithFacing.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        unitFacing: null\n      });\n    }\n    // Clear buildingArea and groundArea fields if unit type doesn't require them\n    const unitTypesWithAreaFields = ['standalone_villas', 'factory_lands', 'commercial_administrative_buildings', 'residential_buildings', 'warehouses'];\n    if (!unitTypesWithAreaFields.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        buildingArea: null,\n        groundArea: null\n      });\n    }\n    // Clear activity field if unit type doesn't require it\n    const unitTypesWithActivity = ['commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings'];\n    if (!unitTypesWithActivity.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        activity: null\n      });\n    }\n    // Clear groundLayoutStatus field if unit type doesn't require it\n    const unitTypesWithGroundLayout = ['factory_lands', 'warehouses', 'residential_buildings', 'commercial_administrative_buildings'];\n    if (!unitTypesWithGroundLayout.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        groundLayoutStatus: null\n      });\n    }\n    // Clear unitDesign field if unit type doesn't require it\n    const unitTypesWithUnitDesign = ['standalone_villas'];\n    if (!unitTypesWithUnitDesign.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        unitDesign: null\n      });\n    }\n    // Clear fitOutCondition field if unit type doesn't require it\n    const unitTypesWithFitOutCondition = ['villas', 'full_buildings', 'pharmacies', 'factory_lands', 'warehouses', 'commercial_stores', 'commercial_administrative_buildings'];\n    if (!unitTypesWithFitOutCondition.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        fitOutCondition: null\n      });\n    }\n    // Clear furnishingStatus field if unit type doesn't require it\n    const unitTypesToHideFurnishing = ['pharmacies', 'commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings', 'administrative_units'];\n    if (unitTypesToHideFurnishing.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        furnishingStatus: null\n      });\n    }\n    // Clear legalStatus field if unit type doesn't require it\n    const unitTypesWithLegalStatus = ['duplexes', 'penthouses', 'basement', 'roofs'];\n    if (!unitTypesWithLegalStatus.includes(UnitValue)) {\n      this.step2Form.patchValue({\n        legalStatus: null\n      });\n    }\n  }\n  selectArea(areaId, areaName) {\n    this.selectedAreaName = areaName;\n    this.step1Form.patchValue({\n      areaId: areaId\n    });\n    this.loadSubAreas(areaId);\n  }\n  selectSubArea(subAreaId, subAreaName) {\n    this.selectedSubAreaName = subAreaName;\n    this.step1Form.patchValue({\n      subAreaId: subAreaId\n    });\n  }\n  // dropdown values for step 2\n  selectStep2Value(fieldName, value) {\n    this.step2Form.patchValue({\n      [fieldName]: value\n    });\n  }\n  //dropdown values for step 3\n  selectStep3Value(fieldName, value) {\n    this.step3Form.patchValue({\n      [fieldName]: value\n    });\n    // Clear price fields when payment system changes\n    if (fieldName === 'paymentSystem') {\n      this.clearPriceFields();\n    }\n  }\n  // Clear all price fields when payment system changes\n  clearPriceFields() {\n    this.step3Form.patchValue({\n      pricePerMeterInCash: null,\n      totalPriceInCash: null,\n      pricePerMeterInInstallment: null,\n      totalPriceInInstallment: null\n    });\n  }\n  // Check if cash price fields should be displayed\n  shouldShowCashFields() {\n    const paymentSystem = this.step3Form.get('paymentSystem')?.value;\n    return paymentSystem === 'cash' || paymentSystem === 'all_of_the_above_are_suitable';\n  }\n  // Check if installment price fields should be displayed\n  shouldShowInstallmentFields() {\n    const paymentSystem = this.step3Form.get('paymentSystem')?.value;\n    return paymentSystem === 'installment' || paymentSystem === 'all_of_the_above_are_suitable';\n  }\n  // Check if unitFacing field should be displayed\n  shouldShowUnitFacingField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithFacing = ['apartments', 'duplexes', 'studios', 'penthouses'];\n    return unitTypesWithFacing.includes(unitType);\n  }\n  // Check if buildingArea and groundArea fields should be displayed\n  shouldShowAreaFields() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithAreaFields = ['standalone_villas', 'factory_lands', 'commercial_administrative_buildings', 'residential_buildings', 'warehouses'];\n    return unitTypesWithAreaFields.includes(unitType);\n  }\n  // Check if groundLayoutStatus field should be displayed\n  shouldShowGroundLayoutStatusField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithGroundLayout = ['factory_lands', 'warehouses', 'residential_buildings', 'commercial_administrative_buildings'];\n    return unitTypesWithGroundLayout.includes(unitType);\n  }\n  // Check if activity field should be displayed\n  shouldShowActivityField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithActivity = ['commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings'];\n    return unitTypesWithActivity.includes(unitType);\n  }\n  // Check if unitDesign field should be displayed\n  shouldShowUnitDesignField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithUnitDesign = ['standalone_villas'];\n    return unitTypesWithUnitDesign.includes(unitType);\n  }\n  // Check if legalStatus field should be displayed\n  shouldShowLegalStatusField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithLegalStatus = ['duplexes', 'penthouses', 'basement', 'roofs'];\n    return unitTypesWithLegalStatus.includes(unitType);\n  }\n  // Check if fitOutCondition field should be displayed\n  shouldShowFitOutConditionField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesWithFitOutCondition = ['villas', 'full_buildings', 'pharmacies', 'factory_lands', 'warehouses', 'commercial_stores', 'commercial_administrative_buildings'];\n    return unitTypesWithFitOutCondition.includes(unitType);\n  }\n  // Check if furnishingStatus field should be displayed\n  shouldShowFurnishingStatusField() {\n    const unitType = this.step0Form.get('type')?.value;\n    const unitTypesToHideFurnishing = ['pharmacies', 'commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings', 'administrative_units'];\n    return !unitTypesToHideFurnishing.includes(unitType);\n  }\n  // dropdown values for step 0\n  selectStep0Value(fieldName, value) {\n    console.log('selectStep0Value called:', {\n      fieldName,\n      value\n    });\n    this.step0Form.patchValue({\n      [fieldName]: value\n    });\n    // Filter unit types when compound type changes\n    if (fieldName === 'compoundType') {\n      this.filterUnitTypes();\n    }\n    // Handle unit type selection\n    if (fieldName === 'type') {\n      this.selectedUnitType = value;\n      this.selectUnitType(value); // Call existing logic for unit type selection\n    }\n    console.log('Step0 form after update:', this.step0Form.value);\n    // Trigger change detection to update button state\n    this.cdr.detectChanges();\n  }\n  //dropdown values for step 5\n  selectStep5Value(fieldName, value) {\n    this.step5Form.patchValue({\n      [fieldName]: value\n    });\n  }\n  submitForm(checkAd) {\n    var _this = this;\n    if (this.isCurrentFormValid()) {\n      // console.log(formData);\n      const httpFormData = new FormData();\n      // Add step0 form data\n      Object.keys(this.step0Form.value).forEach(key => {\n        httpFormData.append(key, this.step0Form.value[key]);\n      });\n      // Add step1 form data\n      Object.keys(this.step1Form.value).forEach(key => {\n        httpFormData.append(key, this.step1Form.value[key]);\n      });\n      // Fields to be included in additionalDetails array\n      const additionalDetailsFields = ['numberOfFloors'];\n      // Get unit type for conditional field inclusion\n      const unitType = this.step0Form.get('type')?.value;\n      // Add buildingArea and groundArea to additionalDetails only for specific unit types\n      const unitTypesWithAreaFields = ['standalone_villas', 'factory_lands', 'commercial_administrative_buildings', 'residential_buildings', 'warehouses'];\n      if (unitTypesWithAreaFields.includes(unitType)) {\n        additionalDetailsFields.push('groundArea');\n        additionalDetailsFields.push('buildingArea');\n      }\n      // Add activity to additionalDetails only for specific unit types\n      const unitTypesWithActivity = ['commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings'];\n      if (unitTypesWithActivity.includes(unitType)) {\n        additionalDetailsFields.push('activity');\n      }\n      // Add groundLayoutStatus to additionalDetails only for specific unit types\n      const unitTypesWithGroundLayout = ['factory_lands', 'warehouses', 'residential_buildings', 'commercial_administrative_buildings'];\n      if (unitTypesWithGroundLayout.includes(unitType)) {\n        additionalDetailsFields.push('groundLayoutStatus');\n      }\n      // Add unitDesign to additionalDetails only for specific unit types\n      const unitTypesWithUnitDesign = ['standalone_villas'];\n      if (unitTypesWithUnitDesign.includes(unitType)) {\n        additionalDetailsFields.push('unitDesign');\n      }\n      // Add fitOutCondition to additionalDetails only for specific unit types\n      const unitTypesWithFitOutCondition = ['villas', 'full_buildings', 'pharmacies', 'factory_lands', 'warehouses', 'commercial_stores', 'commercial_administrative_buildings'];\n      if (unitTypesWithFitOutCondition.includes(unitType)) {\n        additionalDetailsFields.push('fitOutCondition');\n      }\n      // Add furnishingStatus to additionalDetails only for specific unit types\n      const unitTypesToHideFurnishing = ['pharmacies', 'commercial_stores', 'factory_lands', 'warehouses', 'commercial_administrative_buildings', 'administrative_units'];\n      if (!unitTypesToHideFurnishing.includes(unitType)) {\n        additionalDetailsFields.push('furnishingStatus');\n      }\n      // Add unitFacing to additionalDetails only for specific unit types\n      const unitTypesWithFacing = ['apartments', 'duplexes', 'studios', 'penthouses'];\n      if (unitTypesWithFacing.includes(unitType)) {\n        additionalDetailsFields.push('unitFacing');\n      }\n      // Add step2 form data (excluding fields that go to additionalDetails and otherAccessories)\n      Object.keys(this.step2Form.value).forEach(key => {\n        if (key !== 'otherAccessories' && !additionalDetailsFields.includes(key)) {\n          httpFormData.append(key, this.step2Form.value[key]);\n        }\n      });\n      // Add step3 form data (conditionally based on payment system)\n      const paymentSystem = this.step3Form.get('paymentSystem')?.value;\n      // Always add payment system\n      httpFormData.append('paymentSystem', paymentSystem);\n      // Conditionally add price fields based on payment system\n      if (paymentSystem === 'cash') {\n        // Only send cash price fields\n        const pricePerMeterInCash = this.step3Form.get('pricePerMeterInCash')?.value;\n        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;\n        if (pricePerMeterInCash) {\n          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);\n        }\n        if (totalPriceInCash) {\n          httpFormData.append('totalPriceInCash', totalPriceInCash);\n        }\n      } else if (paymentSystem === 'installment') {\n        // Only send installment price fields\n        const pricePerMeterInInstallment = this.step3Form.get('pricePerMeterInInstallment')?.value;\n        const totalPriceInInstallment = this.step3Form.get('totalPriceInInstallment')?.value;\n        if (pricePerMeterInInstallment) {\n          httpFormData.append('pricePerMeterInInstallment', pricePerMeterInInstallment);\n        }\n        if (totalPriceInInstallment) {\n          httpFormData.append('totalPriceInInstallment', totalPriceInInstallment);\n        }\n      } else if (paymentSystem === 'all_of_the_above_are_suitable') {\n        // Send all price fields\n        const pricePerMeterInCash = this.step3Form.get('pricePerMeterInCash')?.value;\n        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;\n        const pricePerMeterInInstallment = this.step3Form.get('pricePerMeterInInstallment')?.value;\n        const totalPriceInInstallment = this.step3Form.get('totalPriceInInstallment')?.value;\n        if (pricePerMeterInCash) {\n          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);\n        }\n        if (totalPriceInCash) {\n          httpFormData.append('totalPriceInCash', totalPriceInCash);\n        }\n        if (pricePerMeterInInstallment) {\n          httpFormData.append('pricePerMeterInInstallment', pricePerMeterInInstallment);\n        }\n        if (totalPriceInInstallment) {\n          httpFormData.append('totalPriceInInstallment', totalPriceInInstallment);\n        }\n      }\n      // Add step5 form data (excluding legalStatus which goes to additionalDetails)\n      Object.keys(this.step5Form.value).forEach(key => {\n        if (key !== 'legalStatus') {\n          httpFormData.append(key, this.step5Form.value[key]);\n        }\n      });\n      // Create additionalDetails object\n      const additionalDetails = {};\n      // Add fields from step2Form\n      additionalDetailsFields.forEach(field => {\n        const value = this.step2Form.get(field)?.value;\n        if (value) {\n          additionalDetails[field] = value;\n        }\n      });\n      // Add legalStatus from step5Form only for specific unit types\n      const unitTypesWithLegalStatus = ['duplexes', 'penthouses', 'basement', 'roofs'];\n      if (unitTypesWithLegalStatus.includes(unitType)) {\n        const legalStatus = this.step5Form.get('legalStatus')?.value;\n        if (legalStatus) {\n          additionalDetails['legalStatus'] = legalStatus;\n        }\n      }\n      // Send additionalDetails as individual form fields (not JSON)\n      Object.keys(additionalDetails).forEach(key => {\n        httpFormData.append(`additionalDetails[${key}]`, additionalDetails[key]);\n      });\n      //add files\n      const fileFields = ['diagram', 'layout', 'videos', 'locationInMasterPlan'];\n      fileFields.forEach(field => {\n        const files = this.step4Form.get(field)?.value;\n        if (files && files.length) {\n          const isMultiple = ['layout', 'videos'].includes(field);\n          if (isMultiple) {\n            files.forEach(file => {\n              httpFormData.append(`${field}[]`, file);\n            });\n          } else {\n            httpFormData.append(field, files[0]);\n          }\n        }\n      });\n      // Handle otherAccessories as array\n      const accessoriesRaw = this.step2Form.get('otherAccessories')?.value;\n      const accessoriesArray = Array.isArray(accessoriesRaw) ? accessoriesRaw : [];\n      // Send otherAccessories as individual array elements\n      accessoriesArray.forEach((accessory, index) => {\n        httpFormData.append(`otherAccessories[${index}]`, accessory);\n      });\n      httpFormData.append('brokerId', this.brokerId.toString());\n      // Set as advertisement\n      if (checkAd) {\n        httpFormData.append('isAdvertisement', '1');\n      }\n      // Show loading state\n      const button = document.querySelector('.btn-primary');\n      if (button) {\n        button.classList.add('btn-loading');\n      }\n      this.propertyService.createProperty(httpFormData).subscribe({\n        next: function () {\n          var _ref = _asyncToGenerator(function* (response) {\n            console.log('Property data submitted:', response);\n            yield Swal.fire('Property data submitted:', '', response.status);\n            _this.router.navigate(['/broker/dataandproperties'], {\n              queryParams: {\n                success: 'add'\n              }\n            });\n          });\n          return function next(_x) {\n            return _ref.apply(this, arguments);\n          };\n        }(),\n        error: err => {\n          console.error('Error loading unitTypes:', err);\n          Swal.fire(err.message, '', err.status);\n          // Remove loading state\n          if (button) {\n            button.classList.remove('btn-loading');\n          }\n        },\n        complete: () => {\n          this.cdr.detectChanges();\n          // Remove loading state\n          if (button) {\n            button.classList.remove('btn-loading');\n          }\n        }\n      });\n    }\n  }\n  cancel() {\n    this.router.navigate(['/broker/dataandproperties']);\n  }\n  onFileChange(event, fieldName) {\n    if (event.target.files && event.target.files.length) {\n      const files = Array.from(event.target.files);\n      this.step4Form.patchValue({\n        [fieldName]: files\n      });\n      console.log(`${fieldName}: ${files.length} files selected`);\n    }\n  }\n  getFileCount(fieldName) {\n    const files = this.step4Form.get(fieldName)?.value;\n    return files && Array.isArray(files) ? files.length : 0;\n  }\n  // Check if current form is valid\n  isCurrentFormValid() {\n    const currentForm = this.getCurrentForm();\n    // For step 0, only check if unit type is selected\n    if (this.currentStep === 0) {\n      const compoundType = this.step0Form.get('compoundType')?.value;\n      const unitType = this.step0Form.get('type')?.value;\n      const isValid = !!(compoundType && unitType);\n      console.log('Step 0 validation:', {\n        compoundType,\n        unitType,\n        isValid\n      });\n      return isValid;\n    }\n    // For step 2, check only visible/required fields\n    if (this.currentStep === 2) {\n      return this.isStep2FormValid();\n    }\n    return currentForm.valid;\n  }\n  // Custom validation for Step 2 - only check visible fields\n  isStep2FormValid() {\n    const form = this.step2Form;\n    const fieldsToShow = this.getFieldsToShow();\n    // Required fields that must always be valid if they're shown\n    const requiredFields = ['unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus'];\n    for (const fieldName of requiredFields) {\n      if (fieldsToShow.includes(fieldName)) {\n        const control = form.get(fieldName);\n        if (!control || control.invalid) {\n          console.log(`Step 2 validation failed for field: ${fieldName}`, control?.errors);\n          return false;\n        }\n      }\n    }\n    // Check conditional fields only if they're shown and required for the specific unit type\n    const conditionalFields = ['unitFacing', 'legalStatus', 'fitOutCondition', 'furnishingStatus', 'groundLayoutStatus', 'unitDesign', 'activity'];\n    for (const fieldName of conditionalFields) {\n      if (fieldsToShow.includes(fieldName) && this.isFieldRequiredForUnitType(fieldName)) {\n        const control = form.get(fieldName);\n        if (!control || control.invalid) {\n          console.log(`Step 2 validation failed for conditional field: ${fieldName}`, control?.errors);\n          return false;\n        }\n      }\n    }\n    console.log('Step 2 validation passed');\n    return true;\n  }\n  // Check if a field is required for the current unit type\n  isFieldRequiredForUnitType(fieldName) {\n    switch (fieldName) {\n      case 'unitFacing':\n        return this.shouldShowUnitFacingField();\n      case 'legalStatus':\n        return this.shouldShowLegalStatusField();\n      case 'fitOutCondition':\n        return this.shouldShowFitOutConditionField();\n      case 'furnishingStatus':\n        return this.shouldShowFurnishingStatusField();\n      case 'groundLayoutStatus':\n        return this.shouldShowGroundLayoutStatusField();\n      case 'unitDesign':\n        return this.shouldShowUnitDesignField();\n      case 'activity':\n        return this.shouldShowActivityField();\n      default:\n        return false;\n    }\n  }\n  // Navigate to next step\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n  // Navigate to previous step\n  prevStep() {\n    if (this.currentStep > 0) {\n      this.currentStep--;\n    }\n  }\n  formatUnitTypeKey(key) {\n    if (!key || typeof key !== 'string') return '';\n    return key.split('_').map(word => word.trim() ? word[0].toUpperCase() + word.slice(1).toLowerCase() : '').join(' ');\n  }\n  toggleAccessory(value) {\n    const index = this.selectedAccessories.indexOf(value);\n    if (index > -1) {\n      this.selectedAccessories.splice(index, 1);\n    } else {\n      this.selectedAccessories.push(value);\n    }\n    // Update form control\n    this.step2Form.patchValue({\n      otherAccessories: [...this.selectedAccessories]\n    });\n  }\n  // Handle \"All The Above Are Suitable\" checkbox\n  onAllAccessoriesChange(event) {\n    if (event.target.checked) {\n      // Select all accessories\n      this.selectedAccessories = this.otherAccessoriesTypes.map(a => a.value);\n    } else {\n      // Unselect all accessories\n      this.selectedAccessories = [];\n    }\n    this.step2Form.patchValue({\n      otherAccessories: [...this.selectedAccessories]\n    });\n  }\n  isAccessorySelected(value) {\n    return this.selectedAccessories.includes(value);\n  }\n  getSelectedAccessoriesText() {\n    if (this.selectedAccessories.length === 0) {\n      return '';\n    }\n    if (this.selectedAccessories.length === 1) {\n      const accessory = this.otherAccessoriesTypes.find(a => a.value === this.selectedAccessories[0]);\n      return accessory ? accessory.key : '';\n    }\n    return `${this.selectedAccessories.length} accessories selected`;\n  }\n  // Get compound type text for display\n  getCompoundTypeText(value) {\n    if (!value) return '';\n    const option = this.compoundOptions.find(opt => opt.value === value);\n    return option ? option.key : '';\n  }\n  // Get unit type text for display\n  getUnitTypeText(value) {\n    if (!value) return '';\n    const unitType = this.filteredUnitTypes.find(unit => unit.value === value);\n    return unitType ? unitType.key : '';\n  }\n  static ɵfac = function AddPropertyComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AddPropertyComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.PropertyService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddPropertyComponent,\n    selectors: [[\"app-add-property\"]],\n    decls: 32,\n    vars: 21,\n    consts: [[\"noCities\", \"\"], [1, \"mb-5\", \"mt-0\"], [3, \"showCreateButton\"], [1, \"card\", \"rounded-4\"], [1, \"card-body\", \"p-10\"], [\"id\", \"add_property_stepper\", 1, \"stepper\", \"stepper-pills\", \"d-flex\", \"flex-column\"], [1, \"mb-5\", \"text-center\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"mb-2\"], [1, \"text-success\", \"fw-bold\"], [1, \"text-muted\", \"mx-1\"], [1, \"text-muted\"], [\"class\", \"text-primary cursor-pointer mb-2\", 3, \"click\", 4, \"ngIf\"], [1, \"progress\", \"h-8px\", \"bg-light-success\", \"w-75\", \"mx-auto\"], [\"role\", \"progressbar\", \"aria-valuenow\", \"50\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"bg-success\"], [1, \"mx-auto\", \"w-100\", \"pt-5\", \"pb-10\"], [3, \"formGroup\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-between pt-10\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center pt-10\", 4, \"ngIf\"], [1, \"text-dark-blue\", \"fw-bold\"], [1, \"text-dark-blue\", \"fw-normal\"], [1, \"text-primary\", \"cursor-pointer\", \"mb-2\", 3, \"click\"], [3, \"formGroup\"], [1, \"mb-10\"], [1, \"form-label\", \"fw-bold\", \"text-start\", \"d-block\"], [1, \"dropdown\"], [\"type\", \"button\", \"id\", \"compoundTypeDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"fas\", \"fa-chevron-down\"], [\"aria-labelledby\", \"compoundTypeDropdown\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"200px\", \"overflow-y\", \"auto\", \"position\", \"absolute\", \"z-index\", \"1000\"], [4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"id\", \"propertyTypeDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"propertyTypeDropdown\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"200px\", \"overflow-y\", \"auto\", \"position\", \"absolute\", \"z-index\", \"1000\"], [\"type\", \"button\", \"id\", \"unitTypeDropdownStep0\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 3, \"disabled\"], [\"aria-labelledby\", \"unitTypeDropdownStep0\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"200px\", \"overflow-y\", \"auto\", \"position\", \"absolute\", \"z-index\", \"1000\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"dropdown-item\", \"text-start\", 3, \"click\"], [1, \"row\", \"mb-10\"], [1, \"col-md-6\"], [\"type\", \"text\", \"formControlName\", \"ownerName\", \"placeholder\", \"Enter name\", 1, \"form-control\", \"text-start\"], [\"type\", \"text\", \"formControlName\", \"ownerPhone\", \"placeholder\", \"Enter phone number\", 1, \"form-control\", \"text-start\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"text-primary mb-2\", 4, \"ngIf\"], [\"class\", \"text-danger mb-2\", 4, \"ngIf\"], [\"type\", \"button\", \"id\", \"cityDropdownStep1\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 3, \"disabled\"], [\"aria-labelledby\", \"cityDropdownStep1\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"200px\", \"overflow-y\", \"auto\", \"position\", \"absolute\", \"z-index\", \"1000\"], [1, \"dropdown-item\", \"disabled\"], [4, \"ngIf\", \"ngIfElse\"], [\"type\", \"button\", \"id\", \"areaDropdownStep1\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"areaDropdownStep1\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"200px\", \"overflow-y\", \"auto\", \"position\", \"absolute\", \"z-index\", \"1000\"], [1, \"row\"], [\"type\", \"button\", \"id\", \"subAreaDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [2, \"color\", \"#000\"], [\"aria-labelledby\", \"subAreaDropdown\", 1, \"dropdown-menu\", \"w-100\", \"areas-dropdown\"], [1, \"areas-list\"], [\"class\", \"dropdown-item text-start text-dark\", \"style\", \"color: #090909 !important;\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"formControlName\", \"detailedAddress\", \"placeholder\", \"Enter detailed address\", 1, \"form-control\", \"text-start\"], [\"class\", \"mb-10\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"location\", \"placeholder\", \"Enter Google Maps link\", 1, \"form-control\", \"text-start\", 3, \"ngClass\"], [1, \"invalid-feedback\"], [1, \"text-primary\", \"mb-2\"], [1, \"text-danger\", \"mb-2\"], [\"style\", \"cursor: pointer\", 4, \"ngFor\", \"ngForOf\"], [2, \"cursor\", \"pointer\"], [1, \"dropdown-item\", \"text-start\", \"disabled\"], [\"class\", \"dropdown-item text-start\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"dropdown-item\", \"text-start\", \"text-dark\", 2, \"color\", \"#090909 !important\", 3, \"click\"], [\"type\", \"text\", \"formControlName\", \"mallName\", \"placeholder\", \"Enter mall name (optional)\", 1, \"form-control\", \"text-start\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"compoundName\", \"placeholder\", \"Enter compound name (optional)\", 1, \"form-control\", \"text-start\", 3, \"ngClass\"], [\"class\", \"col-md-6\", 4, \"ngIf\"], [\"class\", \"col-md-6 \", 4, \"ngIf\"], [\"class\", \"row mb-10\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"buildingNumber\", \"placeholder\", \"Enter property number\", 1, \"form-control\", \"text-start\"], [\"type\", \"text\", \"formControlName\", \"unitNumber\", \"placeholder\", \"Enter unit number\", 1, \"form-control\", \"text-start\"], [\"type\", \"button\", \"id\", \"floorDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"floorDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"number\", \"formControlName\", \"unitArea\", \"placeholder\", \"Enter area in square meters\", \"min\", \"0\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"buildingArea\", \"placeholder\", \"Enter building area in square meters\", \"min\", \"0\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"groundArea\", \"placeholder\", \"Enter ground area in square meters\", \"min\", \"0\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"numberOfRooms\", \"placeholder\", \"Enter number of rooms\", \"min\", \"0\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"numberOfBathrooms\", \"placeholder\", \"Enter number of bathrooms\", \"min\", \"0\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"numberOfFloors\", \"placeholder\", \"Enter number of floors\", \"min\", \"0\", 1, \"form-control\", \"text-start\"], [\"type\", \"button\", \"id\", \"apartmentLocationDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"apartmentLocationDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"formControlName\", \"unitDescription\", \"placeholder\", \"Enter unit description (optional)\", \"rows\", \"4\", 1, \"form-control\", \"text-start\", 3, \"ngClass\"], [1, \"form-text\", \"text-muted\"], [\"type\", \"button\", \"id\", \"buildingDeadlineDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"buildingDeadlineDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"apartmentViewDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"apartmentViewDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"finishingStatusDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"finishingStatusDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"fitOutConditionDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"fitOutConditionDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"furnishingStatusDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"furnishingStatusDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"groundLayoutStatusDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"groundLayoutStatusDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"unitDesignDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"unitDesignDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"activityDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"activityDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"deliveryStatusDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"deliveryStatusDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"date\", \"formControlName\", \"deliveryDate\", \"placeholder\", \"Select delivery date\", 1, \"form-control\", \"text-start\"], [\"type\", \"button\", \"id\", \"legalStatusDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"legalStatusDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"financialStatusDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"financialStatusDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"additionalAmenitiesDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"additionalAmenitiesDropdown\", 1, \"dropdown-menu\", \"w-100\", \"p-3\"], [1, \"mb-2\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"accessory_alltheabovearesuitable\", 1, \"form-check-input\", 3, \"change\", \"checked\"], [\"for\", \"accessory_alltheabovearesuitable\", 1, \"form-check-label\", \"text-start\"], [1, \"my-2\"], [\"class\", \"mb-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"form-check\", 4, \"ngIf\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"id\", \"checked\"], [1, \"form-check-label\", \"text-start\", 3, \"for\"], [\"type\", \"text\", \"formControlName\", \"requestedOver\", \"placeholder\", \"Enter requested over (optional)\", 1, \"form-control\", \"text-start\", 3, \"ngClass\"], [\"type\", \"button\", \"id\", \"paymentSystemDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"paymentSystemDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"number\", \"formControlName\", \"pricePerMeterInCash\", \"placeholder\", \"Enter price per meter in cash\", \"min\", \"0\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"totalPriceInCash\", \"placeholder\", \"Enter total price in cash\", \"min\", \"0\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"pricePerMeterInInstallment\", \"placeholder\", \"Enter price per meter in installment\", \"min\", \"0\", 1, \"form-control\", \"text-start\"], [\"type\", \"number\", \"formControlName\", \"totalPriceInInstallment\", \"placeholder\", \"Enter total price in installment\", \"min\", \"0\", 1, \"form-control\", \"text-start\"], [1, \"mb-10\", \"upload-card-container\"], [1, \"card\", \"mb-5\", \"cursor-pointer\"], [\"for\", \"projectUnitImage\", 1, \"card-body\", \"text-center\", \"py-3\"], [1, \"upload-icon\"], [1, \"fas\", \"fa-arrow-up\"], [1, \"upload-text\"], [\"class\", \"badge bg-success ms-2\", 4, \"ngIf\"], [\"type\", \"file\", \"id\", \"projectUnitImage\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [\"for\", \"projectLayout\", 1, \"card-body\", \"text-center\", \"py-3\"], [\"type\", \"file\", \"id\", \"projectLayout\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [\"for\", \"videos\", 1, \"card-body\", \"text-center\", \"py-3\"], [\"type\", \"file\", \"id\", \"videos\", \"accept\", \"video/*\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [\"for\", \"locationInMasterPlan\", 1, \"card-body\", \"text-center\", \"py-3\"], [\"type\", \"file\", \"id\", \"locationInMasterPlan\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [1, \"badge\", \"bg-success\", \"ms-2\"], [1, \"text-center\", \"py-5\"], [1, \"mb-4\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", 2, \"font-size\", \"3rem\"], [1, \"mb-3\"], [1, \"text-muted\", \"mb-4\"], [1, \"alert\", \"alert-info\", \"text-start\"], [1, \"list-unstyled\", \"mb-0\", \"mt-2\"], [1, \"d-flex\", \"flex-column\", \"gap-3\", \"mt-10\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"px-1\", \"py-2\", \"rounded-pill\", 3, \"click\", \"disabled\"], [1, \"indicator-label\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"px-1\", \"py-2\", \"rounded-pill\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-bullhorn\", \"me-2\"], [1, \"indicator-progress\"], [1, \"spinner-border\", \"spinner-border-sm\", \"align-middle\", \"ms-2\"], [1, \"d-flex\", \"justify-content-between\", \"pt-10\"], [\"type\", \"button\", 1, \"btn\", \"btn-light-dark\", \"btn-lg\", \"px-6\", \"py-3\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-lg\", \"btn-navy\", \"px-10\", \"py-3\", \"rounded-pill\", 3, \"click\", \"disabled\"], [1, \"indicator-label\", \"text-white\"], [1, \"d-flex\", \"justify-content-center\", \"pt-10\"]],\n    template: function AddPropertyComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵelement(1, \"app-broker-title\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5)(5, \"div\", 6);\n        i0.ɵɵtemplate(6, AddPropertyComponent_ng_container_6_Template, 6, 0, \"ng-container\", 7)(7, AddPropertyComponent_ng_container_7_Template, 6, 0, \"ng-container\", 7)(8, AddPropertyComponent_ng_container_8_Template, 6, 0, \"ng-container\", 7)(9, AddPropertyComponent_ng_container_9_Template, 6, 0, \"ng-container\", 7)(10, AddPropertyComponent_ng_container_10_Template, 6, 0, \"ng-container\", 7)(11, AddPropertyComponent_ng_container_11_Template, 6, 0, \"ng-container\", 7);\n        i0.ɵɵelementStart(12, \"div\", 8)(13, \"span\", 9);\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"span\", 10);\n        i0.ɵɵtext(16, \"of\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"span\", 11);\n        i0.ɵɵtext(18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(19, AddPropertyComponent_div_19_Template, 2, 0, \"div\", 12);\n        i0.ɵɵelementStart(20, \"div\", 13);\n        i0.ɵɵelement(21, \"div\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"form\", 15);\n        i0.ɵɵtemplate(23, AddPropertyComponent_div_23_Template, 32, 9, \"div\", 16)(24, AddPropertyComponent_div_24_Template, 64, 23, \"div\", 16)(25, AddPropertyComponent_div_25_Template, 29, 25, \"div\", 16)(26, AddPropertyComponent_div_26_Template, 5, 5, \"div\", 16)(27, AddPropertyComponent_div_27_Template, 34, 5, \"div\", 16)(28, AddPropertyComponent_div_28_Template, 29, 3, \"div\", 16)(29, AddPropertyComponent_div_29_Template, 6, 1, \"div\", 17)(30, AddPropertyComponent_div_30_Template, 4, 1, \"div\", 18)(31, AddPropertyComponent_div_31_Template, 6, 4, \"div\", 18);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"showCreateButton\", false);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 5);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\"Step \", ctx.currentStep, \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.totalSteps);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 0);\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleProp(\"width\", ctx.currentStep / ctx.totalSteps * 100 + \"%\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 5);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1 && ctx.currentStep !== ctx.totalSteps);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.NgForm, i1.FormGroupDirective, i1.FormControlName, i5.BrokerTitleComponent],\n    styles: [\".card[_ngcontent-%COMP%] {\\n  max-width: 550px;\\n  margin: 0 auto;\\n  border-radius: 8px;\\n  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e4e6ef;\\n}\\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.card.cursor-pointer[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.card.cursor-pointer[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.text-dark-blue[_ngcontent-%COMP%] {\\n  color: #0d6efd; \\n\\n}\\n\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  border-radius: 25px;\\n  border: 1px solid #e4e6ef;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  font-size: 1rem;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 80px;\\n  margin-bottom: 0;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background-color: #0d6efd;\\n  border-radius: 50%;\\n  margin-bottom: 10px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 16px;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-text[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n  font-weight: bold;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover {\\n  border-color: #0d6efd;\\n  box-shadow: 0 0 10px rgba(13, 110, 253, 0.1);\\n}\\n\\n.btn-dark-blue[_ngcontent-%COMP%] {\\n  background-color: #1e1e2d;\\n  color: #ffffff;\\n}\\n\\n.btn-navy[_ngcontent-%COMP%] {\\n  background-color: #1e1e7c;\\n  color: #ffffff;\\n  border: none;\\n}\\n.btn-navy[_ngcontent-%COMP%]:hover {\\n  background-color: #16165a;\\n}\\n.btn-navy[_ngcontent-%COMP%]:disabled {\\n  background-color: #9999c9;\\n}\\n\\n.progress[_ngcontent-%COMP%] {\\n  border-radius: 30px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  border-radius: 30px;\\n}\\n\\n.cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n}\\n\\n.form-select[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n}\\n\\n.dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  border: 1px solid #e4e6ef;\\n  background-color: #f5f8fa;\\n  color: #5e6278;\\n  padding: 0.75rem 1rem;\\n}\\n.dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover, .dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:focus {\\n  background-color: #f5f8fa;\\n  border-color: #e4e6ef;\\n}\\n.dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]::after {\\n  display: none;\\n}\\n.dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);\\n  padding: 0.5rem 0;\\n}\\n.dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.25rem;\\n  cursor: pointer;\\n}\\n.dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f8fa;\\n}\\n\\n.cities-dropdown[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n\\n.cities-list[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.cities-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n}\\n.cities-list[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.cities-list[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.cities-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.cities-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n.cities-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #888;\\n  border-radius: 3px;\\n}\\n.cities-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #555;\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  margin: 0;\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.areas-dropdown[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n\\n.areas-list[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.areas-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n}\\n.areas-list[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.areas-list[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.areas-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.areas-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n.areas-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #888;\\n  border-radius: 3px;\\n}\\n.areas-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #555;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AddPropertyComponent_div_19_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "prevStep", "AddPropertyComponent_div_23_li_10_Template_a_click_1_listener", "option_r4", "_r3", "$implicit", "selectStep0Value", "value", "ɵɵadvance", "ɵɵtextInterpolate", "key", "AddPropertyComponent_div_23_li_20_Template_a_click_1_listener", "option_r6", "_r5", "AddPropertyComponent_div_23_li_30_Template_a_click_1_listener", "unitType_r8", "_r7", "ɵɵelement", "ɵɵtemplate", "AddPropertyComponent_div_23_li_10_Template", "AddPropertyComponent_div_23_li_20_Template", "AddPropertyComponent_div_23_li_30_Template", "AddPropertyComponent_div_23_small_31_Template", "ɵɵproperty", "step0Form", "getCompoundTypeText", "tmp_2_0", "get", "compoundOptions", "formatUnitTypeKey", "tmp_4_0", "filterPropertyTypes", "filteredUnitTypes", "length", "tmp_7_0", "AddPropertyComponent_div_24_div_10_div_1_Template", "AddPropertyComponent_div_24_div_10_div_2_Template", "tmp_3_0", "step1Form", "errors", "ɵɵtextInterpolate1", "selectedCityName", "AddPropertyComponent_div_24_ng_container_26_li_1_Template_a_click_1_listener", "city_r10", "_r9", "selectCity", "id", "name_en", "AddPropertyComponent_div_24_ng_container_26_li_1_Template", "cities", "AddPropertyComponent_div_24_li_38_a_1_Template_a_click_0_listener", "area_r12", "_r11", "selectArea", "AddPropertyComponent_div_24_li_38_a_1_Template", "areas", "AddPropertyComponent_div_24_a_51_Template_a_click_0_listener", "subArea_r14", "_r13", "selectSubArea", "AddPropertyComponent_div_24_div_57_div_4_div_1_Template", "AddPropertyComponent_div_24_div_57_div_4_Template", "ɵɵpureFunction1", "_c0", "invalid", "touched", "dirty", "AddPropertyComponent_div_24_div_58_div_4_div_1_Template", "AddPropertyComponent_div_24_div_58_div_4_Template", "AddPropertyComponent_div_24_div_63_div_1_Template", "AddPropertyComponent_div_24_div_63_div_2_Template", "AddPropertyComponent_div_24_div_10_Template", "AddPropertyComponent_div_24_div_15_Template", "AddPropertyComponent_div_24_div_16_Template", "AddPropertyComponent_div_24_ng_container_20_Template", "AddPropertyComponent_div_24_ng_container_21_Template", "AddPropertyComponent_div_24_ng_container_26_Template", "AddPropertyComponent_div_24_ng_template_27_Template", "ɵɵtemplateRefExtractor", "AddPropertyComponent_div_24_li_38_Template", "AddPropertyComponent_div_24_a_51_Template", "AddPropertyComponent_div_24_div_57_Template", "AddPropertyComponent_div_24_div_58_Template", "AddPropertyComponent_div_24_div_63_Template", "isLoadingCities", "noCities_r15", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedSubAreaName", "subAreas", "shouldShowField", "tmp_19_0", "tmp_20_0", "AddPropertyComponent_div_25_div_5_li_9_Template_a_click_1_listener", "floor_r17", "_r16", "selectStep2Value", "AddPropertyComponent_div_25_div_5_li_9_Template", "step2Form", "floorTypes", "AddPropertyComponent_div_25_div_14_Template_a_click_10_listener", "_r18", "AddPropertyComponent_div_25_div_14_Template_a_click_13_listener", "AddPropertyComponent_div_25_div_14_Template_a_click_16_listener", "AddPropertyComponent_div_25_div_14_Template_a_click_19_listener", "AddPropertyComponent_div_25_div_15_div_5_div_1_Template", "AddPropertyComponent_div_25_div_15_div_5_Template", "AddPropertyComponent_div_25_div_16_li_9_Template_a_click_1_listener", "deadline_r20", "_r19", "AddPropertyComponent_div_25_div_16_li_9_Template", "buildingDeadlineTypes", "AddPropertyComponent_div_25_div_17_li_9_Template_a_click_1_listener", "view_r22", "_r21", "AddPropertyComponent_div_25_div_17_li_9_Template", "viewTypes", "AddPropertyComponent_div_25_div_18_li_9_Template_a_click_1_listener", "status_r24", "_r23", "AddPropertyComponent_div_25_div_18_li_9_Template", "finishingType", "AddPropertyComponent_div_25_div_19_li_9_Template_a_click_1_listener", "condition_r26", "_r25", "AddPropertyComponent_div_25_div_19_li_9_Template", "fitOutConditionTypes", "AddPropertyComponent_div_25_div_20_li_9_Template_a_click_1_listener", "furnishing_r28", "_r27", "AddPropertyComponent_div_25_div_20_li_9_Template", "furnishingStatusTypes", "AddPropertyComponent_div_25_div_21_li_9_Template_a_click_1_listener", "layout_r30", "_r29", "AddPropertyComponent_div_25_div_21_li_9_Template", "groundLayoutStatusTypes", "AddPropertyComponent_div_25_div_22_li_9_Template_a_click_1_listener", "design_r32", "_r31", "AddPropertyComponent_div_25_div_22_li_9_Template", "unitDesignTypes", "AddPropertyComponent_div_25_div_23_li_9_Template_a_click_1_listener", "activity_r34", "_r33", "AddPropertyComponent_div_25_div_23_li_9_Template", "activityTypes", "AddPropertyComponent_div_25_div_24_li_9_Template_a_click_1_listener", "delivery_r36", "_r35", "AddPropertyComponent_div_25_div_24_li_9_Template", "deliveryTypes", "AddPropertyComponent_div_25_div_26_li_9_Template_a_click_1_listener", "legal_r38", "_r37", "AddPropertyComponent_div_25_div_26_li_9_Template", "legalTypes", "AddPropertyComponent_div_25_div_27_li_9_Template_a_click_1_listener", "financial_r40", "_r39", "AddPropertyComponent_div_25_div_27_li_9_Template", "financialStatusTypes", "AddPropertyComponent_div_25_div_28_li_15_div_1_Template_input_change_1_listener", "_r42", "accessory_r43", "toggleAccessory", "isAccessorySelected", "AddPropertyComponent_div_25_div_28_li_15_div_1_Template", "AddPropertyComponent_div_25_div_28_Template_input_change_11_listener", "$event", "_r41", "onAllAccessoriesChange", "AddPropertyComponent_div_25_div_28_li_15_Template", "getSelectedAccessoriesText", "otherAccessoriesTypes", "AddPropertyComponent_div_25_div_2_Template", "AddPropertyComponent_div_25_div_3_Template", "AddPropertyComponent_div_25_div_5_Template", "AddPropertyComponent_div_25_div_6_Template", "AddPropertyComponent_div_25_div_8_Template", "AddPropertyComponent_div_25_div_9_Template", "AddPropertyComponent_div_25_div_11_Template", "AddPropertyComponent_div_25_div_12_Template", "AddPropertyComponent_div_25_div_13_Template", "AddPropertyComponent_div_25_div_14_Template", "AddPropertyComponent_div_25_div_15_Template", "AddPropertyComponent_div_25_div_16_Template", "AddPropertyComponent_div_25_div_17_Template", "AddPropertyComponent_div_25_div_18_Template", "AddPropertyComponent_div_25_div_19_Template", "AddPropertyComponent_div_25_div_20_Template", "AddPropertyComponent_div_25_div_21_Template", "AddPropertyComponent_div_25_div_22_Template", "AddPropertyComponent_div_25_div_23_Template", "AddPropertyComponent_div_25_div_24_Template", "AddPropertyComponent_div_25_div_25_Template", "AddPropertyComponent_div_25_div_26_Template", "AddPropertyComponent_div_25_div_27_Template", "AddPropertyComponent_div_25_div_28_Template", "shouldShowFurnishingStatusField", "shouldShowGroundLayoutStatusField", "shouldShowUnitDesignField", "AddPropertyComponent_div_26_div_1_div_4_div_1_Template", "step3Form", "AddPropertyComponent_div_26_div_1_div_4_Template", "AddPropertyComponent_div_26_div_2_li_9_Template_a_click_1_listener", "payment_r45", "_r44", "selectStep3Value", "AddPropertyComponent_div_26_div_2_li_9_Template", "paymentTypes", "AddPropertyComponent_div_26_ng_container_4_div_5_Template", "AddPropertyComponent_div_26_div_1_Template", "AddPropertyComponent_div_26_div_2_Template", "AddPropertyComponent_div_26_ng_container_3_Template", "AddPropertyComponent_div_26_ng_container_4_Template", "shouldShow<PERSON>ash<PERSON>ields", "shouldShowInstallmentFields", "getFileCount", "AddPropertyComponent_div_27_span_8_Template", "AddPropertyComponent_div_27_Template_input_change_9_listener", "_r46", "onFileChange", "AddPropertyComponent_div_27_span_16_Template", "AddPropertyComponent_div_27_Template_input_change_17_listener", "AddPropertyComponent_div_27_span_24_Template", "AddPropertyComponent_div_27_Template_input_change_25_listener", "AddPropertyComponent_div_27_span_32_Template", "AddPropertyComponent_div_27_Template_input_change_33_listener", "step4Form", "AddPropertyComponent_div_28_Template_button_click_19_listener", "_r47", "submitForm", "AddPropertyComponent_div_28_Template_button_click_22_listener", "step5Form", "isCurrentFormValid", "valid", "AddPropertyComponent_div_29_Template_button_click_1_listener", "_r48", "cancel", "AddPropertyComponent_div_29_Template_button_click_3_listener", "nextStep", "AddPropertyComponent_div_30_Template_button_click_1_listener", "_r49", "AddPropertyComponent_div_31_Template_button_click_1_listener", "_r50", "AddPropertyComponent_div_31_ng_container_3_Template", "AddPropertyComponent_div_31_ng_container_4_Template", "AddPropertyComponent_div_31_ng_container_5_Template", "currentStep", "AddPropertyComponent", "fb", "router", "propertyService", "cdr", "totalSteps", "selectedCityId", "selectedUnitType", "unitTypes", "otherAccessoriesList", "brokerId", "selectedAccessories", "propertyTypeOptions", "allUnitTypes", "outsideCompoundUnitTypes", "insideCompoundUnitTypes", "RentalUnitTypes", "constructor", "ngOnInit", "user", "localStorage", "getItem", "JSON", "parse", "initForms", "loadUnitTypes", "loadCities", "loadAreas", "group", "compoundType", "required", "propertyType", "type", "cityId", "areaId", "subAreaId", "mallName", "max<PERSON><PERSON><PERSON>", "compoundName", "detailed<PERSON>ddress", "location", "pattern", "ownerName", "ownerPhone", "buildingNumber", "unitNumber", "floor", "unitArea", "min", "buildingArea", "groundArea", "numberOfRooms", "numberOfBathrooms", "numberOfFloors", "unitFacing", "view", "fitOutCondition", "furnishingStatus", "groundLayoutStatus", "unitDesign", "activity", "deliveryStatus", "deliveryDate", "buildingDeadline", "financialStatus", "otherAccessories", "legalStatus", "unitDescription", "requestedOver", "paymentSystem", "pricePerMeterInInstallment", "totalPriceInInstallment", "pricePerMeterInCash", "totalPriceInCash", "diagram", "layout", "videos", "locationInMasterPlan", "getCurrentForm", "getUnitTypes", "subscribe", "next", "response", "Object", "entries", "data", "map", "console", "log", "error", "err", "complete", "detectChanges", "filterUnitTypes", "patchValue", "getCities", "warn", "<PERSON><PERSON><PERSON><PERSON>", "loadSubAreas", "gitsub<PERSON><PERSON>s", "filter", "option", "getFieldsToShow", "fieldName", "includes", "cityName", "selectUnitType", "UnitValue", "unitTypesWithFacing", "unitTypesWithAreaFields", "unitTypesWithActivity", "unitTypesWithGroundLayout", "unitTypesWithUnitDesign", "unitTypesWithFitOutCondition", "unitTypesToHideFurnishing", "unitTypesWithLegalStatus", "areaName", "subAreaName", "clear<PERSON>rice<PERSON><PERSON>s", "shouldShowUnitFacingField", "unitType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "shouldShowActivityField", "shouldShowLegalStatusField", "shouldShowFitOutConditionField", "selectStep5Value", "checkAd", "_this", "httpFormData", "FormData", "keys", "for<PERSON>ach", "append", "additionalDetailsFields", "push", "additionalDetails", "field", "fileFields", "files", "isMultiple", "file", "accessoriesRaw", "accessoriesArray", "Array", "isArray", "accessory", "index", "toString", "button", "document", "querySelector", "classList", "add", "createProperty", "_ref", "_asyncToGenerator", "fire", "status", "navigate", "queryParams", "success", "_x", "apply", "arguments", "message", "remove", "event", "target", "from", "currentForm", "<PERSON><PERSON><PERSON><PERSON>", "isStep2FormValid", "form", "fieldsToShow", "requiredFields", "control", "conditionalFields", "isFieldRequiredForUnitType", "split", "word", "trim", "toUpperCase", "slice", "toLowerCase", "join", "indexOf", "splice", "checked", "a", "find", "opt", "getUnitTypeText", "unit", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "PropertyService", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "AddPropertyComponent_Template", "rf", "ctx", "AddPropertyComponent_ng_container_6_Template", "AddPropertyComponent_ng_container_7_Template", "AddPropertyComponent_ng_container_8_Template", "AddPropertyComponent_ng_container_9_Template", "AddPropertyComponent_ng_container_10_Template", "AddPropertyComponent_ng_container_11_Template", "AddPropertyComponent_div_19_Template", "AddPropertyComponent_div_23_Template", "AddPropertyComponent_div_24_Template", "AddPropertyComponent_div_25_Template", "AddPropertyComponent_div_26_Template", "AddPropertyComponent_div_27_Template", "AddPropertyComponent_div_28_Template", "AddPropertyComponent_div_29_Template", "AddPropertyComponent_div_30_Template", "AddPropertyComponent_div_31_Template", "ɵɵstyleProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\add-property\\add-property.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\add-property\\add-property.component.html"], "sourcesContent": ["import { Component, OnInit, ChangeDetectorRef, Type } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { PropertyService } from '../services/property.service';\r\nimport Swal from 'sweetalert2';\r\n@Component({\r\n  selector: 'app-add-property',\r\n  templateUrl: './add-property.component.html',\r\n  styleUrl: './add-property.component.scss',\r\n})\r\n\r\n\r\n/*\r\n\r\n    {  key: 'I Villa', value: 'i_villa' },\r\n    { key: 'Residential Buildings', value: 'residential_buildings' },\r\n    { key: 'Mixed Housings', value: ' mixed_housings' },\r\n    { key: 'cooperatives', value: ' cooperatives' },\r\n    { key: 'youth units', value: 'youth_units' },\r\n    { key: 'ganat misr', value: 'ganat_misr' },\r\n    { key: 'dar misr', value: 'dar_misr' },\r\n    { key: 'sakan misr', value: 'sakan_misr' },\r\n    { key: 'industrial_lands', value: 'industrial_lands' },\r\n    { key: ' cabin', value: ' cabin' },\r\n    { key: 'vacation villa', value: ' vacation_villa' },\r\n\r\n\r\n{ key: 'Hotels', value: 'hotels' },\r\n\r\n\r\n    { key: 'Commercial Administrative Buildings', value: 'commercial_administrative_buildings' },\r\n    { key: 'Commercial Stores', value: 'commercial_stores' },\r\n    { key: 'commercial_units', value: ' commercial_units' },\r\n    { key: 'shops', value: 'shops' },\r\n\r\n\r\n\r\n*/\r\nexport class AddPropertyComponent implements OnInit {\r\n  totalSteps = 5;\r\n  currentStep = 0;\r\n  selectedCityId: any;\r\n  selectedCityName: string;\r\n  selectedAreaName: string;\r\n  selectedSubAreaName: string;\r\n  selectedUnitType: string;\r\n  cities: any[] = [];\r\n  unitTypes: { key: string; value: string }[] = [];\r\n  areas: any[] = [];\r\n  subAreas: any[] = [];\r\n  isLoadingCities = false;\r\n\r\n  otherAccessoriesList = [\r\n  { key: 'GARAGE', value: 'garage' },\r\n  { key: 'CLUBHOUSE', value: 'clubhouse' },\r\n  { key: 'CLUB', value: 'club' },\r\n  { key: 'STORAGE', value: 'storage' },\r\n  { key: 'ELEVATOR', value: 'elevator' },\r\n  { key: 'SWIMMING POOL', value: 'swimming_pool' },\r\n  { key: 'ALL THE ABOVE', value: 'all_the_above_are_suitable' }\r\n];\r\n\r\n\r\n  //get brokerId from session\r\n  brokerId: number;\r\n\r\n  finishingType: { key: string; value: string }[] = [\r\n    { key: 'On Brick', value: 'on_brick' },\r\n    { key: 'Semi Finished', value: 'semi_finished' },\r\n    { key: 'Company Finished', value: 'company_finished' },\r\n    { key: 'Super Lux', value: 'super_lux' },\r\n    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },\r\n  ];\r\n\r\n  floorTypes: { key: string; value: string }[] = [\r\n    { key: 'Ground', value: 'ground' },\r\n    { key: 'Last Floor', value: 'last_floor' },\r\n    { key: 'Repeated', value: 'repeated' },\r\n    { key: 'All Of The Above', value: 'all_the_above_are_suitable' },\r\n  ];\r\n\r\n  viewTypes: { key: string; value: string }[] = [\r\n    { key: 'Water View', value: 'water_view' },\r\n    { key: 'Gardens And Landscape', value: 'gardens_and_landscape' },\r\n    { key: 'Street', value: 'street' },\r\n    { key: 'Entertainment Area', value: 'entertainment_area' },\r\n    { key: 'Garden  ', value: 'garden' },\r\n    { key: 'Main Street', value: ' main_street' },\r\n    { key: 'Square', value: 'square' },\r\n    { key: 'Side Street', value: 'side_street' },\r\n    { key: 'Rear View', value: 'rear_view' },\r\n  ];\r\n\r\n  deliveryTypes: { key: string; value: string }[] = [\r\n    { key: 'Immediate Delivery', value: 'immediate_delivery' },\r\n    { key: 'Under Construction', value: 'under_construction' },\r\n  ];\r\n\r\n  activityTypes: { key: string; value: string }[] = [\r\n    { key: 'Administrative Only', value: 'administrative_only' },\r\n    { key: 'Commercial Only', value: 'commercial_only' },\r\n    { key: 'Medical Only', value: 'medical_only' },\r\n    {\r\n      key: 'Administrative And Commercial',\r\n      value: 'administrative_and_commercial',\r\n    },\r\n    {\r\n      key: 'Administrative Commercial And Medical',\r\n      value: 'administrative_commercial_and_medical',\r\n    },\r\n  ];\r\n\r\n  fitOutConditionTypes: { key: string; value: string }[] = [\r\n    { key: 'Unfitted', value: 'unfitted' },\r\n    { key: 'Fully Fitted', value: 'fully_fitted' },\r\n    { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },\r\n  ];\r\n\r\n  furnishingStatusTypes: { key: string; value: string }[] = [\r\n    { key: 'Unfurnished', value: 'unfurnished' },\r\n    {\r\n      key: 'Furnished With Air Conditioners',\r\n      value: 'furnished_with_air_conditioners',\r\n    },\r\n    {\r\n      key: 'Furnished Without Air Conditioners',\r\n      value: 'furnished_without_air_conditioners',\r\n    },\r\n  ];\r\n\r\n  groundLayoutStatusTypes: { key: string; value: string }[] = [\r\n    { key: 'Vacant Land', value: 'vacant_land' },\r\n    { key: 'Under Construction', value: 'under_construction' },\r\n    { key: 'Fully Built', value: 'fully_built' },\r\n    { key: 'All Acceptable', value: 'all_acceptable' },\r\n  ];\r\n\r\n  unitDesignTypes: { key: string; value: string }[] = [\r\n    { key: 'Custom Design', value: 'custom_design' },\r\n    { key: 'One Apartment Per Floor', value: 'one_apartment_per_floor' },\r\n    { key: 'Two Apartments Per Floor', value: 'two_apartments_per_floor' },\r\n    {\r\n      key: 'More Than Two Apartments Per Floor',\r\n      value: 'more_than_two_apartments_per_floor',\r\n    },\r\n    { key: 'All Acceptable', value: 'all_acceptable' },\r\n  ];\r\n\r\n  otherAccessoriesTypes: { key: string; value: string }[] = [\r\n    { key: 'Garage', value: 'garage' },\r\n    { key: 'Clubhouse', value: 'clubhouse' },\r\n    { key: 'Club', value: 'club' },\r\n    { key: 'Storage', value: 'storage' },\r\n    { key: 'Elevator', value: 'elevator' },\r\n    { key: 'Swimming Pool', value: 'swimming_pool' },\r\n  ];\r\n\r\n  selectedAccessories: string[] = [];\r\n\r\n  paymentTypes: { key: string; value: string }[] = [\r\n    { key: 'Cash', value: 'cash' },\r\n    { key: 'Installment', value: 'installment' },\r\n    {\r\n      key: 'All Of The Above Are Suitable ',\r\n      value: 'all_of_the_above_are_suitable',\r\n    },\r\n  ];\r\n\r\n  legalTypes: { key: string; value: string }[] = [\r\n    { key: 'Licensed', value: 'licensed' },\r\n    { key: 'Reconciled', value: 'reconciled' },\r\n    { key: 'Reconciliation Required', value: 'reconciliation_required' },\r\n  ];\r\n\r\n  financialStatusTypes: { key: string; value: string }[] = [\r\n    { key: 'paid_in_full ', value: 'paid_in_full' },\r\n    { key: 'partially_paid_with_remaining_installments ', value: 'partially_paid_with_remaining_installments' },\r\n\r\n  ];\r\n\r\n  buildingDeadlineTypes: { key: string; value: string }[] = [\r\n    { key: 'Grace Period Allowed', value: 'grace_period_allowed' },\r\n    { key: 'No Grace Period', value: 'no_grace_period' },\r\n  ];\r\n\r\n  // Step 0 options\r\n  compoundOptions: { key: string; value: string }[] = [\r\n    { key: 'Outside Compound', value: 'outside_compound' },\r\n    { key: 'Inside Compound', value: 'inside_compound' },\r\n    { key: 'village', value: 'village'},\r\n  ];\r\n\r\n  propertyTypeOptions: { key: string; value: string }[] = [\r\n    { key: 'Sale', value: 'sale' },\r\n    { key: 'Rent', value: 'rent_out' },\r\n  ];\r\n\r\n\r\n  // All unit types for filtering\r\n  allUnitTypes: { key: string; value: string }[] = [];\r\n\r\n  // Unit types for outside compound\r\n  outsideCompoundUnitTypes: { key: string; value: string }[] = [\r\n    // Residential\r\n    { key: 'Apartments', value: 'apartments' },\r\n    { key: 'Duplexes', value: 'duplexes' },\r\n    { key: 'Studios', value: 'studios' },\r\n    { key: 'Penthouses', value: 'penthouses' },\r\n    { key: 'Basement', value: 'basement' },\r\n    { key: 'Roofs', value: 'roofs' },\r\n\r\n    // Villas\r\n    { key: 'Villas', value: 'villas' },\r\n    { key: 'Full Buildings', value: 'full_buildings' },\r\n\r\n\r\n    // Commercial/Administrative\r\n    { key: 'Administrative Units', value: 'administrative_units' },\r\n    { key: 'Medical Clinics', value: 'medical_clinics' },\r\n    { key: 'Commercial Stores', value: 'commercial_stores' },\r\n    { key: 'Pharmacies', value: 'pharmacies' },\r\n\r\n\r\n    // Industrial\r\n    { key: 'Warehouses', value: 'warehouses' },\r\n    { key: 'Factories', value: 'factories' },\r\n\r\n\r\n    // Lands\r\n    { key: 'Residential Villa Lands', value: 'residential_villa_lands' },\r\n    { key: 'Administrative lands', value: 'administrative_lands' },\r\n    { key: 'Residential Lands', value: 'residential_lands' },\r\n    { key: 'Commercial Administrative Lands', value: 'commercial_administrative_lands' },\r\n    { key: 'Medical Lands', value: 'medical_lands' },\r\n    { key: 'Mixed Lands', value: 'mixed_lands' },\r\n    { key: 'Warehouses Land', value: 'warehouses_land' },\r\n    { key: 'Factory Lands', value: 'factory_lands' },\r\n  ];\r\n\r\n   insideCompoundUnitTypes: { key: string; value: string }[] = [\r\n    // Residential\r\n    { key: 'Apartments', value: 'apartments' },\r\n    { key: 'Duplexes', value: 'duplexes' },\r\n    { key: 'Studios', value: 'studios' },\r\n    { key: 'Penthouses', value: 'penthouses' },\r\n    { key: 'I Villa', value: 'i_villa' },\r\n\r\n    //villas\r\n    { key: 'Standalone Villas', value: 'standalone_villas' },\r\n    { key: 'Town Houses', value: 'town_houses' },\r\n    { key: 'Twin Houses', value: 'twin_houses' },\r\n\r\n\r\n    // // Commercial/Administrative\r\n    // { key: 'Administrative Units', value: 'administrative_units' },\r\n    // { key: 'Medical Clinics', value: 'medical_clinics' },\r\n    // { key: 'Commercial Stores', value: 'commercial_stores' },\r\n    // { key: 'Pharmacies', value: 'pharmacies' },\r\n\r\n\r\n\r\n   ];\r\n   RentalUnitTypes: { key: string; value: string }[] = [\r\n    // Residential\r\n      { key: 'Apartments', value: 'apartments' },\r\n      { key: 'Duplexes', value: 'duplexes' },\r\n      { key: 'Studios', value: 'studios' },\r\n      { key: 'Penthouses', value: 'penthouses' },\r\n      { key: 'Basement', value: 'basement' },\r\n      { key: 'Roofs', value: 'roofs' },\r\n\r\n      // villas\r\n      { key: 'I Villa', value: 'i_villa' },\r\n      { key: 'Twin Houses', value: 'twin_houses' },\r\n      { key: 'Town Houses', value: 'town_houses' },\r\n      { key: 'Standalone Villas', value: 'standalone_villas' },\r\n      { key: 'Villas', value: 'villas' },\r\n      { key: 'Full Buildings', value: 'full_buildings' },\r\n\r\n      // Commercial/Administrative\r\n      { key: 'Administrative Units', value: 'administrative_units' },\r\n      { key: 'Medical Clinics', value: 'medical_clinics' },\r\n      { key: 'Commercial Stores', value: 'commercial_stores' },\r\n      { key: 'Pharmacies', value: 'pharmacies' },\r\n\r\n      //other\r\n      { key: 'chalets', value: 'chalets' },\r\n\r\n     // Industrial\r\n      { key: 'Warehouses', value: 'warehouses' },\r\n      { key: 'Factories', value: 'factories' },\r\n\r\n   ];\r\n\r\n\r\n  // Filtered unit types based on compound selection\r\n  filteredUnitTypes: { key: string; value: string }[] = [];\r\n\r\n  step0Form: FormGroup;\r\n  step1Form: FormGroup;\r\n  step2Form: FormGroup;\r\n  step3Form: FormGroup;\r\n  step4Form: FormGroup;\r\n  step5Form: FormGroup;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private propertyService: PropertyService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const user = localStorage.getItem('currentUser');\r\n    this.brokerId = user ? JSON.parse(user).brokerId : null;\r\n    this.initForms();\r\n    this.loadUnitTypes();\r\n    this.loadCities();\r\n    this.loadAreas();\r\n\r\n     this.filteredUnitTypes = [];\r\n  }\r\n\r\n  initForms() {\r\n    // Step 0: Property Category Selection\r\n    this.step0Form = this.fb.group({\r\n      compoundType: ['', [Validators.required]],\r\n      propertyType: ['', [Validators.required]],\r\n      type: ['', [Validators.required]],\r\n    });\r\n\r\n    // Step 1: Basic Property Settings\r\n    this.step1Form = this.fb.group({\r\n      cityId: ['', [Validators.required]],\r\n      areaId: ['', [Validators.required]],\r\n      subAreaId: [''], // Sub area field (optional)\r\n      mallName: ['', [Validators.maxLength(255)]],\r\n      compoundName: ['', [Validators.maxLength(255)]],\r\n      detailedAddress: ['', [Validators.required, Validators.maxLength(255)]],\r\n      location: ['', [ Validators.pattern('https?://.+')]],\r\n      ownerName: ['', Validators.required],\r\n      ownerPhone: [\r\n        '',\r\n        [Validators.required, Validators.pattern('^01[0-2,5]{1}[0-9]{8}$')],\r\n      ],\r\n    });\r\n\r\n    // Step 2: Unit Information\r\n    this.step2Form = this.fb.group({\r\n      buildingNumber: ['', [Validators.maxLength(50)]],\r\n      unitNumber: ['', [Validators.maxLength(50)]],\r\n      floor: ['', [Validators.required]],\r\n      unitArea: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(1),\r\n          Validators.pattern('^[0-9]*$'),\r\n        ],\r\n      ],\r\n      buildingArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],\r\n      groundArea: ['', [Validators.min(0), Validators.pattern('^[0-9]*$')]],\r\n      numberOfRooms: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]*$'),\r\n        ],\r\n      ],\r\n      numberOfBathrooms: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]*$'),\r\n        ],\r\n      ],\r\n      numberOfFloors: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.min(0),\r\n          Validators.pattern('^[0-9]*$'),\r\n        ],\r\n      ],\r\n      unitFacing: [''],\r\n      view: ['', [Validators.required]],\r\n      finishingType: ['', [Validators.required]],\r\n      fitOutCondition: [''],\r\n      furnishingStatus: [''],\r\n      groundLayoutStatus: [''],\r\n      unitDesign: [''],\r\n      activity: [''],\r\n      deliveryStatus: ['', [Validators.required]],\r\n      deliveryDate: [''],\r\n      buildingDeadline: [''], // Building deadline field\r\n      financialStatus: [''], // Financial status field\r\n      otherAccessories: [''], // Optional field - no validators to avoid blocking navigation\r\n      legalStatus: [''], // Moved from step5Form\r\n      unitDescription: ['', [Validators.maxLength(1000)]], // Unit description field\r\n    });\r\n\r\n    // Step 3: Financial Information\r\n    this.step3Form = this.fb.group({\r\n      requestedOver: ['', [Validators.maxLength(255)]], // Requested over field (optional)\r\n      paymentSystem: ['', Validators.required],\r\n      pricePerMeterInInstallment: ['', [Validators.min(0)]],\r\n      totalPriceInInstallment: ['', [Validators.min(0)]],\r\n      pricePerMeterInCash: ['', [Validators.min(0)]],\r\n      totalPriceInCash: ['', [Validators.min(0)]],\r\n    });\r\n\r\n    // Step 4: Project Documents\r\n    this.step4Form = this.fb.group({\r\n      diagram: [[]],\r\n      layout: [[]],\r\n      videos: [[]],\r\n      locationInMasterPlan: [[]],\r\n    });\r\n\r\n    // Step 5: Owner Information\r\n    this.step5Form = this.fb.group({\r\n      // legalStatus moved to step2Form\r\n    });\r\n  }\r\n\r\n  // Get current form based on step\r\n  getCurrentForm(): FormGroup {\r\n    switch (this.currentStep) {\r\n      case 0:\r\n        return this.step0Form;\r\n      case 1:\r\n        return this.step1Form;\r\n      case 2:\r\n        return this.step2Form;\r\n      case 3:\r\n        return this.step3Form;\r\n      case 4:\r\n        return this.step4Form;\r\n      case 5:\r\n        return this.step5Form;\r\n      default:\r\n        return this.step0Form;\r\n    }\r\n  }\r\n\r\n  loadUnitTypes(): void {\r\n    this.propertyService.getUnitTypes().subscribe({\r\n      next: (response) => {\r\n        this.allUnitTypes = Object.entries(response.data).map(([key, value]) => ({\r\n          key,\r\n          value: value as string,\r\n        }));\r\n        this.insideCompoundUnitTypes = this.allUnitTypes; // API data for inside compound\r\n        console.log('Raw API Response:', this.allUnitTypes);\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading unitTypes:', err);\r\n      },\r\n      complete: () => {\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  filterUnitTypes(): void {\r\n    const compoundType = this.step0Form.get('compoundType')?.value;\r\n\r\n    if (compoundType === 'outside_compound') {\r\n      this.filteredUnitTypes = this.outsideCompoundUnitTypes;\r\n    } else if (compoundType === 'inside_compound') {\r\n      this.filteredUnitTypes = this.insideCompoundUnitTypes;\r\n    } else if (compoundType === 'village') {\r\n      // Use property type options for village\r\n      this.filteredUnitTypes = this.RentalUnitTypes;\r\n    } else {\r\n      this.filteredUnitTypes = [];\r\n    }\r\n\r\n    this.step0Form.patchValue({ type: '' });\r\n    this.selectedUnitType = '';\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  loadCities(): void {\r\n    this.isLoadingCities = true;\r\n    this.propertyService.getCities().subscribe({\r\n      next: (response) => {\r\n        if (response && response.data) {\r\n          this.cities = response.data;\r\n        } else {\r\n          console.warn('No cities data in response');\r\n          this.cities = [];\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading cities:', err);\r\n      },\r\n      complete: () => {\r\n        this.isLoadingCities = false;\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  loadAreas(cityId?: number): void {\r\n    this.propertyService.getAreas(cityId).subscribe({\r\n      next: (response) => {\r\n        if (response && response.data) {\r\n          this.areas = response.data;\r\n        } else {\r\n          console.warn('No areas data in response');\r\n          this.areas = [];\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading areas:', err);\r\n        this.areas = [];\r\n      },\r\n      complete: () => {\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  loadSubAreas(areaId?: number): void {\r\n    this.propertyService.gitsubAreas(areaId).subscribe({\r\n      next: (response) => {\r\n        if (response && response.data) {\r\n          this.subAreas = response.data;\r\n        } else {\r\n          console.warn('No sub-areas data in response');\r\n          this.subAreas = [];\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading sub-areas:', err);\r\n        this.subAreas = [];\r\n      },\r\n      complete: () => {\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  //**************************************************************** */\r\n\r\n  // STEP 0\r\n  filterPropertyTypes(): { key: string; value: string }[] {\r\n  const compoundType = this.step0Form.get('compoundType')?.value;\r\n\r\n  if (compoundType === 'village') {\r\n    // Only show 'Rent' option for village compound type\r\n    return this.propertyTypeOptions.filter(option => option.value === 'rent_out');\r\n  }\r\n\r\n  // Show all property type options for other compound types\r\n  return this.propertyTypeOptions;\r\n}\r\n\r\n\r\n  //******************** */\r\n\r\n// STEP 2\r\ngetFieldsToShow(): any[] {\r\n  const compoundType = this.step0Form.get('compoundType')?.value;\r\n  const type = this.step0Form.get('type')?.value;\r\nconsole.log(compoundType, type);\r\nconsole.log(compoundType === 'outside_compound' &&  (type == 'villas' || type == 'full_buildings') );\r\n  // For outside compound apartments\r\n  if (compoundType === 'outside_compound' &&  (type === 'apartments' ||type === 'duplexes' || type === 'studios' || type === 'penthouses'|| type === 'roofs'|| type === 'basement')) {\r\n    return [ 'buildingNumber', 'unitNumber', 'floor', 'unitArea' ,'numberOfRooms' , 'numberOfBathrooms',  'unitFacing', 'view', 'finishingType','deliveryStatus',   'legalStatus', 'otherAccessories' ,   'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];\r\n  }\r\n  else if (compoundType === 'outside_compound' &&  (type == 'villas' || type == 'full_buildings')) {\r\n    return ['buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n  }\r\n  else if (compoundType === 'outside_compound' &&  (type === 'pharmacies' ||type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores' )) {\r\n   return ['mallName',  'buildingNumber', 'unitNumber', 'floor', 'unitArea' ,   'view', 'finishingType', 'fitOutCondition','deliveryStatus','activity' ,'financialStatus', 'otherAccessories' , 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];\r\n  }\r\n  else if (compoundType === 'outside_compound' &&  (type === 'warehouses' || type === 'factories' )) {\r\n  return ['buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n  }\r\n  else if (compoundType === 'outside_compound' &&  (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' ||type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands' )) {\r\n  return [ 'unitNumber','groundArea','fitOutCondition', 'unitDescription','buildingDeadline','view','legalStatus','deliveryStatus','financialStatus', 'otherAccessories' ,'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];\r\n  }\r\n\r\n  else if (compoundType === 'inside_compound' &&  (type === 'apartments' ||type === 'duplexes' || type === 'studios' || type === 'penthouses'|| type === 'i_villa' )) {\r\n  return ['compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea' ,'numberOfRooms' , 'numberOfBathrooms',  'view', 'finishingType','deliveryStatus',   'financialStatus', 'otherAccessories' , 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment' ];\r\n  }\r\n  else if (compoundType === 'inside_compound' &&  (type === 'standalone_villas' || type ==='twin_houses' || type === 'town_houses')) {\r\n  return ['compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n  }\r\n\r\n  else if (compoundType === 'inside_compound' &&  (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores' )) {\r\nreturn ['compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea','view', 'finishingType', 'deliveryStatus','fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'totalPriceInCash', 'pricePerMeterInInstallment', 'totalPriceInInstallment'];\r\n}\r\n\r\n  return [ ];\r\n}\r\n\r\n// Check if a specific field should be shown\r\nshouldShowField(fieldName: string): boolean {\r\n   return this.getFieldsToShow().includes(fieldName);\r\n}\r\n\r\n\r\n  // /*/***************************** */\r\n  selectCity(cityId: number, cityName: string) {\r\n    this.selectedCityId = cityId;\r\n    this.selectedCityName = cityName;\r\n    this.step1Form.patchValue({\r\n      cityId: cityId,\r\n    });\r\n    this.loadAreas(cityId);\r\n  }\r\n\r\n  selectUnitType(UnitValue: string) {\r\n    this.selectedUnitType = UnitValue;\r\n    this.step0Form.patchValue({\r\n      type: UnitValue,\r\n    });\r\n\r\n    // Clear unitFacing field if unit type doesn't require it\r\n    const unitTypesWithFacing = [\r\n      'apartments',\r\n      'duplexes',\r\n      'studios',\r\n      'penthouses',\r\n    ];\r\n    if (!unitTypesWithFacing.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        unitFacing: null,\r\n      });\r\n    }\r\n\r\n    // Clear buildingArea and groundArea fields if unit type doesn't require them\r\n    const unitTypesWithAreaFields = [\r\n      'standalone_villas',\r\n      'factory_lands',\r\n      'commercial_administrative_buildings',\r\n      'residential_buildings',\r\n      'warehouses',\r\n\r\n    ];\r\n    if (!unitTypesWithAreaFields.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        buildingArea: null,\r\n        groundArea: null,\r\n      });\r\n    }\r\n\r\n    // Clear activity field if unit type doesn't require it\r\n    const unitTypesWithActivity = [\r\n      'commercial_stores',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    if (!unitTypesWithActivity.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        activity: null,\r\n      });\r\n    }\r\n\r\n    // Clear groundLayoutStatus field if unit type doesn't require it\r\n    const unitTypesWithGroundLayout = [\r\n      'factory_lands',\r\n      'warehouses',\r\n      'residential_buildings',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    if (!unitTypesWithGroundLayout.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        groundLayoutStatus: null,\r\n      });\r\n    }\r\n\r\n    // Clear unitDesign field if unit type doesn't require it\r\n    const unitTypesWithUnitDesign = ['standalone_villas'];\r\n    if (!unitTypesWithUnitDesign.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        unitDesign: null,\r\n      });\r\n    }\r\n\r\n    // Clear fitOutCondition field if unit type doesn't require it\r\n    const unitTypesWithFitOutCondition = [\r\n      'villas',\r\n      'full_buildings',\r\n      'pharmacies',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_stores',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    if (!unitTypesWithFitOutCondition.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        fitOutCondition: null,\r\n      });\r\n    }\r\n\r\n    // Clear furnishingStatus field if unit type doesn't require it\r\n    const unitTypesToHideFurnishing = [\r\n      'pharmacies',\r\n      'commercial_stores',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_administrative_buildings',\r\n      'administrative_units',\r\n    ];\r\n    if (unitTypesToHideFurnishing.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        furnishingStatus: null,\r\n      });\r\n    }\r\n\r\n    // Clear legalStatus field if unit type doesn't require it\r\n    const unitTypesWithLegalStatus = [\r\n      'duplexes',\r\n      'penthouses',\r\n      'basement',\r\n      'roofs',\r\n    ];\r\n    if (!unitTypesWithLegalStatus.includes(UnitValue)) {\r\n      this.step2Form.patchValue({\r\n        legalStatus: null,\r\n      });\r\n    }\r\n  }\r\n\r\n  selectArea(areaId: number, areaName: string) {\r\n    this.selectedAreaName = areaName;\r\n    this.step1Form.patchValue({\r\n      areaId: areaId,\r\n    });\r\n    this.loadSubAreas(areaId);\r\n  }\r\n\r\n  selectSubArea(subAreaId: number, subAreaName: string) {\r\n    this.selectedSubAreaName = subAreaName;\r\n    this.step1Form.patchValue({\r\n      subAreaId: subAreaId,\r\n    });\r\n  }\r\n\r\n  // dropdown values for step 2\r\n  selectStep2Value(fieldName: string, value: string) {\r\n    this.step2Form.patchValue({\r\n      [fieldName]: value,\r\n    });\r\n  }\r\n\r\n  //dropdown values for step 3\r\n  selectStep3Value(fieldName: string, value: string) {\r\n    this.step3Form.patchValue({\r\n      [fieldName]: value,\r\n    });\r\n\r\n    // Clear price fields when payment system changes\r\n    if (fieldName === 'paymentSystem') {\r\n      this.clearPriceFields();\r\n    }\r\n  }\r\n\r\n  // Clear all price fields when payment system changes\r\n  clearPriceFields() {\r\n    this.step3Form.patchValue({\r\n      pricePerMeterInCash: null,\r\n      totalPriceInCash: null,\r\n      pricePerMeterInInstallment: null,\r\n      totalPriceInInstallment: null,\r\n    });\r\n  }\r\n\r\n  // Check if cash price fields should be displayed\r\n  shouldShowCashFields(): boolean {\r\n    const paymentSystem = this.step3Form.get('paymentSystem')?.value;\r\n    return (\r\n      paymentSystem === 'cash' ||\r\n      paymentSystem === 'all_of_the_above_are_suitable'\r\n    );\r\n  }\r\n\r\n  // Check if installment price fields should be displayed\r\n  shouldShowInstallmentFields(): boolean {\r\n    const paymentSystem = this.step3Form.get('paymentSystem')?.value;\r\n    return (\r\n      paymentSystem === 'installment' ||\r\n      paymentSystem === 'all_of_the_above_are_suitable'\r\n    );\r\n  }\r\n\r\n  // Check if unitFacing field should be displayed\r\n  shouldShowUnitFacingField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithFacing = [\r\n      'apartments',\r\n      'duplexes',\r\n      'studios',\r\n      'penthouses',\r\n    ];\r\n    return unitTypesWithFacing.includes(unitType);\r\n  }\r\n\r\n  // Check if buildingArea and groundArea fields should be displayed\r\n  shouldShowAreaFields(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithAreaFields = [\r\n      'standalone_villas',\r\n      'factory_lands',\r\n      'commercial_administrative_buildings',\r\n      'residential_buildings',\r\n      'warehouses',\r\n    ];\r\n    return unitTypesWithAreaFields.includes(unitType);\r\n  }\r\n\r\n  // Check if groundLayoutStatus field should be displayed\r\n  shouldShowGroundLayoutStatusField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithGroundLayout = [\r\n      'factory_lands',\r\n      'warehouses',\r\n      'residential_buildings',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    return unitTypesWithGroundLayout.includes(unitType);\r\n  }\r\n\r\n  // Check if activity field should be displayed\r\n  shouldShowActivityField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithActivity = [\r\n      'commercial_stores',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    return unitTypesWithActivity.includes(unitType);\r\n  }\r\n\r\n  // Check if unitDesign field should be displayed\r\n  shouldShowUnitDesignField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithUnitDesign = ['standalone_villas'];\r\n    return unitTypesWithUnitDesign.includes(unitType);\r\n  }\r\n\r\n  // Check if legalStatus field should be displayed\r\n  shouldShowLegalStatusField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithLegalStatus = [\r\n      'duplexes',\r\n      'penthouses',\r\n      'basement',\r\n      'roofs',\r\n    ];\r\n    return unitTypesWithLegalStatus.includes(unitType);\r\n  }\r\n\r\n  // Check if fitOutCondition field should be displayed\r\n  shouldShowFitOutConditionField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesWithFitOutCondition = [\r\n      'villas',\r\n      'full_buildings',\r\n      'pharmacies',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_stores',\r\n      'commercial_administrative_buildings',\r\n    ];\r\n    return unitTypesWithFitOutCondition.includes(unitType);\r\n  }\r\n\r\n  // Check if furnishingStatus field should be displayed\r\n  shouldShowFurnishingStatusField(): boolean {\r\n    const unitType = this.step0Form.get('type')?.value;\r\n    const unitTypesToHideFurnishing = [\r\n      'pharmacies',\r\n      'commercial_stores',\r\n      'factory_lands',\r\n      'warehouses',\r\n      'commercial_administrative_buildings',\r\n      'administrative_units',\r\n    ];\r\n    return !unitTypesToHideFurnishing.includes(unitType);\r\n  }\r\n\r\n  // dropdown values for step 0\r\n  selectStep0Value(fieldName: string, value: string) {\r\n    console.log('selectStep0Value called:', { fieldName, value });\r\n\r\n    this.step0Form.patchValue({\r\n      [fieldName]: value,\r\n    });\r\n\r\n    // Filter unit types when compound type changes\r\n    if (fieldName === 'compoundType') {\r\n      this.filterUnitTypes();\r\n    }\r\n\r\n    // Handle unit type selection\r\n    if (fieldName === 'type') {\r\n      this.selectedUnitType = value;\r\n      this.selectUnitType(value); // Call existing logic for unit type selection\r\n    }\r\n\r\n    console.log('Step0 form after update:', this.step0Form.value);\r\n\r\n    // Trigger change detection to update button state\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  //dropdown values for step 5\r\n  selectStep5Value(fieldName: string, value: string) {\r\n    this.step5Form.patchValue({\r\n      [fieldName]: value,\r\n    });\r\n  }\r\n\r\n  submitForm(checkAd : boolean) {\r\n    if (this.isCurrentFormValid()) {\r\n\r\n      // console.log(formData);\r\n      const httpFormData = new FormData();\r\n\r\n      // Add step0 form data\r\n      Object.keys(this.step0Form.value).forEach((key) => {\r\n        httpFormData.append(key, this.step0Form.value[key]);\r\n      });\r\n\r\n      // Add step1 form data\r\n      Object.keys(this.step1Form.value).forEach((key) => {\r\n        httpFormData.append(key, this.step1Form.value[key]);\r\n      });\r\n\r\n      // Fields to be included in additionalDetails array\r\n      const additionalDetailsFields = ['numberOfFloors'];\r\n\r\n      // Get unit type for conditional field inclusion\r\n      const unitType = this.step0Form.get('type')?.value;\r\n\r\n      // Add buildingArea and groundArea to additionalDetails only for specific unit types\r\n      const unitTypesWithAreaFields = [\r\n        'standalone_villas',\r\n        'factory_lands',\r\n        'commercial_administrative_buildings',\r\n        'residential_buildings',\r\n        'warehouses',\r\n      ];\r\n      if (unitTypesWithAreaFields.includes(unitType)) {\r\n        additionalDetailsFields.push('groundArea');\r\n        additionalDetailsFields.push('buildingArea');\r\n      }\r\n\r\n      // Add activity to additionalDetails only for specific unit types\r\n      const unitTypesWithActivity = [\r\n        'commercial_stores',\r\n        'factory_lands',\r\n        'warehouses',\r\n        'commercial_administrative_buildings',\r\n      ];\r\n      if (unitTypesWithActivity.includes(unitType)) {\r\n        additionalDetailsFields.push('activity');\r\n      }\r\n\r\n      // Add groundLayoutStatus to additionalDetails only for specific unit types\r\n      const unitTypesWithGroundLayout = [\r\n        'factory_lands',\r\n        'warehouses',\r\n        'residential_buildings',\r\n        'commercial_administrative_buildings',\r\n      ];\r\n      if (unitTypesWithGroundLayout.includes(unitType)) {\r\n        additionalDetailsFields.push('groundLayoutStatus');\r\n      }\r\n\r\n      // Add unitDesign to additionalDetails only for specific unit types\r\n      const unitTypesWithUnitDesign = ['standalone_villas'];\r\n      if (unitTypesWithUnitDesign.includes(unitType)) {\r\n        additionalDetailsFields.push('unitDesign');\r\n      }\r\n\r\n      // Add fitOutCondition to additionalDetails only for specific unit types\r\n      const unitTypesWithFitOutCondition = [\r\n        'villas',\r\n        'full_buildings',\r\n        'pharmacies',\r\n        'factory_lands',\r\n        'warehouses',\r\n        'commercial_stores',\r\n        'commercial_administrative_buildings',\r\n      ];\r\n      if (unitTypesWithFitOutCondition.includes(unitType)) {\r\n        additionalDetailsFields.push('fitOutCondition');\r\n      }\r\n\r\n      // Add furnishingStatus to additionalDetails only for specific unit types\r\n      const unitTypesToHideFurnishing = [\r\n        'pharmacies',\r\n        'commercial_stores',\r\n        'factory_lands',\r\n        'warehouses',\r\n        'commercial_administrative_buildings',\r\n        'administrative_units',\r\n      ];\r\n      if (!unitTypesToHideFurnishing.includes(unitType)) {\r\n        additionalDetailsFields.push('furnishingStatus');\r\n      }\r\n\r\n      // Add unitFacing to additionalDetails only for specific unit types\r\n      const unitTypesWithFacing = [\r\n        'apartments',\r\n        'duplexes',\r\n        'studios',\r\n        'penthouses',\r\n      ];\r\n      if (unitTypesWithFacing.includes(unitType)) {\r\n        additionalDetailsFields.push('unitFacing');\r\n      }\r\n\r\n      // Add step2 form data (excluding fields that go to additionalDetails and otherAccessories)\r\n      Object.keys(this.step2Form.value).forEach((key) => {\r\n        if (\r\n          key !== 'otherAccessories' &&\r\n          !additionalDetailsFields.includes(key)\r\n        ) {\r\n          httpFormData.append(key, this.step2Form.value[key]);\r\n        }\r\n      });\r\n\r\n      // Add step3 form data (conditionally based on payment system)\r\n      const paymentSystem = this.step3Form.get('paymentSystem')?.value;\r\n\r\n      // Always add payment system\r\n      httpFormData.append('paymentSystem', paymentSystem);\r\n\r\n      // Conditionally add price fields based on payment system\r\n      if (paymentSystem === 'cash') {\r\n        // Only send cash price fields\r\n        const pricePerMeterInCash = this.step3Form.get(\r\n          'pricePerMeterInCash'\r\n        )?.value;\r\n        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;\r\n\r\n        if (pricePerMeterInCash) {\r\n          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);\r\n        }\r\n        if (totalPriceInCash) {\r\n          httpFormData.append('totalPriceInCash', totalPriceInCash);\r\n        }\r\n      } else if (paymentSystem === 'installment') {\r\n        // Only send installment price fields\r\n        const pricePerMeterInInstallment = this.step3Form.get(\r\n          'pricePerMeterInInstallment'\r\n        )?.value;\r\n        const totalPriceInInstallment = this.step3Form.get(\r\n          'totalPriceInInstallment'\r\n        )?.value;\r\n\r\n        if (pricePerMeterInInstallment) {\r\n          httpFormData.append(\r\n            'pricePerMeterInInstallment',\r\n            pricePerMeterInInstallment\r\n          );\r\n        }\r\n        if (totalPriceInInstallment) {\r\n          httpFormData.append(\r\n            'totalPriceInInstallment',\r\n            totalPriceInInstallment\r\n          );\r\n        }\r\n      } else if (paymentSystem === 'all_of_the_above_are_suitable') {\r\n        // Send all price fields\r\n        const pricePerMeterInCash = this.step3Form.get(\r\n          'pricePerMeterInCash'\r\n        )?.value;\r\n        const totalPriceInCash = this.step3Form.get('totalPriceInCash')?.value;\r\n        const pricePerMeterInInstallment = this.step3Form.get(\r\n          'pricePerMeterInInstallment'\r\n        )?.value;\r\n        const totalPriceInInstallment = this.step3Form.get(\r\n          'totalPriceInInstallment'\r\n        )?.value;\r\n\r\n        if (pricePerMeterInCash) {\r\n          httpFormData.append('pricePerMeterInCash', pricePerMeterInCash);\r\n        }\r\n        if (totalPriceInCash) {\r\n          httpFormData.append('totalPriceInCash', totalPriceInCash);\r\n        }\r\n        if (pricePerMeterInInstallment) {\r\n          httpFormData.append(\r\n            'pricePerMeterInInstallment',\r\n            pricePerMeterInInstallment\r\n          );\r\n        }\r\n        if (totalPriceInInstallment) {\r\n          httpFormData.append(\r\n            'totalPriceInInstallment',\r\n            totalPriceInInstallment\r\n          );\r\n        }\r\n      }\r\n\r\n      // Add step5 form data (excluding legalStatus which goes to additionalDetails)\r\n      Object.keys(this.step5Form.value).forEach((key) => {\r\n        if (key !== 'legalStatus') {\r\n          httpFormData.append(key, this.step5Form.value[key]);\r\n        }\r\n      });\r\n\r\n      // Create additionalDetails object\r\n      const additionalDetails: any = {};\r\n\r\n      // Add fields from step2Form\r\n      additionalDetailsFields.forEach((field) => {\r\n        const value = this.step2Form.get(field)?.value;\r\n        if (value) {\r\n          additionalDetails[field] = value;\r\n        }\r\n      });\r\n\r\n      // Add legalStatus from step5Form only for specific unit types\r\n      const unitTypesWithLegalStatus = [\r\n        'duplexes',\r\n        'penthouses',\r\n        'basement',\r\n        'roofs',\r\n      ];\r\n      if (unitTypesWithLegalStatus.includes(unitType)) {\r\n        const legalStatus = this.step5Form.get('legalStatus')?.value;\r\n        if (legalStatus) {\r\n          additionalDetails['legalStatus'] = legalStatus;\r\n        }\r\n      }\r\n\r\n      // Send additionalDetails as individual form fields (not JSON)\r\n      Object.keys(additionalDetails).forEach((key) => {\r\n        httpFormData.append(\r\n          `additionalDetails[${key}]`,\r\n          additionalDetails[key]\r\n        );\r\n      });\r\n\r\n      //add files\r\n      const fileFields = [\r\n        'diagram',\r\n        'layout',\r\n        'videos',\r\n        'locationInMasterPlan',\r\n      ];\r\n      fileFields.forEach((field) => {\r\n        const files = this.step4Form.get(field)?.value;\r\n        if (files && files.length) {\r\n          const isMultiple = ['layout', 'videos'].includes(field);\r\n\r\n          if (isMultiple) {\r\n            files.forEach((file: File) => {\r\n              httpFormData.append(`${field}[]`, file);\r\n            });\r\n          } else {\r\n            httpFormData.append(field, files[0]);\r\n          }\r\n        }\r\n      });\r\n\r\n      // Handle otherAccessories as array\r\n      const accessoriesRaw = this.step2Form.get('otherAccessories')?.value;\r\n      const accessoriesArray = Array.isArray(accessoriesRaw)\r\n        ? accessoriesRaw\r\n        : [];\r\n\r\n      // Send otherAccessories as individual array elements\r\n      accessoriesArray.forEach((accessory, index) => {\r\n        httpFormData.append(`otherAccessories[${index}]`, accessory);\r\n      });\r\n\r\n      httpFormData.append('brokerId', this.brokerId.toString());\r\n\r\n      // Set as advertisement\r\n      if(checkAd){\r\n        httpFormData.append('isAdvertisement', '1');\r\n      }\r\n\r\n      // Show loading state\r\n      const button = document.querySelector('.btn-primary');\r\n      if (button) {\r\n        button.classList.add('btn-loading');\r\n      }\r\n\r\n      this.propertyService.createProperty(httpFormData).subscribe({\r\n        next: async (response) => {\r\n          console.log('Property data submitted:', response);\r\n          await Swal.fire('Property data submitted:', '', response.status);\r\n\r\n          this.router.navigate(['/broker/dataandproperties'], {\r\n            queryParams: { success: 'add' },\r\n          });\r\n        },\r\n        error: (err) => {\r\n          console.error('Error loading unitTypes:', err);\r\n          Swal.fire(err.message, '', err.status);\r\n\r\n          // Remove loading state\r\n          if (button) {\r\n            button.classList.remove('btn-loading');\r\n          }\r\n        },\r\n        complete: () => {\r\n          this.cdr.detectChanges();\r\n          // Remove loading state\r\n          if (button) {\r\n            button.classList.remove('btn-loading');\r\n          }\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  cancel() {\r\n    this.router.navigate(['/broker/dataandproperties']);\r\n  }\r\n\r\n  onFileChange(event: any, fieldName: string) {\r\n    if (event.target.files && event.target.files.length) {\r\n      const files = Array.from(event.target.files);\r\n      this.step4Form.patchValue({\r\n        [fieldName]: files,\r\n      });\r\n\r\n      console.log(`${fieldName}: ${files.length} files selected`);\r\n    }\r\n  }\r\n\r\n  getFileCount(fieldName: string): number {\r\n    const files = this.step4Form.get(fieldName)?.value;\r\n    return files && Array.isArray(files) ? files.length : 0;\r\n  }\r\n\r\n  // Check if current form is valid\r\n  isCurrentFormValid(): boolean {\r\n    const currentForm = this.getCurrentForm();\r\n\r\n    // For step 0, only check if unit type is selected\r\n    if (this.currentStep === 0) {\r\n      const compoundType = this.step0Form.get('compoundType')?.value;\r\n      const unitType = this.step0Form.get('type')?.value;\r\n      const isValid = !!(compoundType && unitType);\r\n      console.log('Step 0 validation:', { compoundType, unitType, isValid });\r\n      return isValid;\r\n    }\r\n\r\n    // For step 2, check only visible/required fields\r\n    if (this.currentStep === 2) {\r\n      return this.isStep2FormValid();\r\n    }\r\n\r\n    return currentForm.valid;\r\n  }\r\n\r\n  // Custom validation for Step 2 - only check visible fields\r\n  isStep2FormValid(): boolean {\r\n    const form = this.step2Form;\r\n    const fieldsToShow = this.getFieldsToShow();\r\n\r\n    // Required fields that must always be valid if they're shown\r\n    const requiredFields = ['unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus'];\r\n\r\n\r\n    for (const fieldName of requiredFields) {\r\n      if (fieldsToShow.includes(fieldName)) {\r\n        const control = form.get(fieldName);\r\n        if (!control || control.invalid) {\r\n          console.log(`Step 2 validation failed for field: ${fieldName}`, control?.errors);\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Check conditional fields only if they're shown and required for the specific unit type\r\n    const conditionalFields = ['unitFacing', 'legalStatus', 'fitOutCondition', 'furnishingStatus', 'groundLayoutStatus', 'unitDesign', 'activity'];\r\n\r\n    for (const fieldName of conditionalFields) {\r\n      if (fieldsToShow.includes(fieldName) && this.isFieldRequiredForUnitType(fieldName)) {\r\n        const control = form.get(fieldName);\r\n        if (!control || control.invalid) {\r\n          console.log(`Step 2 validation failed for conditional field: ${fieldName}`, control?.errors);\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('Step 2 validation passed');\r\n    return true;\r\n  }\r\n\r\n  // Check if a field is required for the current unit type\r\n  isFieldRequiredForUnitType(fieldName: string): boolean {\r\n    switch (fieldName) {\r\n      case 'unitFacing':\r\n        return this.shouldShowUnitFacingField();\r\n      case 'legalStatus':\r\n        return this.shouldShowLegalStatusField();\r\n      case 'fitOutCondition':\r\n        return this.shouldShowFitOutConditionField();\r\n      case 'furnishingStatus':\r\n        return this.shouldShowFurnishingStatusField();\r\n      case 'groundLayoutStatus':\r\n        return this.shouldShowGroundLayoutStatusField();\r\n      case 'unitDesign':\r\n        return this.shouldShowUnitDesignField();\r\n      case 'activity':\r\n        return this.shouldShowActivityField();\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  // Navigate to next step\r\n  nextStep() {\r\n    if (this.currentStep < this.totalSteps) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  // Navigate to previous step\r\n  prevStep() {\r\n    if (this.currentStep > 0) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  formatUnitTypeKey(key: string): string {\r\n    if (!key || typeof key !== 'string') return '';\r\n\r\n    return key\r\n      .split('_')\r\n      .map((word) =>\r\n        word.trim() ? word[0].toUpperCase() + word.slice(1).toLowerCase() : ''\r\n      )\r\n      .join(' ');\r\n  }\r\n\r\n  toggleAccessory(value: string): void {\r\n    const index = this.selectedAccessories.indexOf(value);\r\n\r\n    if (index > -1) {\r\n      this.selectedAccessories.splice(index, 1);\r\n    } else {\r\n      this.selectedAccessories.push(value);\r\n    }\r\n\r\n    // Update form control\r\n    this.step2Form.patchValue({\r\n      otherAccessories: [...this.selectedAccessories],\r\n    });\r\n  }\r\n\r\n  // Handle \"All The Above Are Suitable\" checkbox\r\n  onAllAccessoriesChange(event: any): void {\r\n    if (event.target.checked) {\r\n      // Select all accessories\r\n      this.selectedAccessories = this.otherAccessoriesTypes.map((a) => a.value);\r\n    } else {\r\n      // Unselect all accessories\r\n      this.selectedAccessories = [];\r\n    }\r\n\r\n    this.step2Form.patchValue({\r\n      otherAccessories: [...this.selectedAccessories],\r\n    });\r\n  }\r\n\r\n  isAccessorySelected(value: string): boolean {\r\n    return this.selectedAccessories.includes(value);\r\n  }\r\n\r\n  getSelectedAccessoriesText(): string {\r\n    if (this.selectedAccessories.length === 0) {\r\n      return '';\r\n    }\r\n\r\n    if (this.selectedAccessories.length === 1) {\r\n      const accessory = this.otherAccessoriesTypes.find(\r\n        (a) => a.value === this.selectedAccessories[0]\r\n      );\r\n      return accessory ? accessory.key : '';\r\n    }\r\n\r\n    return `${this.selectedAccessories.length} accessories selected`;\r\n  }\r\n\r\n  // Get compound type text for display\r\n  getCompoundTypeText(value: string): string {\r\n    if (!value) return '';\r\n    const option = this.compoundOptions.find(opt => opt.value === value);\r\n    return option ? option.key : '';\r\n  }\r\n\r\n\r\n\r\n  // Get unit type text for display\r\n  getUnitTypeText(value: string): string {\r\n    if (!value) return '';\r\n    const unitType = this.filteredUnitTypes.find(unit => unit.value === value);\r\n    return unitType ? unitType.key : '';\r\n  }\r\n}\r\n", "<div class=\"mb-5 mt-0\">\r\n  <app-broker-title [showCreateButton]=\"false\"></app-broker-title>\r\n</div>\r\n\r\n<div class=\"card rounded-4\">\r\n  <div class=\"card-body p-10\">\r\n    <div class=\"stepper stepper-pills d-flex flex-column\" id=\"add_property_stepper\">\r\n      <!-- Header and Progress Bar -->\r\n      <div class=\"mb-5 text-center\">\r\n        <ng-container *ngIf=\"currentStep === 0\">\r\n          <h2>\r\n            <span class=\"text-dark-blue fw-bold\">Add Property - </span>\r\n            <span class=\"text-dark-blue fw-normal\">Property Category </span>\r\n          </h2>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"currentStep === 1\">\r\n          <h2>\r\n            <span class=\"text-dark-blue fw-bold\">Add Unit - </span>\r\n            <span class=\"text-dark-blue fw-normal\">Location Information </span>\r\n          </h2>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"currentStep === 2\">\r\n          <h2>\r\n            <span class=\"text-dark-blue fw-bold\">Add Unit - </span>\r\n            <span class=\"text-dark-blue fw-normal\">Unit Information </span>\r\n          </h2>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"currentStep === 3\">\r\n          <h2>\r\n            <span class=\"text-dark-blue fw-bold\">Add Unit - </span>\r\n            <span class=\"text-dark-blue fw-normal\">Payment Details </span>\r\n          </h2>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"currentStep === 4\">\r\n          <h2>\r\n            <span class=\"text-dark-blue fw-bold\">Add Property - </span>\r\n            <span class=\"text-dark-blue fw-normal\">Media & Documents</span>\r\n          </h2>\r\n        </ng-container>\r\n\r\n        <ng-container *ngIf=\"currentStep === 5\">\r\n          <h2>\r\n            <span class=\"text-dark-blue fw-bold\">Add Property - </span>\r\n            <span class=\"text-dark-blue fw-normal\">Review & Submit</span>\r\n          </h2>\r\n        </ng-container>\r\n\r\n        <div class=\"d-flex justify-content-center align-items-center mb-2\">\r\n          <span class=\"text-success fw-bold\">Step {{ currentStep }}</span>\r\n          <span class=\"text-muted mx-1\">of</span>\r\n          <span class=\"text-muted\">{{ totalSteps }}</span>\r\n        </div>\r\n\r\n        <div *ngIf=\"currentStep > 0\" class=\"text-primary cursor-pointer mb-2\" (click)=\"prevStep()\">\r\n          Back to previous step\r\n        </div>\r\n\r\n        <div class=\"progress h-8px bg-light-success w-75 mx-auto\">\r\n          <div class=\"progress-bar bg-success\" role=\"progressbar\" [style.width]=\"(currentStep / totalSteps) * 100 + '%'\"\r\n            aria-valuenow=\"50\" aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Form Content -->\r\n      <form class=\"mx-auto w-100 pt-5 pb-10\">\r\n        <!-- Step 0: Property Category Selection -->\r\n        <div *ngIf=\"currentStep === 0\" [formGroup]=\"step0Form\">\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Compound Type</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"compoundTypeDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  getCompoundTypeText(step0Form.get(\"compoundType\")?.value) ||\r\n                  \" Select Compound Type\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"compoundTypeDropdown\" style=\"\r\n                  max-height: 200px;\r\n                  overflow-y: auto;\r\n                  position: absolute;\r\n                  z-index: 1000;\r\n                \">\r\n                <li *ngFor=\"let option of compoundOptions\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep0Value('compoundType', option.value)\">{{\r\n                    option.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Property Type (Sale/Rent) -->\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Property Type</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"propertyTypeDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step0Form.get(\"propertyType\")?.value) ||\r\n                  \"Select property type\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"propertyTypeDropdown\" style=\"\r\n                  max-height: 200px;\r\n                  overflow-y: auto;\r\n                  position: absolute;\r\n                  z-index: 1000;\r\n                \">\r\n                <li *ngFor=\"let option of filterPropertyTypes()\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep0Value('propertyType', option.value)\">{{\r\n                    option.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Unit Type</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"unitTypeDropdownStep0\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\"\r\n                [disabled]=\"filteredUnitTypes.length === 0\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step0Form.get(\"type\")?.value) ||\r\n                  \"Select Unit Type\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"unitTypeDropdownStep0\" style=\"\r\n                  max-height: 200px;\r\n                  overflow-y: auto;\r\n                  position: absolute;\r\n                  z-index: 1000;\r\n                \">\r\n                <li *ngFor=\"let unitType of filteredUnitTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep0Value('type', unitType.value)\">{{ unitType.key\r\n                    }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <small *ngIf=\"filteredUnitTypes.length === 0\" class=\"text-muted\">\r\n              Please select compound type first\r\n            </small>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Step 1: Basic Property Settings -->\r\n        <div *ngIf=\"currentStep === 1\" [formGroup]=\"step1Form\">\r\n          <!-- Owner Information Row -->\r\n          <div class=\"row mb-10\">\r\n            <!-- Owner Name -->\r\n            <div class=\"col-md-6\">\r\n              <label class=\"form-label fw-bold text-start d-block\">\r\n                Owner Name\r\n              </label>\r\n              <input type=\"text\" class=\"form-control text-start\" formControlName=\"ownerName\" placeholder=\"Enter name\" />\r\n            </div>\r\n\r\n            <!-- Owner Phone -->\r\n            <div class=\"col-md-6\">\r\n              <label class=\"form-label fw-bold text-start d-block\">\r\n                Owner Phone\r\n              </label>\r\n              <input type=\"text\" class=\"form-control text-start\" [ngClass]=\"{\r\n                  'is-invalid':\r\n                    step1Form.get('ownerPhone')?.invalid &&\r\n                    (step1Form.get('ownerPhone')?.touched ||\r\n                      step1Form.get('ownerPhone')?.dirty)\r\n                }\" formControlName=\"ownerPhone\" placeholder=\"Enter phone number\" />\r\n              <div *ngIf=\"\r\n                  step1Form.get('ownerPhone')?.invalid &&\r\n                  (step1Form.get('ownerPhone')?.touched ||\r\n                    step1Form.get('ownerPhone')?.dirty)\r\n                \" class=\"invalid-feedback\">\r\n                <div *ngIf=\"step1Form.get('ownerPhone')?.errors?.['required']\">\r\n                  Phone number is required.\r\n                </div>\r\n                <div *ngIf=\"step1Form.get('ownerPhone')?.errors?.['pattern']\">\r\n                  Please enter a valid Egyptian phone number (e.g., 01XXXXXXXXX).\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Location Information Row -->\r\n          <div class=\"row mb-10\">\r\n            <!-- City -->\r\n            <div class=\"col-md-6\">\r\n              <label class=\"form-label fw-bold text-start d-block\">City</label>\r\n\r\n              <!-- Loading indicator -->\r\n              <div *ngIf=\"isLoadingCities\" class=\"text-primary mb-2\">\r\n                Loading cities...\r\n              </div>\r\n\r\n              <!-- Debug info -->\r\n              <div *ngIf=\"!isLoadingCities && cities.length === 0\" class=\"text-danger mb-2\">\r\n                No cities available\r\n              </div>\r\n\r\n              <div class=\"dropdown\">\r\n                <button\r\n                  class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                  type=\"button\" id=\"cityDropdownStep1\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\"\r\n                  [disabled]=\"isLoadingCities\">\r\n                  <span>\r\n                    <ng-container *ngIf=\"isLoadingCities\">\r\n                      Loading...\r\n                    </ng-container>\r\n                    <ng-container *ngIf=\"!isLoadingCities\">\r\n                      {{ selectedCityName || \"Select City\" }}\r\n                    </ng-container>\r\n                  </span>\r\n                  <i class=\"fas fa-chevron-down\"></i>\r\n                </button>\r\n\r\n                <ul class=\"dropdown-menu w-100\" aria-labelledby=\"cityDropdownStep1\" style=\"\r\n                    max-height: 200px;\r\n                    overflow-y: auto;\r\n                    position: absolute;\r\n                    z-index: 1000;\r\n                  \">\r\n                  <!-- Debug info -->\r\n                  <li class=\"dropdown-item disabled\">\r\n                    Total - Cities: {{ cities.length }}\r\n                  </li>\r\n\r\n                  <ng-container *ngIf=\"cities && cities.length > 0; else noCities\">\r\n                    <li *ngFor=\"let city of cities\" style=\"cursor: pointer\">\r\n                      <a class=\"dropdown-item text-start\" (click)=\"selectCity(city.id, city.name_en)\">\r\n                        {{ city.name_en }}\r\n                      </a>\r\n                    </li>\r\n                  </ng-container>\r\n\r\n                  <ng-template #noCities>\r\n                    <li>\r\n                      <a class=\"dropdown-item text-start disabled\">\r\n                        No cities available\r\n                      </a>\r\n                    </li>\r\n                  </ng-template>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Area -->\r\n            <div class=\"col-md-6\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Area</label>\r\n              <div class=\"dropdown\">\r\n                <button\r\n                  class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                  type=\"button\" id=\"areaDropdownStep1\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                  <span>{{ selectedAreaName || \"Select Area\" }}</span>\r\n                  <i class=\"fas fa-chevron-down\"></i>\r\n                </button>\r\n                <ul class=\"dropdown-menu w-100\" aria-labelledby=\"areaDropdownStep1\" style=\"\r\n                    max-height: 200px;\r\n                    overflow-y: auto;\r\n                    position: absolute;\r\n                    z-index: 1000;\r\n                  \">\r\n                  <li *ngIf=\"areas.length > 0\">\r\n                    <a *ngFor=\"let area of areas\" class=\"dropdown-item text-start\"\r\n                      (click)=\"selectArea(area.id, area.name_en)\">{{ area.name_en }}</a>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"row\">\r\n            <div class=\"col-md-6\">\r\n              <div class=\"mb-10\">\r\n                <label class=\"form-label fw-bold text-start d-block\">\r\n                  Sub Area\r\n                </label>\r\n                <div class=\"dropdown\">\r\n                  <button\r\n                    class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                    type=\"button\" id=\"subAreaDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                    <span style=\"color: #000;\">{{ selectedSubAreaName || \"Select sub area\" }}</span>\r\n                    <i class=\"fas fa-chevron-down\"></i>\r\n                  </button>\r\n                  <ul class=\"dropdown-menu w-100 areas-dropdown\" aria-labelledby=\"subAreaDropdown\">\r\n                    <li class=\"areas-list\">\r\n                      <a *ngFor=\"let subArea of subAreas\" class=\"dropdown-item text-start text-dark\"\r\n                        (click)=\"selectSubArea(subArea.id, subArea.name_en)\" style=\"color: #090909 !important;\">\r\n                        {{ subArea.name_en }}\r\n                      </a>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"mb-10\">\r\n                <label class=\"form-label fw-bold text-start d-block\">\r\n                  Detailed Address\r\n                </label>\r\n                <input type=\"text\" class=\"form-control text-start\" formControlName=\"detailedAddress\"\r\n                  placeholder=\"Enter detailed address\" />\r\n              </div>\r\n            </div>\r\n\r\n          </div>\r\n\r\n          <!-- Mall Name -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('mallName')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Mall Name\r\n            </label>\r\n            <input type=\"text\" class=\"form-control text-start\" formControlName=\"mallName\"\r\n              placeholder=\"Enter mall name (optional)\" [ngClass]=\"{\r\n                'is-invalid':\r\n                  step1Form.get('mallName')?.invalid &&\r\n                  (step1Form.get('mallName')?.touched ||\r\n                    step1Form.get('mallName')?.dirty)\r\n              }\" />\r\n            <div *ngIf=\"\r\n                step1Form.get('mallName')?.invalid &&\r\n                (step1Form.get('mallName')?.touched ||\r\n                  step1Form.get('mallName')?.dirty)\r\n              \" class=\"invalid-feedback\">\r\n              <div *ngIf=\"step1Form.get('mallName')?.errors?.['maxlength']\">\r\n                Mall name cannot exceed 255 characters.\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Compound Name -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('compoundName')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Compound Name\r\n            </label>\r\n            <input type=\"text\" class=\"form-control text-start\" formControlName=\"compoundName\"\r\n              placeholder=\"Enter compound name (optional)\" [ngClass]=\"{\r\n                'is-invalid':\r\n                  step1Form.get('compoundName')?.invalid &&\r\n                  (step1Form.get('compoundName')?.touched ||\r\n                    step1Form.get('compoundName')?.dirty)\r\n              }\" />\r\n            <div *ngIf=\"\r\n                step1Form.get('compoundName')?.invalid &&\r\n                (step1Form.get('compoundName')?.touched ||\r\n                  step1Form.get('compoundName')?.dirty)\r\n              \" class=\"invalid-feedback\">\r\n              <div *ngIf=\"step1Form.get('compoundName')?.errors?.['maxlength']\">\r\n                Compound name cannot exceed 255 characters.\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n\r\n\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Google Maps Link\r\n            </label>\r\n            <input type=\"text\" class=\"form-control text-start\" [ngClass]=\"{\r\n                'is-invalid':\r\n                  step1Form.get('location')?.invalid &&\r\n                  (step1Form.get('location')?.touched ||\r\n                    step1Form.get('location')?.dirty)\r\n              }\" formControlName=\"location\" placeholder=\"Enter Google Maps link\" />\r\n            <div *ngIf=\"\r\n                step1Form.get('location')?.invalid &&\r\n                (step1Form.get('location')?.touched ||\r\n                  step1Form.get('location')?.dirty)\r\n              \" class=\"invalid-feedback\">\r\n              <div *ngIf=\"step1Form.get('location')?.errors?.['required']\">\r\n                Google Maps link is required.\r\n              </div>\r\n              <div *ngIf=\"step1Form.get('location')?.errors?.['pattern']\">\r\n                Please enter a valid URL (e.g., https://maps.google.com/...).\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n\r\n        </div>\r\n\r\n        <!-- Step 2: Unit Information -->\r\n        <div *ngIf=\"currentStep === 2\" [formGroup]=\"step2Form\">\r\n          <div class=\"row mb-10\">\r\n            <div class=\"col-md-6\" *ngIf=\"shouldShowField('buildingNumber')\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Building Number</label>\r\n              <input type=\"text\" class=\"form-control text-start\" formControlName=\"buildingNumber\"\r\n                placeholder=\"Enter property number\" />\r\n            </div>\r\n            <div class=\"col-md-6\" *ngIf=\"shouldShowField('unitNumber')\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Unit Number</label>\r\n              <input type=\"text\" class=\"form-control text-start\" formControlName=\"unitNumber\"\r\n                placeholder=\"Enter unit number\" />\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"row mb-10\">\r\n            <div class=\"col-md-6\" *ngIf=\"shouldShowField('floor')\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Floor</label>\r\n              <div class=\"dropdown\">\r\n                <button\r\n                  class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                  type=\"button\" id=\"floorDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                  <span>{{\r\n                    formatUnitTypeKey(step2Form.get(\"floor\")?.value) ||\r\n                    \"Select floor\"\r\n                    }}</span>\r\n                  <i class=\"fas fa-chevron-down\"></i>\r\n                </button>\r\n                <ul class=\"dropdown-menu w-100\" aria-labelledby=\"floorDropdown\">\r\n                  <li *ngFor=\"let floor of floorTypes\">\r\n                    <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('floor', floor.value)\">{{ floor.key\r\n                      }}</a>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\" *ngIf=\"shouldShowField('unitArea')\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Unit Area (sqm)</label>\r\n              <input type=\"number\" class=\"form-control text-start\" formControlName=\"unitArea\"\r\n                placeholder=\"Enter area in square meters\" min=\"0\" />\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"row mb-10\">\r\n            <div class=\"col-md-6 \" *ngIf=\"shouldShowField('buildingArea')\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Building Area (sqm)</label>\r\n              <input type=\"number\" class=\"form-control text-start\" formControlName=\"buildingArea\"\r\n                placeholder=\"Enter building area in square meters\" min=\"0\" />\r\n            </div>\r\n            <div class=\"col-md-6\" *ngIf=\"shouldShowField('groundArea')\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Ground Area (sqm)</label>\r\n              <input type=\"number\" class=\"form-control text-start\" formControlName=\"groundArea\"\r\n                placeholder=\"Enter ground area in square meters\" min=\"0\" />\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"row mb-10\">\r\n            <div class=\"col-md-6\" *ngIf=\"shouldShowField('numberOfRooms')\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Number of Rooms</label>\r\n              <input type=\"number\" class=\"form-control text-start\" formControlName=\"numberOfRooms\"\r\n                placeholder=\"Enter number of rooms\" min=\"0\" />\r\n            </div>\r\n            <div class=\"col-md-6\" *ngIf=\"shouldShowField('numberOfBathrooms')\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Number of Bathrooms</label>\r\n              <input type=\"number\" class=\"form-control text-start\" formControlName=\"numberOfBathrooms\"\r\n                placeholder=\"Enter number of bathrooms\" min=\"0\" />\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"row mb-10\" *ngIf=\"shouldShowField('numberOfFloors')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Number of Floors</label>\r\n            <input type=\"number\" class=\"form-control text-start\" formControlName=\"numberOfFloors\"\r\n              placeholder=\"Enter number of floors\" min=\"0\" />\r\n          </div>\r\n\r\n          <!-- Unit Facing Field - Only show for specific unit types -->\r\n\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('unitFacing')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Facing Location</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"apartmentLocationDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"unitFacing\")?.value) ||\r\n                  \"Select apartment location\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"apartmentLocationDropdown\">\r\n                <li>\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('unitFacing', 'right_of_facade')\">Right\r\n                    Of Facade</a>\r\n                </li>\r\n                <li>\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('unitFacing', 'left_of_facade')\">\r\n                    Left Of Facade\r\n                  </a>\r\n                </li>\r\n                <li>\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('unitFacing', 'side_view')\">\r\n                    Side View\r\n                  </a>\r\n                </li>\r\n                <li>\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('unitFacing', 'rear_view')\">\r\n                    Rear View\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Unit Description Field -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('unitDescription')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Unit Description</label>\r\n            <textarea class=\"form-control text-start\" formControlName=\"unitDescription\"\r\n              placeholder=\"Enter unit description (optional)\" rows=\"4\" [ngClass]=\"{\r\n                'is-invalid':\r\n                  step2Form.get('unitDescription')?.invalid &&\r\n                  (step2Form.get('unitDescription')?.touched ||\r\n                    step2Form.get('unitDescription')?.dirty)\r\n              }\">\r\n            </textarea>\r\n            <div *ngIf=\"\r\n                step2Form.get('unitDescription')?.invalid &&\r\n                (step2Form.get('unitDescription')?.touched ||\r\n                  step2Form.get('unitDescription')?.dirty)\r\n              \" class=\"invalid-feedback\">\r\n              <div *ngIf=\"step2Form.get('unitDescription')?.errors?.['maxlength']\">\r\n                Description cannot exceed 1000 characters.\r\n              </div>\r\n            </div>\r\n            <small class=\"form-text text-muted\">\r\n              Maximum 1000 characters\r\n            </small>\r\n          </div>\r\n\r\n          <!-- Building Deadline -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('buildingDeadline')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Building Deadline</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"buildingDeadlineDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"buildingDeadline\")?.value) ||\r\n                  \"Select building deadline\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"buildingDeadlineDropdown\">\r\n                <li *ngFor=\"let deadline of buildingDeadlineTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('buildingDeadline', deadline.value)\">\r\n                    {{ deadline.key }}\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('view')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">View</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"apartmentViewDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"view\")?.value) ||\r\n                  \"Select apartment view\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"apartmentViewDropdown\">\r\n                <li *ngFor=\"let view of viewTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('view', view.value)\">\r\n                    {{ view.key }}\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('finishingType')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Finishing Status</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"finishingStatusDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"finishingType\")?.value) ||\r\n                  \"Select finishing status\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"finishingStatusDropdown\">\r\n                <li *ngFor=\"let status of finishingType\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('finishingType', status.value)\">{{\r\n                    status.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Fit Out Condition Field - Only show for specific unit types -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('fitOutCondition')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Fit Out Condition Status</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"fitOutConditionDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"fitOutCondition\")?.value) ||\r\n                  \"Select fit out condition\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"fitOutConditionDropdown\">\r\n                <li *ngFor=\"let condition of fitOutConditionTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"\r\n                      selectStep2Value('fitOutCondition', condition.value)\r\n                    \">{{ condition.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Furnishing Status Field - Only show for specific unit types -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowFurnishingStatusField() && shouldShowField('furnishingStatus')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Furnishing Status</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"furnishingStatusDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"furnishingStatus\")?.value) ||\r\n                  \"Select furnishing status\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"furnishingStatusDropdown\">\r\n                <li *ngFor=\"let furnishing of furnishingStatusTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"\r\n                      selectStep2Value('furnishingStatus', furnishing.value)\r\n                    \">{{ furnishing.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Ground Layout Status Field - Only show for specific unit types -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowGroundLayoutStatusField() && shouldShowField('groundLayoutStatus')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Ground Layout Status</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"groundLayoutStatusDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(\r\n                  step2Form.get(\"groundLayoutStatus\")?.value\r\n                  ) || \"Select ground layout status\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"groundLayoutStatusDropdown\">\r\n                <li *ngFor=\"let layout of groundLayoutStatusTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"\r\n                      selectStep2Value('groundLayoutStatus', layout.value)\r\n                    \">{{ layout.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Unit Design Field - Only show for specific unit types -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowUnitDesignField() && shouldShowField('unitDesign')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Unit Design</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"unitDesignDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"unitDesign\")?.value) ||\r\n                  \"Select unit design\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"unitDesignDropdown\">\r\n                <li *ngFor=\"let design of unitDesignTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('unitDesign', design.value)\">{{\r\n                    design.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Activity Field - Only show for specific unit types -->\r\n          <div class=\"mb-10\" *ngIf=\" shouldShowField('activity')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Activity</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"activityDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"activity\")?.value) ||\r\n                  \"Select activity\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"activityDropdown\">\r\n                <li *ngFor=\"let activity of activityTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('activity', activity.value)\">{{\r\n                    activity.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('deliveryStatus')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Delivery Status</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"deliveryStatusDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"deliveryStatus\")?.value) ||\r\n                  \"Select delivery status\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"deliveryStatusDropdown\">\r\n                <li *ngFor=\"let delivery of deliveryTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('deliveryStatus', delivery.value)\">{{\r\n                    delivery.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('deliveryDate')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Delivery Date</label>\r\n            <input type=\"date\" class=\"form-control text-start\" formControlName=\"deliveryDate\"\r\n              placeholder=\"Select delivery date\" />\r\n          </div>\r\n\r\n          <!-- Legal Status Field - Only show for specific unit types -->\r\n          <div class=\"mb-10\" *ngIf=\" shouldShowField('legalStatus')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Legal Status\r\n            </label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"legalStatusDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"legalStatus\")?.value) ||\r\n                  \"Select legal status\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"legalStatusDropdown\">\r\n                <li *ngFor=\"let legal of legalTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('legalStatus', legal.value)\">{{\r\n                    legal.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Financial Status Field -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('financialStatus')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Financial Status\r\n            </label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"financialStatusDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step2Form.get(\"financialStatus\")?.value) ||\r\n                  \"Select financial status\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"financialStatusDropdown\">\r\n                <li *ngFor=\"let financial of financialStatusTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep2Value('financialStatus', financial.value)\">{{\r\n                    financial.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('otherAccessories')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Other Accessories</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"additionalAmenitiesDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  getSelectedAccessoriesText() || \"Select additional amenities\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100 p-3\" aria-labelledby=\"additionalAmenitiesDropdown\">\r\n                <!-- All The Above Are Suitable - Special handling -->\r\n                <li class=\"mb-2\">\r\n                  <div class=\"form-check\">\r\n                    <input class=\"form-check-input\" type=\"checkbox\" id=\"accessory_alltheabovearesuitable\" [checked]=\"\r\n                        isAccessorySelected('all_the_above_are_suitable')\r\n                      \" (change)=\"onAllAccessoriesChange($event)\" />\r\n                    <label class=\"form-check-label text-start\" for=\"accessory_alltheabovearesuitable\">\r\n                      All The Above Are Suitable\r\n                    </label>\r\n                  </div>\r\n                </li>\r\n\r\n                <hr class=\"my-2\" />\r\n\r\n                <!-- Individual accessories -->\r\n                <li *ngFor=\"let accessory of otherAccessoriesTypes\" class=\"mb-2\">\r\n                  <div class=\"form-check\" *ngIf=\"accessory.value !== 'alltheabovearesuitable'\">\r\n                    <input class=\"form-check-input\" type=\"checkbox\" [id]=\"'accessory_' + accessory.value\"\r\n                      [checked]=\"isAccessorySelected(accessory.value)\" (change)=\"toggleAccessory(accessory.value)\" />\r\n                    <label class=\"form-check-label text-start\" [for]=\"'accessory_' + accessory.value\">\r\n                      {{ accessory.key }}\r\n                    </label>\r\n                  </div>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Step 3: Project type   -->\r\n        <div *ngIf=\"currentStep === 3\" [formGroup]=\"step3Form\">\r\n          <!-- Requested Over -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('requestedOver')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Requested Over\r\n            </label>\r\n            <input type=\"text\" class=\"form-control text-start\" formControlName=\"requestedOver\"\r\n              placeholder=\"Enter requested over (optional)\" [ngClass]=\"{\r\n                'is-invalid':\r\n                  step3Form.get('requestedOver')?.invalid &&\r\n                  (step3Form.get('requestedOver')?.touched ||\r\n                    step3Form.get('requestedOver')?.dirty)\r\n              }\" />\r\n            <div *ngIf=\"\r\n                step3Form.get('requestedOver')?.invalid &&\r\n                (step3Form.get('requestedOver')?.touched ||\r\n                  step3Form.get('requestedOver')?.dirty)\r\n              \" class=\"invalid-feedback\">\r\n              <div *ngIf=\"step3Form.get('requestedOver')?.errors?.['maxlength']\">\r\n                Requested over cannot exceed 255 characters.\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Payment System -->\r\n          <div class=\"mb-10\" *ngIf=\"shouldShowField('paymentSystem')\">\r\n            <label class=\"form-label fw-bold text-start d-block\">\r\n              Payment System\r\n            </label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"paymentSystemDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  formatUnitTypeKey(step3Form.get(\"paymentSystem\")?.value) ||\r\n                  \"Select payment system\"\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"paymentSystemDropdown\">\r\n                <li *ngFor=\"let payment of paymentTypes\">\r\n                  <a class=\"dropdown-item text-start\" (click)=\"selectStep3Value('paymentSystem', payment.value)\">{{\r\n                    payment.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n\r\n          <!-- Cash Price Fields - Show when Cash is selected -->\r\n          <ng-container *ngIf=\"shouldShowCashFields() && shouldShowField('pricePerMeterInCash')\">\r\n            <div class=\"mb-10\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Price Per Meter In Cash</label>\r\n              <input type=\"number\" class=\"form-control text-start\" formControlName=\"pricePerMeterInCash\"\r\n                placeholder=\"Enter price per meter in cash\" min=\"0\" />\r\n            </div>\r\n\r\n            <div class=\"mb-10\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Total Price In Cash</label>\r\n              <input type=\"number\" class=\"form-control text-start\" formControlName=\"totalPriceInCash\"\r\n                placeholder=\"Enter total price in cash\" min=\"0\" />\r\n            </div>\r\n          </ng-container>\r\n\r\n          <!-- Installment Price Fields - Show when Installment is selected -->\r\n          <ng-container *ngIf=\"shouldShowInstallmentFields() && shouldShowField('pricePerMeterInInstallment')\">\r\n            <div class=\"mb-10\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Price Per Meter In Installment</label>\r\n              <input type=\"number\" class=\"form-control text-start\" formControlName=\"pricePerMeterInInstallment\"\r\n                placeholder=\"Enter price per meter in installment\" min=\"0\" />\r\n            </div>\r\n\r\n            <div class=\"mb-10\" *ngIf=\"shouldShowField('totalPriceInInstallment')\">\r\n              <label class=\"form-label fw-bold text-start d-block\">Total Price In Installment</label>\r\n              <input type=\"number\" class=\"form-control text-start\" formControlName=\"totalPriceInInstallment\"\r\n                placeholder=\"Enter total price in installment\" min=\"0\" />\r\n            </div>\r\n          </ng-container>\r\n        </div>\r\n\r\n        <!-- Step 4: Project Documents -->\r\n        <div *ngIf=\"currentStep === 4\" [formGroup]=\"step4Form\">\r\n          <!-- Project Documents Cards -->\r\n          <div class=\"mb-10 upload-card-container\">\r\n            <!--    Upload image of main unit -->\r\n            <div class=\"card mb-5 cursor-pointer\">\r\n              <label for=\"projectUnitImage\" class=\"card-body text-center py-3\">\r\n                <div class=\"upload-icon\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text\">\r\n                  Upload image of main unit\r\n                  <span *ngIf=\"getFileCount('diagram') > 0\" class=\"badge bg-success ms-2\">\r\n                    {{ getFileCount(\"diagram\") }}\r\n                  </span>\r\n                </span>\r\n                <input type=\"file\" id=\"projectUnitImage\" class=\"d-none\" (change)=\"onFileChange($event, 'diagram')\"\r\n                  multiple />\r\n              </label>\r\n            </div>\r\n\r\n            <!-- Upload a photos to the gallery -->\r\n            <div class=\"card mb-5 cursor-pointer\">\r\n              <label for=\"projectLayout\" class=\"card-body text-center py-3\">\r\n                <div class=\"upload-icon\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text\">\r\n                  Upload photos to the gallery\r\n                  <span *ngIf=\"getFileCount('layout') > 0\" class=\"badge bg-success ms-2\">\r\n                    {{ getFileCount(\"layout\") }}\r\n                  </span>\r\n                </span>\r\n                <input type=\"file\" id=\"projectLayout\" class=\"d-none\" (change)=\"onFileChange($event, 'layout')\"\r\n                  multiple />\r\n              </label>\r\n            </div>\r\n            <!-- Project Videos -->\r\n            <div class=\"card mb-5 cursor-pointer\">\r\n              <label for=\"videos\" class=\"card-body text-center py-3\">\r\n                <div class=\"upload-icon\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text\">\r\n                  Upload project videos\r\n\r\n                  <span *ngIf=\"getFileCount('videos') > 0\" class=\"badge bg-success ms-2\">\r\n                    {{ getFileCount(\"videos\") }}\r\n                  </span>\r\n                </span>\r\n                <input type=\"file\" id=\"videos\" class=\"d-none\" (change)=\"onFileChange($event, 'videos')\" accept=\"video/*\"\r\n                  multiple />\r\n              </label>\r\n            </div>\r\n\r\n            <!-- Upload unit plan -->\r\n            <div class=\"card mb-5 cursor-pointer\">\r\n              <label for=\"locationInMasterPlan\" class=\"card-body text-center py-3\">\r\n                <div class=\"upload-icon\">\r\n                  <i class=\"fas fa-arrow-up\"></i>\r\n                </div>\r\n                <span class=\"upload-text\">\r\n                  Upload unit plan\r\n                  <span *ngIf=\"getFileCount('locationInMasterPlan') > 0\" class=\"badge bg-success ms-2\">\r\n                    {{ getFileCount(\"locationInMasterPlan\") }}\r\n                  </span>\r\n                </span>\r\n                <input type=\"file\" id=\"locationInMasterPlan\" class=\"d-none\"\r\n                  (change)=\"onFileChange($event, 'locationInMasterPlan')\" multiple />\r\n              </label>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Step 5: Review & Submit -->\r\n        <div *ngIf=\"currentStep === 5\" [formGroup]=\"step5Form\">\r\n          <div class=\"text-center py-5\">\r\n            <div class=\"mb-4\">\r\n              <i class=\"fas fa-check-circle text-success\" style=\"font-size: 3rem;\"></i>\r\n            </div>\r\n            <h4 class=\"mb-3\">Ready to Submit Property</h4>\r\n            <p class=\"text-muted mb-4\">\r\n              Please review all the information you've entered and click submit to add the property.\r\n            </p>\r\n            <div class=\"alert alert-info text-start\">\r\n              <strong>Note:</strong>\r\n              <ul class=\"list-unstyled mb-0 mt-2\">\r\n                <li>• Owner information is now collected in Step 1</li>\r\n                <li>• Legal Status is now part of Step 2 (Unit Information)</li>\r\n                <li>• All forms have been reorganized for better user experience</li>\r\n              </ul>\r\n            </div>\r\n\r\n            <!-- Submit Buttons -->\r\n            <div class=\"d-flex flex-column gap-3 mt-10\">\r\n              <button type=\"button\" class=\"btn btn-success px-1 py-2 rounded-pill\" [disabled]=\"!isCurrentFormValid()\"\r\n                (click)=\"submitForm(false)\">\r\n                <span class=\"indicator-label\"> Add Property</span>\r\n              </button>\r\n\r\n              <button type=\"button\" class=\"btn btn-primary px-1 py-2 rounded-pill\" [disabled]=\"!step5Form.valid\"\r\n                (click)=\"submitForm(true)\">\r\n                <span class=\"indicator-label\">\r\n                  <i class=\"fas fa-bullhorn me-2\"></i> Add Property & Publish\r\n                </span>\r\n                <span class=\"indicator-progress\">\r\n                  Please wait...\r\n                  <span class=\"spinner-border spinner-border-sm align-middle ms-2\"></span>\r\n                </span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Navigation Buttons -->\r\n\r\n        <!-- Step 0 Navigation -->\r\n        <div *ngIf=\"currentStep === 0\" class=\"d-flex justify-content-between pt-10\">\r\n          <button type=\"button\" class=\"btn btn-light-dark btn-lg px-6 py-3\" (click)=\"cancel()\">\r\n            Cancel\r\n          </button>\r\n\r\n          <button type=\"button\" class=\"btn btn-lg btn-navy px-10 py-3 rounded-pill\" [disabled]=\"!isCurrentFormValid()\"\r\n            (click)=\"nextStep()\">\r\n            <span class=\"indicator-label text-white\"> Next - Location Information </span>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Step 1 Navigation -->\r\n        <div *ngIf=\"currentStep === 1\" class=\"d-flex justify-content-center pt-10\">\r\n          <button type=\"button\" class=\"btn btn-lg btn-navy px-10 py-3 rounded-pill\" [disabled]=\"!isCurrentFormValid()\"\r\n            (click)=\"nextStep()\">\r\n            <span class=\"indicator-label text-white\"> Next - Unit Data </span>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- ******-->\r\n        <div *ngIf=\"currentStep > 1 && currentStep !== totalSteps\" class=\"d-flex justify-content-center pt-10\">\r\n          <button type=\"button\" class=\"btn btn-lg btn-navy px-10 py-3 rounded-pill\" [disabled]=\"!isCurrentFormValid()\"\r\n            (click)=\"nextStep()\">\r\n            <span class=\"indicator-label text-white\">\r\n              <ng-container *ngIf=\"currentStep === 2\">\r\n                Next - Payment Details\r\n              </ng-container>\r\n              <ng-container *ngIf=\"currentStep === 3\">\r\n                Next - Media & Documents\r\n              </ng-container>\r\n              <ng-container *ngIf=\"currentStep === 4\">\r\n                Next - Review & Submit\r\n              </ng-container>\r\n            </span>\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;ICKtBC,EAAA,CAAAC,uBAAA,GAAwC;IAEpCD,EADF,CAAAE,cAAA,SAAI,eACmC;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3DJ,EAAA,CAAAE,cAAA,eAAuC;IAAAF,EAAA,CAAAG,MAAA,yBAAkB;IAC3DH,EAD2D,CAAAI,YAAA,EAAO,EAC7D;;;;;;IAEPJ,EAAA,CAAAC,uBAAA,GAAwC;IAEpCD,EADF,CAAAE,cAAA,SAAI,eACmC;IAAAF,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,eAAuC;IAAAF,EAAA,CAAAG,MAAA,4BAAqB;IAC9DH,EAD8D,CAAAI,YAAA,EAAO,EAChE;;;;;;IAEPJ,EAAA,CAAAC,uBAAA,GAAwC;IAEpCD,EADF,CAAAE,cAAA,SAAI,eACmC;IAAAF,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,eAAuC;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IAC1DH,EAD0D,CAAAI,YAAA,EAAO,EAC5D;;;;;;IAEPJ,EAAA,CAAAC,uBAAA,GAAwC;IAEpCD,EADF,CAAAE,cAAA,SAAI,eACmC;IAAAF,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,eAAuC;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IACzDH,EADyD,CAAAI,YAAA,EAAO,EAC3D;;;;;;IAEPJ,EAAA,CAAAC,uBAAA,GAAwC;IAEpCD,EADF,CAAAE,cAAA,SAAI,eACmC;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3DJ,EAAA,CAAAE,cAAA,eAAuC;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IAC1DH,EAD0D,CAAAI,YAAA,EAAO,EAC5D;;;;;;IAGPJ,EAAA,CAAAC,uBAAA,GAAwC;IAEpCD,EADF,CAAAE,cAAA,SAAI,eACmC;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3DJ,EAAA,CAAAE,cAAA,eAAuC;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IACxDH,EADwD,CAAAI,YAAA,EAAO,EAC1D;;;;;;;IASPJ,EAAA,CAAAE,cAAA,cAA2F;IAArBF,EAAA,CAAAK,UAAA,mBAAAC,0DAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IACxFZ,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;;IA+BIJ,EADF,CAAAE,cAAA,SAA2C,YACoD;IAAzDF,EAAA,CAAAK,UAAA,mBAAAQ,8DAAA;MAAA,MAAAC,SAAA,GAAAd,EAAA,CAAAO,aAAA,CAAAQ,GAAA,EAAAC,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAQ,gBAAA,CAAiB,cAAc,EAAAH,SAAA,CAAAI,KAAA,CAAe;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GAC9E;IACjBH,EADiB,CAAAI,YAAA,EAAI,EAChB;;;;IAF0FJ,EAAA,CAAAmB,SAAA,GAC9E;IAD8EnB,EAAA,CAAAoB,iBAAA,CAAAN,SAAA,CAAAO,GAAA,CAC9E;;;;;;IA0BfrB,EADF,CAAAE,cAAA,SAAiD,YAC8C;IAAzDF,EAAA,CAAAK,UAAA,mBAAAiB,8DAAA;MAAA,MAAAC,SAAA,GAAAvB,EAAA,CAAAO,aAAA,CAAAiB,GAAA,EAAAR,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAQ,gBAAA,CAAiB,cAAc,EAAAM,SAAA,CAAAL,KAAA,CAAe;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GAC9E;IACjBH,EADiB,CAAAI,YAAA,EAAI,EAChB;;;;IAF0FJ,EAAA,CAAAmB,SAAA,GAC9E;IAD8EnB,EAAA,CAAAoB,iBAAA,CAAAG,SAAA,CAAAF,GAAA,CAC9E;;;;;;IA0BfrB,EADF,CAAAE,cAAA,SAA+C,YAC0C;IAAnDF,EAAA,CAAAK,UAAA,mBAAAoB,8DAAA;MAAA,MAAAC,WAAA,GAAA1B,EAAA,CAAAO,aAAA,CAAAoB,GAAA,EAAAX,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAQ,gBAAA,CAAiB,MAAM,EAAAS,WAAA,CAAAR,KAAA,CAAiB;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GACnF;IACNH,EADM,CAAAI,YAAA,EAAI,EACL;;;;IAFoFJ,EAAA,CAAAmB,SAAA,GACnF;IADmFnB,EAAA,CAAAoB,iBAAA,CAAAM,WAAA,CAAAL,GAAA,CACnF;;;;;IAIVrB,EAAA,CAAAE,cAAA,gBAAiE;IAC/DF,EAAA,CAAAG,MAAA,0CACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;;IA/ERJ,EAFJ,CAAAE,cAAA,cAAuD,cAClC,gBACoC;IAAAF,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKtEJ,EAJJ,CAAAE,cAAA,cAAsB,iBAGsE,WAClF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,aAKI;IACFF,EAAA,CAAA6B,UAAA,KAAAC,0CAAA,iBAA2C;IAMjD9B,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;IAIJJ,EADF,CAAAE,cAAA,eAAmB,iBACoC;IAAAF,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKtEJ,EAJJ,CAAAE,cAAA,eAAsB,kBAGsE,YAClF;IAAAF,EAAA,CAAAG,MAAA,IAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,aAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,cAKI;IACFF,EAAA,CAAA6B,UAAA,KAAAE,0CAAA,iBAAiD;IAMvD/B,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;IAGJJ,EADF,CAAAE,cAAA,eAAmB,iBACoC;IAAAF,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAMlEJ,EALJ,CAAAE,cAAA,eAAsB,kBAI0B,YACtC;IAAAF,EAAA,CAAAG,MAAA,IAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,aAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,cAKI;IACFF,EAAA,CAAA6B,UAAA,KAAAG,0CAAA,iBAA+C;IAKnDhC,EADE,CAAAI,YAAA,EAAK,EACD;IACNJ,EAAA,CAAA6B,UAAA,KAAAI,6CAAA,oBAAiE;IAIrEjC,EADE,CAAAI,YAAA,EAAM,EACF;;;;;;;IAnFyBJ,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAA0B,SAAA,CAAuB;IAOxCnC,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA2B,mBAAA,EAAAC,OAAA,GAAA5B,MAAA,CAAA0B,SAAA,CAAAG,GAAA,mCAAAD,OAAA,CAAAnB,KAAA,6BAGF;IASmBlB,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAA8B,eAAA,CAAkB;IAenCvC,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAC,OAAA,GAAAhC,MAAA,CAAA0B,SAAA,CAAAG,GAAA,mCAAAG,OAAA,CAAAvB,KAAA,4BAGF;IASmBlB,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAiC,mBAAA,GAAwB;IAc/C1C,EAAA,CAAAmB,SAAA,GAA2C;IAA3CnB,EAAA,CAAAkC,UAAA,aAAAzB,MAAA,CAAAkC,iBAAA,CAAAC,MAAA,OAA2C;IACrC5C,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAK,OAAA,GAAApC,MAAA,CAAA0B,SAAA,CAAAG,GAAA,2BAAAO,OAAA,CAAA3B,KAAA,wBAGF;IASqBlB,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAkC,iBAAA,CAAoB;IAMzC3C,EAAA,CAAAmB,SAAA,EAAoC;IAApCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAkC,iBAAA,CAAAC,MAAA,OAAoC;;;;;IAkCxC5C,EAAA,CAAAE,cAAA,UAA+D;IAC7DF,EAAA,CAAAG,MAAA,kCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,UAA8D;IAC5DF,EAAA,CAAAG,MAAA,wEACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAVRJ,EAAA,CAAAE,cAAA,cAI6B;IAI3BF,EAHA,CAAA6B,UAAA,IAAAiB,iDAAA,iBAA+D,IAAAC,iDAAA,iBAGD;IAGhE/C,EAAA,CAAAI,YAAA,EAAM;;;;;;IANEJ,EAAA,CAAAmB,SAAA,EAAuD;IAAvDnB,EAAA,CAAAkC,UAAA,UAAAc,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,iCAAAU,OAAA,CAAAE,MAAA,kBAAAF,OAAA,CAAAE,MAAA,aAAuD;IAGvDlD,EAAA,CAAAmB,SAAA,EAAsD;IAAtDnB,EAAA,CAAAkC,UAAA,UAAAO,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,iCAAAG,OAAA,CAAAS,MAAA,kBAAAT,OAAA,CAAAS,MAAA,YAAsD;;;;;IAc9DlD,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAGNJ,EAAA,CAAAE,cAAA,cAA8E;IAC5EF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAQAJ,EAAA,CAAAC,uBAAA,GAAsC;IACpCD,EAAA,CAAAG,MAAA,mBACF;;;;;;IACAH,EAAA,CAAAC,uBAAA,GAAuC;IACrCD,EAAA,CAAAG,MAAA,GACF;;;;;IADEH,EAAA,CAAAmB,SAAA,EACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAA1C,MAAA,CAAA2C,gBAAA,uBACF;;;;;;IAkBEpD,EADF,CAAAE,cAAA,aAAwD,YAC0B;IAA5CF,EAAA,CAAAK,UAAA,mBAAAgD,6EAAA;MAAA,MAAAC,QAAA,GAAAtD,EAAA,CAAAO,aAAA,CAAAgD,GAAA,EAAAvC,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+C,UAAA,CAAAF,QAAA,CAAAG,EAAA,EAAAH,QAAA,CAAAI,OAAA,CAAiC;IAAA,EAAC;IAC7E1D,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACD;;;;IAFDJ,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAAG,QAAA,CAAAI,OAAA,MACF;;;;;IAJJ1D,EAAA,CAAAC,uBAAA,GAAiE;IAC/DD,EAAA,CAAA6B,UAAA,IAAA8B,yDAAA,iBAAwD;;;;;IAAnC3D,EAAA,CAAAmB,SAAA,EAAS;IAATnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAmD,MAAA,CAAS;;;;;IAS5B5D,EADF,CAAAE,cAAA,SAAI,YAC2C;IAC3CF,EAAA,CAAAG,MAAA,4BACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACD;;;;;;IAuBLJ,EAAA,CAAAE,cAAA,YAC8C;IAA5CF,EAAA,CAAAK,UAAA,mBAAAwD,kEAAA;MAAA,MAAAC,QAAA,GAAA9D,EAAA,CAAAO,aAAA,CAAAwD,IAAA,EAAA/C,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuD,UAAA,CAAAF,QAAA,CAAAL,EAAA,EAAAK,QAAA,CAAAJ,OAAA,CAAiC;IAAA,EAAC;IAAC1D,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAtBJ,EAAA,CAAAmB,SAAA,EAAkB;IAAlBnB,EAAA,CAAAoB,iBAAA,CAAA0C,QAAA,CAAAJ,OAAA,CAAkB;;;;;IAFlE1D,EAAA,CAAAE,cAAA,SAA6B;IAC3BF,EAAA,CAAA6B,UAAA,IAAAoC,8CAAA,gBAC8C;IAChDjE,EAAA,CAAAI,YAAA,EAAK;;;;IAFiBJ,EAAA,CAAAmB,SAAA,EAAQ;IAARnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAyD,KAAA,CAAQ;;;;;;IAuB1BlE,EAAA,CAAAE,cAAA,YAC0F;IAAxFF,EAAA,CAAAK,UAAA,mBAAA8D,6DAAA;MAAA,MAAAC,WAAA,GAAApE,EAAA,CAAAO,aAAA,CAAA8D,IAAA,EAAArD,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6D,aAAA,CAAAF,WAAA,CAAAX,EAAA,EAAAW,WAAA,CAAAV,OAAA,CAA0C;IAAA,EAAC;IACpD1D,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IADFJ,EAAA,CAAAmB,SAAA,EACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAAiB,WAAA,CAAAV,OAAA,MACF;;;;;IAmCR1D,EAAA,CAAAE,cAAA,UAA8D;IAC5DF,EAAA,CAAAG,MAAA,gDACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAPRJ,EAAA,CAAAE,cAAA,cAI6B;IAC3BF,EAAA,CAAA6B,UAAA,IAAA0C,uDAAA,iBAA8D;IAGhEvE,EAAA,CAAAI,YAAA,EAAM;;;;;IAHEJ,EAAA,CAAAmB,SAAA,EAAsD;IAAtDnB,EAAA,CAAAkC,UAAA,UAAAO,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAG,OAAA,CAAAS,MAAA,kBAAAT,OAAA,CAAAS,MAAA,cAAsD;;;;;IAf9DlD,EADF,CAAAE,cAAA,cAAuD,gBACA;IACnDF,EAAA,CAAAG,MAAA,kBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAA4B,SAAA,gBAMO;IACP5B,EAAA,CAAA6B,UAAA,IAAA2C,iDAAA,kBAI6B;IAK/BxE,EAAA,CAAAI,YAAA,EAAM;;;;;;IAfuCJ,EAAA,CAAAmB,SAAA,GAKvC;IALuCnB,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAyE,eAAA,IAAAC,GAAA,IAAA1B,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAU,OAAA,CAAA2B,OAAA,QAAA3B,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAU,OAAA,CAAA4B,OAAA,OAAA5B,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAU,OAAA,CAAA6B,KAAA,IAKvC;IACE7E,EAAA,CAAAmB,SAAA,EAIL;IAJKnB,EAAA,CAAAkC,UAAA,WAAAO,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAG,OAAA,CAAAkC,OAAA,QAAAlC,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAG,OAAA,CAAAmC,OAAA,OAAAnC,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAG,OAAA,CAAAoC,KAAA,GAIL;;;;;IAwBC7E,EAAA,CAAAE,cAAA,UAAkE;IAChEF,EAAA,CAAAG,MAAA,oDACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAPRJ,EAAA,CAAAE,cAAA,cAI6B;IAC3BF,EAAA,CAAA6B,UAAA,IAAAiD,uDAAA,iBAAkE;IAGpE9E,EAAA,CAAAI,YAAA,EAAM;;;;;IAHEJ,EAAA,CAAAmB,SAAA,EAA0D;IAA1DnB,EAAA,CAAAkC,UAAA,UAAAO,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,mCAAAG,OAAA,CAAAS,MAAA,kBAAAT,OAAA,CAAAS,MAAA,cAA0D;;;;;IAflElD,EADF,CAAAE,cAAA,cAA2D,gBACJ;IACnDF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAA4B,SAAA,gBAMO;IACP5B,EAAA,CAAA6B,UAAA,IAAAkD,iDAAA,kBAI6B;IAK/B/E,EAAA,CAAAI,YAAA,EAAM;;;;;;IAf2CJ,EAAA,CAAAmB,SAAA,GAK3C;IAL2CnB,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAyE,eAAA,IAAAC,GAAA,IAAA1B,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,mCAAAU,OAAA,CAAA2B,OAAA,QAAA3B,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,mCAAAU,OAAA,CAAA4B,OAAA,OAAA5B,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,mCAAAU,OAAA,CAAA6B,KAAA,IAK3C;IACE7E,EAAA,CAAAmB,SAAA,EAIL;IAJKnB,EAAA,CAAAkC,UAAA,WAAAO,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,mCAAAG,OAAA,CAAAkC,OAAA,QAAAlC,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,mCAAAG,OAAA,CAAAmC,OAAA,OAAAnC,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,mCAAAG,OAAA,CAAAoC,KAAA,GAIL;;;;;IAwBC7E,EAAA,CAAAE,cAAA,UAA6D;IAC3DF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,UAA4D;IAC1DF,EAAA,CAAAG,MAAA,sEACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAVRJ,EAAA,CAAAE,cAAA,cAI6B;IAI3BF,EAHA,CAAA6B,UAAA,IAAAmD,iDAAA,iBAA6D,IAAAC,iDAAA,iBAGD;IAG9DjF,EAAA,CAAAI,YAAA,EAAM;;;;;;IANEJ,EAAA,CAAAmB,SAAA,EAAqD;IAArDnB,EAAA,CAAAkC,UAAA,UAAAc,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAU,OAAA,CAAAE,MAAA,kBAAAF,OAAA,CAAAE,MAAA,aAAqD;IAGrDlD,EAAA,CAAAmB,SAAA,EAAoD;IAApDnB,EAAA,CAAAkC,UAAA,UAAAO,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAG,OAAA,CAAAS,MAAA,kBAAAT,OAAA,CAAAS,MAAA,YAAoD;;;;;IA7N1DlD,EALN,CAAAE,cAAA,cAAuD,cAE9B,cAEC,gBACiC;IACnDF,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAA4B,SAAA,gBAA0G;IAC5G5B,EAAA,CAAAI,YAAA,EAAM;IAIJJ,EADF,CAAAE,cAAA,cAAsB,gBACiC;IACnDF,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAA4B,SAAA,gBAKqE;IACrE5B,EAAA,CAAA6B,UAAA,KAAAqD,2CAAA,kBAI6B;IASjClF,EADE,CAAAI,YAAA,EAAM,EACF;IAMFJ,EAHJ,CAAAE,cAAA,eAAuB,eAEC,iBACiC;IAAAF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAQjEJ,EALA,CAAA6B,UAAA,KAAAsD,2CAAA,kBAAuD,KAAAC,2CAAA,kBAKuB;IAS1EpF,EALJ,CAAAE,cAAA,eAAsB,kBAIW,YACvB;IAIJF,EAHA,CAAA6B,UAAA,KAAAwD,oDAAA,0BAAsC,KAAAC,oDAAA,0BAGC;IAGzCtF,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAA4B,SAAA,aAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IASPJ,EAPF,CAAAE,cAAA,cAKI,cAEiC;IACjCF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAULJ,EARA,CAAA6B,UAAA,KAAA0D,oDAAA,2BAAiE,KAAAC,mDAAA,gCAAAxF,EAAA,CAAAyF,sBAAA,CAQ1C;IAS7BzF,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;IAIJJ,EADF,CAAAE,cAAA,eAAsB,iBACiC;IAAAF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAK7DJ,EAJJ,CAAAE,cAAA,eAAsB,kBAGmE,YAC/E;IAAAF,EAAA,CAAAG,MAAA,IAAuC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAA4B,SAAA,aAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,cAKI;IACFF,EAAA,CAAA6B,UAAA,KAAA6D,0CAAA,gBAA6B;IAOrC1F,EAHM,CAAAI,YAAA,EAAK,EACD,EACF,EACF;IAKAJ,EAHN,CAAAE,cAAA,eAAiB,eACO,eACD,iBACoC;IACnDF,EAAA,CAAAG,MAAA,kBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKJJ,EAJJ,CAAAE,cAAA,eAAsB,kBAGiE,gBACxD;IAAAF,EAAA,CAAAG,MAAA,IAA8C;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChFJ,EAAA,CAAA4B,SAAA,aAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IAEPJ,EADF,CAAAE,cAAA,cAAiF,cACxD;IACrBF,EAAA,CAAA6B,UAAA,KAAA8D,yCAAA,gBAC0F;IAOpG3F,EAJQ,CAAAI,YAAA,EAAK,EACF,EACD,EACF,EACF;IAGFJ,EAFJ,CAAAE,cAAA,eAAsB,eACD,iBACoC;IACnDF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAA4B,SAAA,iBACyC;IAI/C5B,EAHI,CAAAI,YAAA,EAAM,EACF,EAEF;IA0BNJ,EAvBA,CAAA6B,UAAA,KAAA+D,2CAAA,kBAAuD,KAAAC,2CAAA,kBAuBI;IAyBzD7F,EADF,CAAAE,cAAA,eAAmB,iBACoC;IACnDF,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAA4B,SAAA,iBAKuE;IACvE5B,EAAA,CAAA6B,UAAA,KAAAiE,2CAAA,kBAI6B;IAWjC9F,EAHE,CAAAI,YAAA,EAAM,EAGF;;;;;;;;;IAzOyBJ,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAAwC,SAAA,CAAuB;IAgBGjD,EAAA,CAAAmB,SAAA,GAK/C;IAL+CnB,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAyE,eAAA,KAAAC,GAAA,IAAA1B,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,iCAAAU,OAAA,CAAA2B,OAAA,QAAA3B,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,iCAAAU,OAAA,CAAA4B,OAAA,OAAA5B,OAAA,GAAAvC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,iCAAAU,OAAA,CAAA6B,KAAA,IAK/C;IACE7E,EAAA,CAAAmB,SAAA,EAIL;IAJKnB,EAAA,CAAAkC,UAAA,WAAAO,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,iCAAAG,OAAA,CAAAkC,OAAA,QAAAlC,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,iCAAAG,OAAA,CAAAmC,OAAA,OAAAnC,OAAA,GAAAhC,MAAA,CAAAwC,SAAA,CAAAX,GAAA,iCAAAG,OAAA,CAAAoC,KAAA,GAIL;IAkBK7E,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAsF,eAAA,CAAqB;IAKrB/F,EAAA,CAAAmB,SAAA,EAA6C;IAA7CnB,EAAA,CAAAkC,UAAA,UAAAzB,MAAA,CAAAsF,eAAA,IAAAtF,MAAA,CAAAmD,MAAA,CAAAhB,MAAA,OAA6C;IAQ/C5C,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAAkC,UAAA,aAAAzB,MAAA,CAAAsF,eAAA,CAA4B;IAEX/F,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAsF,eAAA,CAAqB;IAGrB/F,EAAA,CAAAmB,SAAA,EAAsB;IAAtBnB,EAAA,CAAAkC,UAAA,UAAAzB,MAAA,CAAAsF,eAAA,CAAsB;IAerC/F,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAmD,kBAAA,sBAAA1C,MAAA,CAAAmD,MAAA,CAAAhB,MAAA,MACF;IAEe5C,EAAA,CAAAmB,SAAA,EAAmC;IAAAnB,EAAnC,CAAAkC,UAAA,SAAAzB,MAAA,CAAAmD,MAAA,IAAAnD,MAAA,CAAAmD,MAAA,CAAAhB,MAAA,KAAmC,aAAAoD,YAAA,CAAa;IA0BzDhG,EAAA,CAAAmB,SAAA,GAAuC;IAAvCnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAAwF,gBAAA,kBAAuC;IASxCjG,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAyD,KAAA,CAAAtB,MAAA,KAAsB;IAmBE5C,EAAA,CAAAmB,SAAA,GAA8C;IAA9CnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAAyF,mBAAA,sBAA8C;IAKhDlG,EAAA,CAAAmB,SAAA,GAAW;IAAXnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAA0F,QAAA,CAAW;IAsB1BnG,EAAA,CAAAmB,SAAA,GAAiC;IAAjCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,aAAiC;IAuBjCpG,EAAA,CAAAmB,SAAA,EAAqC;IAArCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,iBAAqC;IA4BJpG,EAAA,CAAAmB,SAAA,GAK/C;IAL+CnB,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAyE,eAAA,KAAAC,GAAA,IAAA2B,QAAA,GAAA5F,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAA+D,QAAA,CAAA1B,OAAA,QAAA0B,QAAA,GAAA5F,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAA+D,QAAA,CAAAzB,OAAA,OAAAyB,QAAA,GAAA5F,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAA+D,QAAA,CAAAxB,KAAA,IAK/C;IACE7E,EAAA,CAAAmB,SAAA,EAIL;IAJKnB,EAAA,CAAAkC,UAAA,WAAAoE,QAAA,GAAA7F,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAgE,QAAA,CAAA3B,OAAA,QAAA2B,QAAA,GAAA7F,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAgE,QAAA,CAAA1B,OAAA,OAAA0B,QAAA,GAAA7F,MAAA,CAAAwC,SAAA,CAAAX,GAAA,+BAAAgE,QAAA,CAAAzB,KAAA,GAIL;;;;;IAiBC7E,EADF,CAAAE,cAAA,cAAgE,gBACT;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5EJ,EAAA,CAAA4B,SAAA,gBACwC;IAC1C5B,EAAA,CAAAI,YAAA,EAAM;;;;;IAEJJ,EADF,CAAAE,cAAA,cAA4D,gBACL;IAAAF,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxEJ,EAAA,CAAA4B,SAAA,gBACoC;IACtC5B,EAAA,CAAAI,YAAA,EAAM;;;;;;IAkBEJ,EADF,CAAAE,cAAA,SAAqC,YACkD;IAAjDF,EAAA,CAAAK,UAAA,mBAAAkG,mEAAA;MAAA,MAAAC,SAAA,GAAAxG,EAAA,CAAAO,aAAA,CAAAkG,IAAA,EAAAzF,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,OAAO,EAAAF,SAAA,CAAAtF,KAAA,CAAc;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GACjF;IACNH,EADM,CAAAI,YAAA,EAAI,EACL;;;;IAFkFJ,EAAA,CAAAmB,SAAA,GACjF;IADiFnB,EAAA,CAAAoB,iBAAA,CAAAoF,SAAA,CAAAnF,GAAA,CACjF;;;;;IAdVrB,EADF,CAAAE,cAAA,cAAuD,gBACA;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAK9DJ,EAJJ,CAAAE,cAAA,cAAsB,iBAG+D,WAC3E;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,aAAgE;IAC9DF,EAAA,CAAA6B,UAAA,IAAA8E,+CAAA,iBAAqC;IAM3C3G,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAbMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,4BAAAD,OAAA,CAAAnB,KAAA,oBAGF;IAIkBlB,EAAA,CAAAmB,SAAA,GAAa;IAAbnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAoG,UAAA,CAAa;;;;;IAQvC7G,EADF,CAAAE,cAAA,cAA0D,gBACH;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5EJ,EAAA,CAAA4B,SAAA,gBACsD;IACxD5B,EAAA,CAAAI,YAAA,EAAM;;;;;IAKJJ,EADF,CAAAE,cAAA,cAA+D,gBACR;IAAAF,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAChFJ,EAAA,CAAA4B,SAAA,gBAC+D;IACjE5B,EAAA,CAAAI,YAAA,EAAM;;;;;IAEJJ,EADF,CAAAE,cAAA,cAA4D,gBACL;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC9EJ,EAAA,CAAA4B,SAAA,gBAC6D;IAC/D5B,EAAA,CAAAI,YAAA,EAAM;;;;;IAKJJ,EADF,CAAAE,cAAA,cAA+D,gBACR;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5EJ,EAAA,CAAA4B,SAAA,gBACgD;IAClD5B,EAAA,CAAAI,YAAA,EAAM;;;;;IAEJJ,EADF,CAAAE,cAAA,cAAmE,gBACZ;IAAAF,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAChFJ,EAAA,CAAA4B,SAAA,gBACoD;IACtD5B,EAAA,CAAAI,YAAA,EAAM;;;;;IAINJ,EADF,CAAAE,cAAA,cAAiE,gBACV;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC7EJ,EAAA,CAAA4B,SAAA,gBACiD;IACnD5B,EAAA,CAAAI,YAAA,EAAM;;;;;;IAKJJ,EADF,CAAAE,cAAA,cAAyD,gBACF;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKxEJ,EAJJ,CAAAE,cAAA,cAAsB,iBAG2E,WACvF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IAGLJ,EAFJ,CAAAE,cAAA,aAA4E,SACtE,aAC8F;IAA5DF,EAAA,CAAAK,UAAA,mBAAAyG,gEAAA;MAAA9G,EAAA,CAAAO,aAAA,CAAAwG,IAAA;MAAA,MAAAtG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,YAAY,EAAE,iBAAiB,CAAC;IAAA,EAAC;IAAC1G,EAAA,CAAAG,MAAA,uBACrF;IACbH,EADa,CAAAI,YAAA,EAAI,EACZ;IAEHJ,EADF,CAAAE,cAAA,UAAI,aAC6F;IAA3DF,EAAA,CAAAK,UAAA,mBAAA2G,gEAAA;MAAAhH,EAAA,CAAAO,aAAA,CAAAwG,IAAA;MAAA,MAAAtG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,YAAY,EAAE,gBAAgB,CAAC;IAAA,EAAC;IAC5F1G,EAAA,CAAAG,MAAA,wBACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACD;IAEHJ,EADF,CAAAE,cAAA,UAAI,aACwF;IAAtDF,EAAA,CAAAK,UAAA,mBAAA4G,gEAAA;MAAAjH,EAAA,CAAAO,aAAA,CAAAwG,IAAA;MAAA,MAAAtG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,YAAY,EAAE,WAAW,CAAC;IAAA,EAAC;IACvF1G,EAAA,CAAAG,MAAA,mBACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACD;IAEHJ,EADF,CAAAE,cAAA,UAAI,aACwF;IAAtDF,EAAA,CAAAK,UAAA,mBAAA6G,gEAAA;MAAAlH,EAAA,CAAAO,aAAA,CAAAwG,IAAA;MAAA,MAAAtG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,YAAY,EAAE,WAAW,CAAC;IAAA,EAAC;IACvF1G,EAAA,CAAAG,MAAA,mBACF;IAIRH,EAJQ,CAAAI,YAAA,EAAI,EACD,EACF,EACD,EACF;;;;;IA5BMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,iCAAAD,OAAA,CAAAnB,KAAA,iCAGF;;;;;IA2CNlB,EAAA,CAAAE,cAAA,UAAqE;IACnEF,EAAA,CAAAG,MAAA,mDACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAPRJ,EAAA,CAAAE,cAAA,cAI6B;IAC3BF,EAAA,CAAA6B,UAAA,IAAAsF,uDAAA,iBAAqE;IAGvEnH,EAAA,CAAAI,YAAA,EAAM;;;;;IAHEJ,EAAA,CAAAmB,SAAA,EAA6D;IAA7DnB,EAAA,CAAAkC,UAAA,UAAAc,OAAA,GAAAvC,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,sCAAAU,OAAA,CAAAE,MAAA,kBAAAF,OAAA,CAAAE,MAAA,cAA6D;;;;;IAdrElD,EADF,CAAAE,cAAA,cAA8D,gBACP;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC7EJ,EAAA,CAAAE,cAAA,mBAMK;IACLF,EAAA,CAAAG,MAAA;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACXJ,EAAA,CAAA6B,UAAA,IAAAuF,iDAAA,kBAI6B;IAK7BpH,EAAA,CAAAE,cAAA,gBAAoC;IAClCF,EAAA,CAAAG,MAAA,gCACF;IACFH,EADE,CAAAI,YAAA,EAAQ,EACJ;;;;;;IAnBuDJ,EAAA,CAAAmB,SAAA,GAKvD;IALuDnB,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAyE,eAAA,IAAAC,GAAA,IAAArC,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,sCAAAD,OAAA,CAAAsC,OAAA,QAAAtC,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,sCAAAD,OAAA,CAAAuC,OAAA,OAAAvC,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,sCAAAD,OAAA,CAAAwC,KAAA,IAKvD;IAEE7E,EAAA,CAAAmB,SAAA,GAIL;IAJKnB,EAAA,CAAAkC,UAAA,WAAAc,OAAA,GAAAvC,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,sCAAAU,OAAA,CAAA2B,OAAA,QAAA3B,OAAA,GAAAvC,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,sCAAAU,OAAA,CAAA4B,OAAA,OAAA5B,OAAA,GAAAvC,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,sCAAAU,OAAA,CAAA6B,KAAA,GAIL;;;;;;IAyBK7E,EADF,CAAAE,cAAA,SAAmD,YACkD;IAA/DF,EAAA,CAAAK,UAAA,mBAAAgH,oEAAA;MAAA,MAAAC,YAAA,GAAAtH,EAAA,CAAAO,aAAA,CAAAgH,IAAA,EAAAvG,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,kBAAkB,EAAAY,YAAA,CAAApG,KAAA,CAAiB;IAAA,EAAC;IAChGlB,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACD;;;;IAFDJ,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAAmE,YAAA,CAAAjG,GAAA,MACF;;;;;IAfNrB,EADF,CAAAE,cAAA,cAA+D,gBACR;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAK1EJ,EAJJ,CAAAE,cAAA,cAAsB,iBAG0E,WACtF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,aAA2E;IACzEF,EAAA,CAAA6B,UAAA,IAAA2F,gDAAA,iBAAmD;IAOzDxH,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAdMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,uCAAAD,OAAA,CAAAnB,KAAA,gCAGF;IAIqBlB,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAgH,qBAAA,CAAwB;;;;;;IAuB/CzH,EADF,CAAAE,cAAA,SAAmC,YACkD;IAA/CF,EAAA,CAAAK,UAAA,mBAAAqH,oEAAA;MAAA,MAAAC,QAAA,GAAA3H,EAAA,CAAAO,aAAA,CAAAqH,IAAA,EAAA5G,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,MAAM,EAAAiB,QAAA,CAAAzG,KAAA,CAAa;IAAA,EAAC;IAChFlB,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAI,EACD;;;;IAFDJ,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAAwE,QAAA,CAAAtG,GAAA,MACF;;;;;IAfNrB,EADF,CAAAE,cAAA,cAAmD,gBACI;IAAAF,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAK7DJ,EAJJ,CAAAE,cAAA,cAAsB,iBAGuE,WACnF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,aAAwE;IACtEF,EAAA,CAAA6B,UAAA,IAAAgG,gDAAA,iBAAmC;IAOzC7H,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAdMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,2BAAAD,OAAA,CAAAnB,KAAA,6BAGF;IAIiBlB,EAAA,CAAAmB,SAAA,GAAY;IAAZnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAqH,SAAA,CAAY;;;;;;IAuB/B9H,EADF,CAAAE,cAAA,SAAyC,YACuD;IAA1DF,EAAA,CAAAK,UAAA,mBAAA0H,oEAAA;MAAA,MAAAC,UAAA,GAAAhI,EAAA,CAAAO,aAAA,CAAA0H,IAAA,EAAAjH,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,eAAe,EAAAsB,UAAA,CAAA9G,KAAA,CAAe;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GAC/E;IACjBH,EADiB,CAAAI,YAAA,EAAI,EAChB;;;;IAF2FJ,EAAA,CAAAmB,SAAA,GAC/E;IAD+EnB,EAAA,CAAAoB,iBAAA,CAAA4G,UAAA,CAAA3G,GAAA,CAC/E;;;;;IAdrBrB,EADF,CAAAE,cAAA,cAA4D,gBACL;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKzEJ,EAJJ,CAAAE,cAAA,cAAsB,iBAGyE,WACrF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,aAA0E;IACxEF,EAAA,CAAA6B,UAAA,IAAAqG,gDAAA,iBAAyC;IAM/ClI,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAbMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,oCAAAD,OAAA,CAAAnB,KAAA,+BAGF;IAImBlB,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAA0H,aAAA,CAAgB;;;;;;IAuBrCnI,EADF,CAAAE,cAAA,SAAmD,YAG7C;IAFgCF,EAAA,CAAAK,UAAA,mBAAA+H,oEAAA;MAAA,MAAAC,aAAA,GAAArI,EAAA,CAAAO,aAAA,CAAA+H,IAAA,EAAAtH,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACTF,MAAA,CAAAiG,gBAAA,CAAiB,iBAC3D,EAAA2B,aAAA,CAAAnH,KAAA,CAAkB;IAAA;IAAClB,EAAA,CAAAG,MAAA,GAAmB;IACzBH,EADyB,CAAAI,YAAA,EAAI,EACxB;;;;IADCJ,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAoB,iBAAA,CAAAiH,aAAA,CAAAhH,GAAA,CAAmB;;;;;IAf7BrB,EADF,CAAAE,cAAA,cAA8D,gBACP;IAAAF,EAAA,CAAAG,MAAA,+BAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKjFJ,EAJJ,CAAAE,cAAA,cAAsB,iBAGyE,WACrF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,aAA0E;IACxEF,EAAA,CAAA6B,UAAA,IAAA0G,gDAAA,iBAAmD;IAOzDvI,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAdMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,sCAAAD,OAAA,CAAAnB,KAAA,gCAGF;IAIsBlB,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAA+H,oBAAA,CAAuB;;;;;;IAwB/CxI,EADF,CAAAE,cAAA,SAAqD,YAG/C;IAFgCF,EAAA,CAAAK,UAAA,mBAAAoI,oEAAA;MAAA,MAAAC,cAAA,GAAA1I,EAAA,CAAAO,aAAA,CAAAoI,IAAA,EAAA3H,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACTF,MAAA,CAAAiG,gBAAA,CAAiB,kBAC5D,EAAAgC,cAAA,CAAAxH,KAAA,CAAmB;IAAA;IAAClB,EAAA,CAAAG,MAAA,GAAoB;IAC1BH,EAD0B,CAAAI,YAAA,EAAI,EACzB;;;;IADCJ,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAAoB,iBAAA,CAAAsH,cAAA,CAAArH,GAAA,CAAoB;;;;;IAf9BrB,EADF,CAAAE,cAAA,cAAoG,gBAC7C;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAK1EJ,EAJJ,CAAAE,cAAA,cAAsB,iBAG0E,WACtF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,aAA2E;IACzEF,EAAA,CAAA6B,UAAA,IAAA+G,gDAAA,iBAAqD;IAO3D5I,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAdMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,uCAAAD,OAAA,CAAAnB,KAAA,gCAGF;IAIuBlB,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAoI,qBAAA,CAAwB;;;;;;IAyBjD7I,EADF,CAAAE,cAAA,SAAmD,YAG7C;IAFgCF,EAAA,CAAAK,UAAA,mBAAAyI,oEAAA;MAAA,MAAAC,UAAA,GAAA/I,EAAA,CAAAO,aAAA,CAAAyI,IAAA,EAAAhI,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACTF,MAAA,CAAAiG,gBAAA,CAAiB,oBACxD,EAAAqC,UAAA,CAAA7H,KAAA,CAAe;IAAA;IAAClB,EAAA,CAAAG,MAAA,GAAgB;IACtBH,EADsB,CAAAI,YAAA,EAAI,EACrB;;;;IADCJ,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAoB,iBAAA,CAAA2H,UAAA,CAAA1H,GAAA,CAAgB;;;;;IAhB1BrB,EADF,CAAAE,cAAA,cAAwG,gBACjD;IAAAF,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAK7EJ,EAJJ,CAAAE,cAAA,cAAsB,iBAG4E,WACxF;IAAAF,EAAA,CAAAG,MAAA,GAIF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,aAA6E;IAC3EF,EAAA,CAAA6B,UAAA,IAAAoH,gDAAA,iBAAmD;IAOzDjJ,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAfMJ,EAAA,CAAAmB,SAAA,GAIF;IAJEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,yCAAAD,OAAA,CAAAnB,KAAA,mCAIF;IAImBlB,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAyI,uBAAA,CAA0B;;;;;;IAwB/ClJ,EADF,CAAAE,cAAA,SAA2C,YACkD;IAAvDF,EAAA,CAAAK,UAAA,mBAAA8I,oEAAA;MAAA,MAAAC,UAAA,GAAApJ,EAAA,CAAAO,aAAA,CAAA8I,IAAA,EAAArI,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,YAAY,EAAA0C,UAAA,CAAAlI,KAAA,CAAe;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GAC5E;IACjBH,EADiB,CAAAI,YAAA,EAAI,EAChB;;;;IAFwFJ,EAAA,CAAAmB,SAAA,GAC5E;IAD4EnB,EAAA,CAAAoB,iBAAA,CAAAgI,UAAA,CAAA/H,GAAA,CAC5E;;;;;IAdrBrB,EADF,CAAAE,cAAA,cAAwF,gBACjC;IAAAF,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKpEJ,EAJJ,CAAAE,cAAA,cAAsB,iBAGoE,WAChF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,aAAqE;IACnEF,EAAA,CAAA6B,UAAA,IAAAyH,gDAAA,iBAA2C;IAMjDtJ,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAbMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,iCAAAD,OAAA,CAAAnB,KAAA,0BAGF;IAImBlB,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAA8I,eAAA,CAAkB;;;;;;IAuBvCvJ,EADF,CAAAE,cAAA,SAA2C,YACkD;IAAvDF,EAAA,CAAAK,UAAA,mBAAAmJ,oEAAA;MAAA,MAAAC,YAAA,GAAAzJ,EAAA,CAAAO,aAAA,CAAAmJ,IAAA,EAAA1I,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,UAAU,EAAA+C,YAAA,CAAAvI,KAAA,CAAiB;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GAC1E;IACnBH,EADmB,CAAAI,YAAA,EAAI,EAClB;;;;IAFwFJ,EAAA,CAAAmB,SAAA,GAC1E;IAD0EnB,EAAA,CAAAoB,iBAAA,CAAAqI,YAAA,CAAApI,GAAA,CAC1E;;;;;IAdvBrB,EADF,CAAAE,cAAA,cAAwD,gBACD;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKjEJ,EAJJ,CAAAE,cAAA,cAAsB,iBAGkE,WAC9E;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,cAAmE;IACjEF,EAAA,CAAA6B,UAAA,IAAA8H,gDAAA,iBAA2C;IAMjD3J,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAbMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,+BAAAD,OAAA,CAAAnB,KAAA,uBAGF;IAIqBlB,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAmJ,aAAA,CAAgB;;;;;;IAsBvC5J,EADF,CAAAE,cAAA,SAA2C,YACwD;IAA7DF,EAAA,CAAAK,UAAA,mBAAAwJ,oEAAA;MAAA,MAAAC,YAAA,GAAA9J,EAAA,CAAAO,aAAA,CAAAwJ,IAAA,EAAA/I,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,gBAAgB,EAAAoD,YAAA,CAAA5I,KAAA,CAAiB;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GAChF;IACnBH,EADmB,CAAAI,YAAA,EAAI,EAClB;;;;IAF8FJ,EAAA,CAAAmB,SAAA,GAChF;IADgFnB,EAAA,CAAAoB,iBAAA,CAAA0I,YAAA,CAAAzI,GAAA,CAChF;;;;;IAdvBrB,EADF,CAAAE,cAAA,cAA6D,gBACN;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKxEJ,EAJJ,CAAAE,cAAA,cAAsB,kBAGwE,WACpF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,cAAyE;IACvEF,EAAA,CAAA6B,UAAA,IAAAmI,gDAAA,iBAA2C;IAMjDhK,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAbMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,qCAAAD,OAAA,CAAAnB,KAAA,8BAGF;IAIqBlB,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAwJ,aAAA,CAAgB;;;;;IAS7CjK,EADF,CAAAE,cAAA,cAA2D,gBACJ;IAAAF,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1EJ,EAAA,CAAA4B,SAAA,iBACuC;IACzC5B,EAAA,CAAAI,YAAA,EAAM;;;;;;IAmBEJ,EADF,CAAAE,cAAA,SAAqC,YACwD;IAAvDF,EAAA,CAAAK,UAAA,mBAAA6J,oEAAA;MAAA,MAAAC,SAAA,GAAAnK,EAAA,CAAAO,aAAA,CAAA6J,IAAA,EAAApJ,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,aAAa,EAAAyD,SAAA,CAAAjJ,KAAA,CAAc;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GAC7E;IAChBH,EADgB,CAAAI,YAAA,EAAI,EACf;;;;IAFwFJ,EAAA,CAAAmB,SAAA,GAC7E;IAD6EnB,EAAA,CAAAoB,iBAAA,CAAA+I,SAAA,CAAA9I,GAAA,CAC7E;;;;;IAhBpBrB,EADF,CAAAE,cAAA,cAA2D,gBACJ;IACnDF,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKJJ,EAJJ,CAAAE,cAAA,cAAsB,kBAGqE,WACjF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,cAAsE;IACpEF,EAAA,CAAA6B,UAAA,IAAAwI,gDAAA,iBAAqC;IAM3CrK,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAbMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,kCAAAD,OAAA,CAAAnB,KAAA,2BAGF;IAIkBlB,EAAA,CAAAmB,SAAA,GAAa;IAAbnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAA6J,UAAA,CAAa;;;;;;IAyBjCtK,EADF,CAAAE,cAAA,SAAmD,YACkD;IAA/DF,EAAA,CAAAK,UAAA,mBAAAkK,oEAAA;MAAA,MAAAC,aAAA,GAAAxK,EAAA,CAAAO,aAAA,CAAAkK,IAAA,EAAAzJ,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiG,gBAAA,CAAiB,iBAAiB,EAAA8D,aAAA,CAAAtJ,KAAA,CAAkB;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GACjF;IACpBH,EADoB,CAAAI,YAAA,EAAI,EACnB;;;;IAFgGJ,EAAA,CAAAmB,SAAA,GACjF;IADiFnB,EAAA,CAAAoB,iBAAA,CAAAoJ,aAAA,CAAAnJ,GAAA,CACjF;;;;;IAhBxBrB,EADF,CAAAE,cAAA,cAA8D,gBACP;IACnDF,EAAA,CAAAG,MAAA,yBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKJJ,EAJJ,CAAAE,cAAA,cAAsB,kBAGyE,WACrF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,cAA0E;IACxEF,EAAA,CAAA6B,UAAA,IAAA6I,gDAAA,iBAAmD;IAMzD1K,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAbMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAAmG,SAAA,CAAAtE,GAAA,sCAAAD,OAAA,CAAAnB,KAAA,+BAGF;IAIsBlB,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAkK,oBAAA,CAAuB;;;;;;IAsC7C3K,EADF,CAAAE,cAAA,eAA6E,iBAEsB;IAA9CF,EAAA,CAAAK,UAAA,oBAAAuK,gFAAA;MAAA5K,EAAA,CAAAO,aAAA,CAAAsK,IAAA;MAAA,MAAAC,aAAA,GAAA9K,EAAA,CAAAU,aAAA,GAAAM,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAAsK,eAAA,CAAAD,aAAA,CAAA5J,KAAA,CAAgC;IAAA,EAAC;IAD9FlB,EAAA,CAAAI,YAAA,EACiG;IACjGJ,EAAA,CAAAE,cAAA,iBAAkF;IAChFF,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAQ,EACJ;;;;;IAL4CJ,EAAA,CAAAmB,SAAA,EAAqC;IACnFnB,EAD8C,CAAAkC,UAAA,sBAAA4I,aAAA,CAAA5J,KAAA,CAAqC,YAAAT,MAAA,CAAAuK,mBAAA,CAAAF,aAAA,CAAA5J,KAAA,EACnC;IACPlB,EAAA,CAAAmB,SAAA,EAAsC;IAAtCnB,EAAA,CAAAkC,UAAA,uBAAA4I,aAAA,CAAA5J,KAAA,CAAsC;IAC/ElB,EAAA,CAAAmB,SAAA,EACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAA2H,aAAA,CAAAzJ,GAAA,MACF;;;;;IANJrB,EAAA,CAAAE,cAAA,cAAiE;IAC/DF,EAAA,CAAA6B,UAAA,IAAAoJ,uDAAA,mBAA6E;IAO/EjL,EAAA,CAAAI,YAAA,EAAK;;;;IAPsBJ,EAAA,CAAAmB,SAAA,EAAkD;IAAlDnB,EAAA,CAAAkC,UAAA,SAAA4I,aAAA,CAAA5J,KAAA,8BAAkD;;;;;;IA5BjFlB,EADF,CAAAE,cAAA,cAA+D,gBACR;IACnDF,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKvBJ,EAJJ,CAAAE,cAAA,cAAsB,kBAG6E,WACzF;IAAAF,EAAA,CAAAG,MAAA,GAEF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IAKHJ,EAJN,CAAAE,cAAA,cAAkF,cAE/D,gBACS,kBAG0B;IAA5CF,EAAA,CAAAK,UAAA,oBAAA6K,qEAAAC,MAAA;MAAAnL,EAAA,CAAAO,aAAA,CAAA6K,IAAA;MAAA,MAAA3K,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAA4K,sBAAA,CAAAF,MAAA,CAA8B;IAAA,EAAC;IAF7CnL,EAAA,CAAAI,YAAA,EAEgD;IAChDJ,EAAA,CAAAE,cAAA,kBAAkF;IAChFF,EAAA,CAAAG,MAAA,oCACF;IAEJH,EAFI,CAAAI,YAAA,EAAQ,EACJ,EACH;IAELJ,EAAA,CAAA4B,SAAA,eAAmB;IAGnB5B,EAAA,CAAA6B,UAAA,KAAAyJ,iDAAA,kBAAiE;IAWvEtL,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;IAhCMJ,EAAA,CAAAmB,SAAA,GAEF;IAFEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA8K,0BAAA,oCAEF;IAOsFvL,EAAA,CAAAmB,SAAA,GAEnF;IAFmFnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAuK,mBAAA,+BAEnF;IAUmBhL,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAA+K,qBAAA,CAAwB;;;;;IAnaxDxL,EADF,CAAAE,cAAA,cAAuD,cAC9B;IAMrBF,EALA,CAAA6B,UAAA,IAAA4J,0CAAA,kBAAgE,IAAAC,0CAAA,kBAKJ;IAK9D1L,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,cAAuB;IAqBrBF,EApBA,CAAA6B,UAAA,IAAA8J,0CAAA,mBAAuD,IAAAC,0CAAA,kBAoBG;IAK5D5L,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,cAAuB;IAMrBF,EALA,CAAA6B,UAAA,IAAAgK,0CAAA,kBAA+D,IAAAC,0CAAA,kBAKH;IAK9D9L,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,eAAuB;IAMrBF,EALA,CAAA6B,UAAA,KAAAkK,2CAAA,kBAA+D,KAAAC,2CAAA,kBAKI;IAKrEhM,EAAA,CAAAI,YAAA,EAAM;IAsUNJ,EApUA,CAAA6B,UAAA,KAAAoK,2CAAA,kBAAiE,KAAAC,2CAAA,mBAQR,KAAAC,2CAAA,kBAqCK,KAAAC,2CAAA,mBAyBC,KAAAC,2CAAA,mBAsBZ,KAAAC,2CAAA,mBAsBS,KAAAC,2CAAA,mBAsBE,KAAAC,2CAAA,mBAuBsC,KAAAC,2CAAA,mBAuBI,KAAAC,2CAAA,mBAwBhB,KAAAC,2CAAA,mBAsBhC,KAAAC,2CAAA,mBAqBK,KAAAC,2CAAA,kBAqBF,KAAAC,2CAAA,mBAOA,KAAAC,2CAAA,mBAwBG,KAAAC,2CAAA,mBAuBC;IAwCjEhN,EAAA,CAAAI,YAAA,EAAM;;;;IAhbyBJ,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAAmG,SAAA,CAAuB;IAE3B5G,EAAA,CAAAmB,SAAA,GAAuC;IAAvCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,mBAAuC;IAKvCpG,EAAA,CAAAmB,SAAA,EAAmC;IAAnCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,eAAmC;IAQnCpG,EAAA,CAAAmB,SAAA,GAA8B;IAA9BnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,UAA8B;IAoB9BpG,EAAA,CAAAmB,SAAA,EAAiC;IAAjCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,aAAiC;IAQhCpG,EAAA,CAAAmB,SAAA,GAAqC;IAArCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,iBAAqC;IAKtCpG,EAAA,CAAAmB,SAAA,EAAmC;IAAnCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,eAAmC;IAQnCpG,EAAA,CAAAmB,SAAA,GAAsC;IAAtCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,kBAAsC;IAKtCpG,EAAA,CAAAmB,SAAA,EAA0C;IAA1CnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,sBAA0C;IAO3CpG,EAAA,CAAAmB,SAAA,EAAuC;IAAvCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,mBAAuC;IAQ3CpG,EAAA,CAAAmB,SAAA,EAAmC;IAAnCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,eAAmC;IAqCnCpG,EAAA,CAAAmB,SAAA,EAAwC;IAAxCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,oBAAwC;IAyBxCpG,EAAA,CAAAmB,SAAA,EAAyC;IAAzCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,qBAAyC;IAsBzCpG,EAAA,CAAAmB,SAAA,EAA6B;IAA7BnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,SAA6B;IAsB7BpG,EAAA,CAAAmB,SAAA,EAAsC;IAAtCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,kBAAsC;IAsBtCpG,EAAA,CAAAmB,SAAA,EAAwC;IAAxCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,oBAAwC;IAuBxCpG,EAAA,CAAAmB,SAAA,EAA8E;IAA9EnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAwM,+BAAA,MAAAxM,MAAA,CAAA2F,eAAA,qBAA8E;IAuB9EpG,EAAA,CAAAmB,SAAA,EAAkF;IAAlFnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAyM,iCAAA,MAAAzM,MAAA,CAAA2F,eAAA,uBAAkF;IAwBlFpG,EAAA,CAAAmB,SAAA,EAAkE;IAAlEnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA0M,yBAAA,MAAA1M,MAAA,CAAA2F,eAAA,eAAkE;IAsBlEpG,EAAA,CAAAmB,SAAA,EAAmC;IAAnCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,aAAmC;IAqBnCpG,EAAA,CAAAmB,SAAA,EAAuC;IAAvCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,mBAAuC;IAqBvCpG,EAAA,CAAAmB,SAAA,EAAqC;IAArCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,iBAAqC;IAOrCpG,EAAA,CAAAmB,SAAA,EAAsC;IAAtCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,gBAAsC;IAwBtCpG,EAAA,CAAAmB,SAAA,EAAwC;IAAxCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,oBAAwC;IAuBxCpG,EAAA,CAAAmB,SAAA,EAAyC;IAAzCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,qBAAyC;;;;;IA6DzDpG,EAAA,CAAAE,cAAA,UAAmE;IACjEF,EAAA,CAAAG,MAAA,qDACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAPRJ,EAAA,CAAAE,cAAA,cAI6B;IAC3BF,EAAA,CAAA6B,UAAA,IAAAuL,sDAAA,iBAAmE;IAGrEpN,EAAA,CAAAI,YAAA,EAAM;;;;;IAHEJ,EAAA,CAAAmB,SAAA,EAA2D;IAA3DnB,EAAA,CAAAkC,UAAA,UAAAc,OAAA,GAAAvC,MAAA,CAAA4M,SAAA,CAAA/K,GAAA,oCAAAU,OAAA,CAAAE,MAAA,kBAAAF,OAAA,CAAAE,MAAA,cAA2D;;;;;IAfnElD,EADF,CAAAE,cAAA,cAA4D,gBACL;IACnDF,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACRJ,EAAA,CAAA4B,SAAA,iBAMO;IACP5B,EAAA,CAAA6B,UAAA,IAAAyL,gDAAA,kBAI6B;IAK/BtN,EAAA,CAAAI,YAAA,EAAM;;;;;;IAf4CJ,EAAA,CAAAmB,SAAA,GAK5C;IAL4CnB,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAyE,eAAA,IAAAC,GAAA,IAAArC,OAAA,GAAA5B,MAAA,CAAA4M,SAAA,CAAA/K,GAAA,oCAAAD,OAAA,CAAAsC,OAAA,QAAAtC,OAAA,GAAA5B,MAAA,CAAA4M,SAAA,CAAA/K,GAAA,oCAAAD,OAAA,CAAAuC,OAAA,OAAAvC,OAAA,GAAA5B,MAAA,CAAA4M,SAAA,CAAA/K,GAAA,oCAAAD,OAAA,CAAAwC,KAAA,IAK5C;IACE7E,EAAA,CAAAmB,SAAA,EAIL;IAJKnB,EAAA,CAAAkC,UAAA,WAAAc,OAAA,GAAAvC,MAAA,CAAA4M,SAAA,CAAA/K,GAAA,oCAAAU,OAAA,CAAA2B,OAAA,QAAA3B,OAAA,GAAAvC,MAAA,CAAA4M,SAAA,CAAA/K,GAAA,oCAAAU,OAAA,CAAA4B,OAAA,OAAA5B,OAAA,GAAAvC,MAAA,CAAA4M,SAAA,CAAA/K,GAAA,oCAAAU,OAAA,CAAA6B,KAAA,GAIL;;;;;;IAwBK7E,EADF,CAAAE,cAAA,SAAyC,YACwD;IAA3DF,EAAA,CAAAK,UAAA,mBAAAkN,mEAAA;MAAA,MAAAC,WAAA,GAAAxN,EAAA,CAAAO,aAAA,CAAAkN,IAAA,EAAAzM,SAAA;MAAA,MAAAP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiN,gBAAA,CAAiB,eAAe,EAAAF,WAAA,CAAAtM,KAAA,CAAgB;IAAA,EAAC;IAAClB,EAAA,CAAAG,MAAA,GAC/E;IAClBH,EADkB,CAAAI,YAAA,EAAI,EACjB;;;;IAF4FJ,EAAA,CAAAmB,SAAA,GAC/E;IAD+EnB,EAAA,CAAAoB,iBAAA,CAAAoM,WAAA,CAAAnM,GAAA,CAC/E;;;;;IAhBtBrB,EADF,CAAAE,cAAA,cAA4D,gBACL;IACnDF,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAKJJ,EAJJ,CAAAE,cAAA,cAAsB,kBAGuE,WACnF;IAAAF,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACXJ,EAAA,CAAA4B,SAAA,YAAmC;IACrC5B,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,cAAwE;IACtEF,EAAA,CAAA6B,UAAA,IAAA8L,+CAAA,iBAAyC;IAM/C3N,EAFI,CAAAI,YAAA,EAAK,EACD,EACF;;;;;IAbMJ,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoB,iBAAA,CAAAX,MAAA,CAAA+B,iBAAA,EAAAH,OAAA,GAAA5B,MAAA,CAAA4M,SAAA,CAAA/K,GAAA,oCAAAD,OAAA,CAAAnB,KAAA,6BAGF;IAIoBlB,EAAA,CAAAmB,SAAA,GAAe;IAAfnB,EAAA,CAAAkC,UAAA,YAAAzB,MAAA,CAAAmN,YAAA,CAAe;;;;;IAU7C5N,EAAA,CAAAC,uBAAA,GAAuF;IAEnFD,EADF,CAAAE,cAAA,cAAmB,gBACoC;IAAAF,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACpFJ,EAAA,CAAA4B,SAAA,iBACwD;IAC1D5B,EAAA,CAAAI,YAAA,EAAM;IAGJJ,EADF,CAAAE,cAAA,cAAmB,gBACoC;IAAAF,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAChFJ,EAAA,CAAA4B,SAAA,iBACoD;IACtD5B,EAAA,CAAAI,YAAA,EAAM;;;;;;IAYJJ,EADF,CAAAE,cAAA,cAAsE,gBACf;IAAAF,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvFJ,EAAA,CAAA4B,SAAA,iBAC2D;IAC7D5B,EAAA,CAAAI,YAAA,EAAM;;;;;IAXRJ,EAAA,CAAAC,uBAAA,GAAqG;IAEjGD,EADF,CAAAE,cAAA,cAAmB,gBACoC;IAAAF,EAAA,CAAAG,MAAA,qCAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3FJ,EAAA,CAAA4B,SAAA,iBAC+D;IACjE5B,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAA6B,UAAA,IAAAgM,yDAAA,kBAAsE;;;;;IAAlD7N,EAAA,CAAAmB,SAAA,GAAgD;IAAhDnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,4BAAgD;;;;;IAxExEpG,EAAA,CAAAE,cAAA,cAAuD;IAiErDF,EA/DA,CAAA6B,UAAA,IAAAiM,0CAAA,kBAA4D,IAAAC,0CAAA,mBAuBA,IAAAC,mDAAA,0BAyB2B,IAAAC,mDAAA,0BAec;IAavGjO,EAAA,CAAAI,YAAA,EAAM;;;;IA9EyBJ,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAA4M,SAAA,CAAuB;IAEhCrN,EAAA,CAAAmB,SAAA,EAAsC;IAAtCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,kBAAsC;IAuBtCpG,EAAA,CAAAmB,SAAA,EAAsC;IAAtCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2F,eAAA,kBAAsC;IAyB3CpG,EAAA,CAAAmB,SAAA,EAAsE;IAAtEnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAAyN,oBAAA,MAAAzN,MAAA,CAAA2F,eAAA,wBAAsE;IAetEpG,EAAA,CAAAmB,SAAA,EAAoF;IAApFnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA0N,2BAAA,MAAA1N,MAAA,CAAA2F,eAAA,+BAAoF;;;;;IA2B3FpG,EAAA,CAAAE,cAAA,gBAAwE;IACtEF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAmB,SAAA,EACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAA1C,MAAA,CAAA2N,YAAA,iBACF;;;;;IAeApO,EAAA,CAAAE,cAAA,gBAAuE;IACrEF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAmB,SAAA,EACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAA1C,MAAA,CAAA2N,YAAA,gBACF;;;;;IAeApO,EAAA,CAAAE,cAAA,gBAAuE;IACrEF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAmB,SAAA,EACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAA1C,MAAA,CAAA2N,YAAA,gBACF;;;;;IAeApO,EAAA,CAAAE,cAAA,gBAAqF;IACnFF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAmB,SAAA,EACF;IADEnB,EAAA,CAAAmD,kBAAA,MAAA1C,MAAA,CAAA2N,YAAA,8BACF;;;;;;IA1DFpO,EANR,CAAAE,cAAA,cAAuD,eAEZ,eAED,iBAC6B,eACtC;IACvBF,EAAA,CAAA4B,SAAA,aAA+B;IACjC5B,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBAA0B;IACxBF,EAAA,CAAAG,MAAA,kCACA;IAAAH,EAAA,CAAA6B,UAAA,IAAAwM,2CAAA,oBAAwE;IAG1ErO,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,iBACa;IAD2CF,EAAA,CAAAK,UAAA,oBAAAiO,6DAAAnD,MAAA;MAAAnL,EAAA,CAAAO,aAAA,CAAAgO,IAAA;MAAA,MAAA9N,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAA+N,YAAA,CAAArD,MAAA,EAAqB,SAAS,CAAC;IAAA,EAAC;IAGtGnL,EAHI,CAAAI,YAAA,EACa,EACP,EACJ;IAKFJ,EAFJ,CAAAE,cAAA,gBAAsC,kBAC0B,gBACnC;IACvBF,EAAA,CAAA4B,SAAA,cAA+B;IACjC5B,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBAA0B;IACxBF,EAAA,CAAAG,MAAA,sCACA;IAAAH,EAAA,CAAA6B,UAAA,KAAA4M,4CAAA,oBAAuE;IAGzEzO,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,kBACa;IADwCF,EAAA,CAAAK,UAAA,oBAAAqO,8DAAAvD,MAAA;MAAAnL,EAAA,CAAAO,aAAA,CAAAgO,IAAA;MAAA,MAAA9N,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAA+N,YAAA,CAAArD,MAAA,EAAqB,QAAQ,CAAC;IAAA,EAAC;IAGlGnL,EAHI,CAAAI,YAAA,EACa,EACP,EACJ;IAIFJ,EAFJ,CAAAE,cAAA,gBAAsC,kBACmB,gBAC5B;IACvBF,EAAA,CAAA4B,SAAA,cAA+B;IACjC5B,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBAA0B;IACxBF,EAAA,CAAAG,MAAA,+BAEA;IAAAH,EAAA,CAAA6B,UAAA,KAAA8M,4CAAA,oBAAuE;IAGzE3O,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,kBACa;IADiCF,EAAA,CAAAK,UAAA,oBAAAuO,8DAAAzD,MAAA;MAAAnL,EAAA,CAAAO,aAAA,CAAAgO,IAAA;MAAA,MAAA9N,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAA+N,YAAA,CAAArD,MAAA,EAAqB,QAAQ,CAAC;IAAA,EAAC;IAG3FnL,EAHI,CAAAI,YAAA,EACa,EACP,EACJ;IAKFJ,EAFJ,CAAAE,cAAA,gBAAsC,kBACiC,gBAC1C;IACvBF,EAAA,CAAA4B,SAAA,cAA+B;IACjC5B,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,iBAA0B;IACxBF,EAAA,CAAAG,MAAA,0BACA;IAAAH,EAAA,CAAA6B,UAAA,KAAAgN,4CAAA,oBAAqF;IAGvF7O,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,kBACqE;IAAnEF,EAAA,CAAAK,UAAA,oBAAAyO,8DAAA3D,MAAA;MAAAnL,EAAA,CAAAO,aAAA,CAAAgO,IAAA;MAAA,MAAA9N,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAUF,MAAA,CAAA+N,YAAA,CAAArD,MAAA,EAAqB,sBAAsB,CAAC;IAAA,EAAC;IAIjEnL,EALQ,CAAAI,YAAA,EACqE,EAC/D,EACJ,EACF,EACF;;;;IAvEyBJ,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAAsO,SAAA,CAAuB;IAWrC/O,EAAA,CAAAmB,SAAA,GAAiC;IAAjCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2N,YAAA,gBAAiC;IAiBjCpO,EAAA,CAAAmB,SAAA,GAAgC;IAAhCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2N,YAAA,eAAgC;IAiBhCpO,EAAA,CAAAmB,SAAA,GAAgC;IAAhCnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2N,YAAA,eAAgC;IAiBhCpO,EAAA,CAAAmB,SAAA,GAA8C;IAA9CnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA2N,YAAA,6BAA8C;;;;;;IAc3DpO,EAFJ,CAAAE,cAAA,cAAuD,eACvB,eACV;IAChBF,EAAA,CAAA4B,SAAA,aAAyE;IAC3E5B,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAAiB;IAAAF,EAAA,CAAAG,MAAA,+BAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9CJ,EAAA,CAAAE,cAAA,aAA2B;IACzBF,EAAA,CAAAG,MAAA,+FACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEFJ,EADF,CAAAE,cAAA,eAAyC,aAC/B;IAAAF,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAEpBJ,EADF,CAAAE,cAAA,eAAoC,UAC9B;IAAAF,EAAA,CAAAG,MAAA,2DAA8C;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvDJ,EAAA,CAAAE,cAAA,UAAI;IAAAF,EAAA,CAAAG,MAAA,oEAAuD;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChEJ,EAAA,CAAAE,cAAA,UAAI;IAAAF,EAAA,CAAAG,MAAA,yEAA4D;IAEpEH,EAFoE,CAAAI,YAAA,EAAK,EAClE,EACD;IAIJJ,EADF,CAAAE,cAAA,gBAA4C,mBAEZ;IAA5BF,EAAA,CAAAK,UAAA,mBAAA2O,8DAAA;MAAAhP,EAAA,CAAAO,aAAA,CAAA0O,IAAA;MAAA,MAAAxO,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyO,UAAA,CAAW,KAAK,CAAC;IAAA,EAAC;IAC3BlP,EAAA,CAAAE,cAAA,iBAA8B;IAACF,EAAA,CAAAG,MAAA,qBAAY;IAC7CH,EAD6C,CAAAI,YAAA,EAAO,EAC3C;IAETJ,EAAA,CAAAE,cAAA,mBAC6B;IAA3BF,EAAA,CAAAK,UAAA,mBAAA8O,8DAAA;MAAAnP,EAAA,CAAAO,aAAA,CAAA0O,IAAA;MAAA,MAAAxO,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyO,UAAA,CAAW,IAAI,CAAC;IAAA,EAAC;IAC1BlP,EAAA,CAAAE,cAAA,iBAA8B;IAC5BF,EAAA,CAAA4B,SAAA,cAAoC;IAAC5B,EAAA,CAAAG,MAAA,gCACvC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,iBAAiC;IAC/BF,EAAA,CAAAG,MAAA,wBACA;IAAAH,EAAA,CAAA4B,SAAA,iBAAwE;IAKlF5B,EAJQ,CAAAI,YAAA,EAAO,EACA,EACL,EACF,EACF;;;;IArCyBJ,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAA2O,SAAA,CAAuB;IAoBqBpP,EAAA,CAAAmB,SAAA,IAAkC;IAAlCnB,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAA4O,kBAAA,GAAkC;IAKlCrP,EAAA,CAAAmB,SAAA,GAA6B;IAA7BnB,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAA2O,SAAA,CAAAE,KAAA,CAA6B;;;;;;IAkBtGtP,EADF,CAAAE,cAAA,eAA4E,kBACW;IAAnBF,EAAA,CAAAK,UAAA,mBAAAkP,6DAAA;MAAAvP,EAAA,CAAAO,aAAA,CAAAiP,IAAA;MAAA,MAAA/O,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgP,MAAA,EAAQ;IAAA,EAAC;IAClFzP,EAAA,CAAAG,MAAA,eACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,kBACuB;IAArBF,EAAA,CAAAK,UAAA,mBAAAqP,6DAAA;MAAA1P,EAAA,CAAAO,aAAA,CAAAiP,IAAA;MAAA,MAAA/O,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkP,QAAA,EAAU;IAAA,EAAC;IACpB3P,EAAA,CAAAE,cAAA,gBAAyC;IAACF,EAAA,CAAAG,MAAA,oCAA4B;IAE1EH,EAF0E,CAAAI,YAAA,EAAO,EACtE,EACL;;;;IAJsEJ,EAAA,CAAAmB,SAAA,GAAkC;IAAlCnB,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAA4O,kBAAA,GAAkC;;;;;;IAQ5GrP,EADF,CAAAE,cAAA,eAA2E,kBAElD;IAArBF,EAAA,CAAAK,UAAA,mBAAAuP,6DAAA;MAAA5P,EAAA,CAAAO,aAAA,CAAAsP,IAAA;MAAA,MAAApP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkP,QAAA,EAAU;IAAA,EAAC;IACpB3P,EAAA,CAAAE,cAAA,gBAAyC;IAACF,EAAA,CAAAG,MAAA,yBAAiB;IAE/DH,EAF+D,CAAAI,YAAA,EAAO,EAC3D,EACL;;;;IAJsEJ,EAAA,CAAAmB,SAAA,EAAkC;IAAlCnB,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAA4O,kBAAA,GAAkC;;;;;IAWxGrP,EAAA,CAAAC,uBAAA,GAAwC;IACtCD,EAAA,CAAAG,MAAA,+BACF;;;;;;IACAH,EAAA,CAAAC,uBAAA,GAAwC;IACtCD,EAAA,CAAAG,MAAA,iCACF;;;;;;IACAH,EAAA,CAAAC,uBAAA,GAAwC;IACtCD,EAAA,CAAAG,MAAA,+BACF;;;;;;;IAXJH,EADF,CAAAE,cAAA,eAAuG,kBAE9E;IAArBF,EAAA,CAAAK,UAAA,mBAAAyP,6DAAA;MAAA9P,EAAA,CAAAO,aAAA,CAAAwP,IAAA;MAAA,MAAAtP,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkP,QAAA,EAAU;IAAA,EAAC;IACpB3P,EAAA,CAAAE,cAAA,gBAAyC;IAOvCF,EANA,CAAA6B,UAAA,IAAAmO,mDAAA,0BAAwC,IAAAC,mDAAA,0BAGA,IAAAC,mDAAA,0BAGA;IAK9ClQ,EAFI,CAAAI,YAAA,EAAO,EACA,EACL;;;;IAdsEJ,EAAA,CAAAmB,SAAA,EAAkC;IAAlCnB,EAAA,CAAAkC,UAAA,cAAAzB,MAAA,CAAA4O,kBAAA,GAAkC;IAGzFrP,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA0P,WAAA,OAAuB;IAGvBnQ,EAAA,CAAAmB,SAAA,EAAuB;IAAvBnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA0P,WAAA,OAAuB;IAGvBnQ,EAAA,CAAAmB,SAAA,EAAuB;IAAvBnB,EAAA,CAAAkC,UAAA,SAAAzB,MAAA,CAAA0P,WAAA,OAAuB;;;AD9gCpD;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,OAAM,MAAOC,oBAAoB;EA4QrBC,EAAA;EACAC,MAAA;EACAC,eAAA;EACAC,GAAA;EA9QVC,UAAU,GAAG,CAAC;EACdN,WAAW,GAAG,CAAC;EACfO,cAAc;EACdtN,gBAAgB;EAChB6C,gBAAgB;EAChBC,mBAAmB;EACnByK,gBAAgB;EAChB/M,MAAM,GAAU,EAAE;EAClBgN,SAAS,GAAqC,EAAE;EAChD1M,KAAK,GAAU,EAAE;EACjBiC,QAAQ,GAAU,EAAE;EACpBJ,eAAe,GAAG,KAAK;EAEvB8K,oBAAoB,GAAG,CACvB;IAAExP,GAAG,EAAE,QAAQ;IAAEH,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAEG,GAAG,EAAE,WAAW;IAAEH,KAAK,EAAE;EAAW,CAAE,EACxC;IAAEG,GAAG,EAAE,MAAM;IAAEH,KAAK,EAAE;EAAM,CAAE,EAC9B;IAAEG,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE;EAAS,CAAE,EACpC;IAAEG,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,eAAe;IAAEH,KAAK,EAAE;EAAe,CAAE,EAChD;IAAEG,GAAG,EAAE,eAAe;IAAEH,KAAK,EAAE;EAA4B,CAAE,CAC9D;EAGC;EACA4P,QAAQ;EAER3I,aAAa,GAAqC,CAChD;IAAE9G,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,eAAe;IAAEH,KAAK,EAAE;EAAe,CAAE,EAChD;IAAEG,GAAG,EAAE,kBAAkB;IAAEH,KAAK,EAAE;EAAkB,CAAE,EACtD;IAAEG,GAAG,EAAE,WAAW;IAAEH,KAAK,EAAE;EAAW,CAAE,EACxC;IAAEG,GAAG,EAAE,iBAAiB;IAAEH,KAAK,EAAE;EAAiB,CAAE,CACrD;EAED2F,UAAU,GAAqC,CAC7C;IAAExF,GAAG,EAAE,QAAQ;IAAEH,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAEG,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,kBAAkB;IAAEH,KAAK,EAAE;EAA4B,CAAE,CACjE;EAED4G,SAAS,GAAqC,CAC5C;IAAEzG,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,uBAAuB;IAAEH,KAAK,EAAE;EAAuB,CAAE,EAChE;IAAEG,GAAG,EAAE,QAAQ;IAAEH,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAEG,GAAG,EAAE,oBAAoB;IAAEH,KAAK,EAAE;EAAoB,CAAE,EAC1D;IAAEG,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAQ,CAAE,EACpC;IAAEG,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAc,CAAE,EAC7C;IAAEG,GAAG,EAAE,QAAQ;IAAEH,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAEG,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAEG,GAAG,EAAE,WAAW;IAAEH,KAAK,EAAE;EAAW,CAAE,CACzC;EAED+I,aAAa,GAAqC,CAChD;IAAE5I,GAAG,EAAE,oBAAoB;IAAEH,KAAK,EAAE;EAAoB,CAAE,EAC1D;IAAEG,GAAG,EAAE,oBAAoB;IAAEH,KAAK,EAAE;EAAoB,CAAE,CAC3D;EAED0I,aAAa,GAAqC,CAChD;IAAEvI,GAAG,EAAE,qBAAqB;IAAEH,KAAK,EAAE;EAAqB,CAAE,EAC5D;IAAEG,GAAG,EAAE,iBAAiB;IAAEH,KAAK,EAAE;EAAiB,CAAE,EACpD;IAAEG,GAAG,EAAE,cAAc;IAAEH,KAAK,EAAE;EAAc,CAAE,EAC9C;IACEG,GAAG,EAAE,+BAA+B;IACpCH,KAAK,EAAE;GACR,EACD;IACEG,GAAG,EAAE,uCAAuC;IAC5CH,KAAK,EAAE;GACR,CACF;EAEDsH,oBAAoB,GAAqC,CACvD;IAAEnH,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,cAAc;IAAEH,KAAK,EAAE;EAAc,CAAE,EAC9C;IAAEG,GAAG,EAAE,4BAA4B;IAAEH,KAAK,EAAE;EAA4B,CAAE,CAC3E;EAED2H,qBAAqB,GAAqC,CACxD;IAAExH,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAa,CAAE,EAC5C;IACEG,GAAG,EAAE,iCAAiC;IACtCH,KAAK,EAAE;GACR,EACD;IACEG,GAAG,EAAE,oCAAoC;IACzCH,KAAK,EAAE;GACR,CACF;EAEDgI,uBAAuB,GAAqC,CAC1D;IAAE7H,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAEG,GAAG,EAAE,oBAAoB;IAAEH,KAAK,EAAE;EAAoB,CAAE,EAC1D;IAAEG,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAEG,GAAG,EAAE,gBAAgB;IAAEH,KAAK,EAAE;EAAgB,CAAE,CACnD;EAEDqI,eAAe,GAAqC,CAClD;IAAElI,GAAG,EAAE,eAAe;IAAEH,KAAK,EAAE;EAAe,CAAE,EAChD;IAAEG,GAAG,EAAE,yBAAyB;IAAEH,KAAK,EAAE;EAAyB,CAAE,EACpE;IAAEG,GAAG,EAAE,0BAA0B;IAAEH,KAAK,EAAE;EAA0B,CAAE,EACtE;IACEG,GAAG,EAAE,oCAAoC;IACzCH,KAAK,EAAE;GACR,EACD;IAAEG,GAAG,EAAE,gBAAgB;IAAEH,KAAK,EAAE;EAAgB,CAAE,CACnD;EAEDsK,qBAAqB,GAAqC,CACxD;IAAEnK,GAAG,EAAE,QAAQ;IAAEH,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAEG,GAAG,EAAE,WAAW;IAAEH,KAAK,EAAE;EAAW,CAAE,EACxC;IAAEG,GAAG,EAAE,MAAM;IAAEH,KAAK,EAAE;EAAM,CAAE,EAC9B;IAAEG,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE;EAAS,CAAE,EACpC;IAAEG,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,eAAe;IAAEH,KAAK,EAAE;EAAe,CAAE,CACjD;EAED6P,mBAAmB,GAAa,EAAE;EAElCnD,YAAY,GAAqC,CAC/C;IAAEvM,GAAG,EAAE,MAAM;IAAEH,KAAK,EAAE;EAAM,CAAE,EAC9B;IAAEG,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAa,CAAE,EAC5C;IACEG,GAAG,EAAE,gCAAgC;IACrCH,KAAK,EAAE;GACR,CACF;EAEDoJ,UAAU,GAAqC,CAC7C;IAAEjJ,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,yBAAyB;IAAEH,KAAK,EAAE;EAAyB,CAAE,CACrE;EAEDyJ,oBAAoB,GAAqC,CACvD;IAAEtJ,GAAG,EAAE,eAAe;IAAEH,KAAK,EAAE;EAAc,CAAE,EAC/C;IAAEG,GAAG,EAAE,6CAA6C;IAAEH,KAAK,EAAE;EAA4C,CAAE,CAE5G;EAEDuG,qBAAqB,GAAqC,CACxD;IAAEpG,GAAG,EAAE,sBAAsB;IAAEH,KAAK,EAAE;EAAsB,CAAE,EAC9D;IAAEG,GAAG,EAAE,iBAAiB;IAAEH,KAAK,EAAE;EAAiB,CAAE,CACrD;EAED;EACAqB,eAAe,GAAqC,CAClD;IAAElB,GAAG,EAAE,kBAAkB;IAAEH,KAAK,EAAE;EAAkB,CAAE,EACtD;IAAEG,GAAG,EAAE,iBAAiB;IAAEH,KAAK,EAAE;EAAiB,CAAE,EACpD;IAAEG,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE;EAAS,CAAC,CACpC;EAED8P,mBAAmB,GAAqC,CACtD;IAAE3P,GAAG,EAAE,MAAM;IAAEH,KAAK,EAAE;EAAM,CAAE,EAC9B;IAAEG,GAAG,EAAE,MAAM;IAAEH,KAAK,EAAE;EAAU,CAAE,CACnC;EAGD;EACA+P,YAAY,GAAqC,EAAE;EAEnD;EACAC,wBAAwB,GAAqC;EAC3D;EACA;IAAE7P,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE;EAAS,CAAE,EACpC;IAAEG,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,OAAO;IAAEH,KAAK,EAAE;EAAO,CAAE;EAEhC;EACA;IAAEG,GAAG,EAAE,QAAQ;IAAEH,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAEG,GAAG,EAAE,gBAAgB;IAAEH,KAAK,EAAE;EAAgB,CAAE;EAGlD;EACA;IAAEG,GAAG,EAAE,sBAAsB;IAAEH,KAAK,EAAE;EAAsB,CAAE,EAC9D;IAAEG,GAAG,EAAE,iBAAiB;IAAEH,KAAK,EAAE;EAAiB,CAAE,EACpD;IAAEG,GAAG,EAAE,mBAAmB;IAAEH,KAAK,EAAE;EAAmB,CAAE,EACxD;IAAEG,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE;EAG1C;EACA;IAAEG,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,WAAW;IAAEH,KAAK,EAAE;EAAW,CAAE;EAGxC;EACA;IAAEG,GAAG,EAAE,yBAAyB;IAAEH,KAAK,EAAE;EAAyB,CAAE,EACpE;IAAEG,GAAG,EAAE,sBAAsB;IAAEH,KAAK,EAAE;EAAsB,CAAE,EAC9D;IAAEG,GAAG,EAAE,mBAAmB;IAAEH,KAAK,EAAE;EAAmB,CAAE,EACxD;IAAEG,GAAG,EAAE,iCAAiC;IAAEH,KAAK,EAAE;EAAiC,CAAE,EACpF;IAAEG,GAAG,EAAE,eAAe;IAAEH,KAAK,EAAE;EAAe,CAAE,EAChD;IAAEG,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAEG,GAAG,EAAE,iBAAiB;IAAEH,KAAK,EAAE;EAAiB,CAAE,EACpD;IAAEG,GAAG,EAAE,eAAe;IAAEH,KAAK,EAAE;EAAe,CAAE,CACjD;EAEAiQ,uBAAuB,GAAqC;EAC3D;EACA;IAAE9P,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE;EAAS,CAAE,EACpC;IAAEG,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE;EAAS,CAAE;EAEpC;EACA;IAAEG,GAAG,EAAE,mBAAmB;IAAEH,KAAK,EAAE;EAAmB,CAAE,EACxD;IAAEG,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAEG,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAa;EAG1C;EACA;EACA;EACA;EACA;EAAA,CAIA;EACDkQ,eAAe,GAAqC;EACnD;EACE;IAAE/P,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE;EAAS,CAAE,EACpC;IAAEG,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE;EAAU,CAAE,EACtC;IAAEG,GAAG,EAAE,OAAO;IAAEH,KAAK,EAAE;EAAO,CAAE;EAEhC;EACA;IAAEG,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE;EAAS,CAAE,EACpC;IAAEG,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAEG,GAAG,EAAE,aAAa;IAAEH,KAAK,EAAE;EAAa,CAAE,EAC5C;IAAEG,GAAG,EAAE,mBAAmB;IAAEH,KAAK,EAAE;EAAmB,CAAE,EACxD;IAAEG,GAAG,EAAE,QAAQ;IAAEH,KAAK,EAAE;EAAQ,CAAE,EAClC;IAAEG,GAAG,EAAE,gBAAgB;IAAEH,KAAK,EAAE;EAAgB,CAAE;EAElD;EACA;IAAEG,GAAG,EAAE,sBAAsB;IAAEH,KAAK,EAAE;EAAsB,CAAE,EAC9D;IAAEG,GAAG,EAAE,iBAAiB;IAAEH,KAAK,EAAE;EAAiB,CAAE,EACpD;IAAEG,GAAG,EAAE,mBAAmB;IAAEH,KAAK,EAAE;EAAmB,CAAE,EACxD;IAAEG,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE;EAE1C;EACA;IAAEG,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE;EAAS,CAAE;EAErC;EACC;IAAEG,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE;EAAY,CAAE,EAC1C;IAAEG,GAAG,EAAE,WAAW;IAAEH,KAAK,EAAE;EAAW,CAAE,CAE1C;EAGF;EACAyB,iBAAiB,GAAqC,EAAE;EAExDR,SAAS;EACTc,SAAS;EACT2D,SAAS;EACTyG,SAAS;EACT0B,SAAS;EACTK,SAAS;EAETiC,YACUhB,EAAe,EACfC,MAAc,EACdC,eAAgC,EAChCC,GAAsB;IAHtB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,GAAG,GAAHA,GAAG;EACV;EAEHc,QAAQA,CAAA;IACN,MAAMC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAChD,IAAI,CAACX,QAAQ,GAAGS,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,CAACT,QAAQ,GAAG,IAAI;IACvD,IAAI,CAACc,SAAS,EAAE;IAChB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,SAAS,EAAE;IAEf,IAAI,CAACpP,iBAAiB,GAAG,EAAE;EAC9B;EAEAiP,SAASA,CAAA;IACP;IACA,IAAI,CAACzP,SAAS,GAAG,IAAI,CAACkO,EAAE,CAAC2B,KAAK,CAAC;MAC7BC,YAAY,EAAE,CAAC,EAAE,EAAE,CAACnS,UAAU,CAACoS,QAAQ,CAAC,CAAC;MACzCC,YAAY,EAAE,CAAC,EAAE,EAAE,CAACrS,UAAU,CAACoS,QAAQ,CAAC,CAAC;MACzCE,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtS,UAAU,CAACoS,QAAQ,CAAC;KACjC,CAAC;IAEF;IACA,IAAI,CAACjP,SAAS,GAAG,IAAI,CAACoN,EAAE,CAAC2B,KAAK,CAAC;MAC7BK,MAAM,EAAE,CAAC,EAAE,EAAE,CAACvS,UAAU,CAACoS,QAAQ,CAAC,CAAC;MACnCI,MAAM,EAAE,CAAC,EAAE,EAAE,CAACxS,UAAU,CAACoS,QAAQ,CAAC,CAAC;MACnCK,SAAS,EAAE,CAAC,EAAE,CAAC;MAAE;MACjBC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC1S,UAAU,CAAC2S,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3CC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC5S,UAAU,CAAC2S,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/CE,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC7S,UAAU,CAACoS,QAAQ,EAAEpS,UAAU,CAAC2S,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACvEG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAE9S,UAAU,CAAC+S,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACpDC,SAAS,EAAE,CAAC,EAAE,EAAEhT,UAAU,CAACoS,QAAQ,CAAC;MACpCa,UAAU,EAAE,CACV,EAAE,EACF,CAACjT,UAAU,CAACoS,QAAQ,EAAEpS,UAAU,CAAC+S,OAAO,CAAC,wBAAwB,CAAC,CAAC;KAEtE,CAAC;IAEF;IACA,IAAI,CAACjM,SAAS,GAAG,IAAI,CAACyJ,EAAE,CAAC2B,KAAK,CAAC;MAC7BgB,cAAc,EAAE,CAAC,EAAE,EAAE,CAAClT,UAAU,CAAC2S,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAChDQ,UAAU,EAAE,CAAC,EAAE,EAAE,CAACnT,UAAU,CAAC2S,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5CS,KAAK,EAAE,CAAC,EAAE,EAAE,CAACpT,UAAU,CAACoS,QAAQ,CAAC,CAAC;MAClCiB,QAAQ,EAAE,CACR,EAAE,EACF,CACErT,UAAU,CAACoS,QAAQ,EACnBpS,UAAU,CAACsT,GAAG,CAAC,CAAC,CAAC,EACjBtT,UAAU,CAAC+S,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDQ,YAAY,EAAE,CAAC,EAAE,EAAE,CAACvT,UAAU,CAACsT,GAAG,CAAC,CAAC,CAAC,EAAEtT,UAAU,CAAC+S,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;MACvES,UAAU,EAAE,CAAC,EAAE,EAAE,CAACxT,UAAU,CAACsT,GAAG,CAAC,CAAC,CAAC,EAAEtT,UAAU,CAAC+S,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;MACrEU,aAAa,EAAE,CACb,EAAE,EACF,CACEzT,UAAU,CAACoS,QAAQ,EACnBpS,UAAU,CAACsT,GAAG,CAAC,CAAC,CAAC,EACjBtT,UAAU,CAAC+S,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDW,iBAAiB,EAAE,CACjB,EAAE,EACF,CACE1T,UAAU,CAACoS,QAAQ,EACnBpS,UAAU,CAACsT,GAAG,CAAC,CAAC,CAAC,EACjBtT,UAAU,CAAC+S,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDY,cAAc,EAAE,CACd,EAAE,EACF,CACE3T,UAAU,CAACoS,QAAQ,EACnBpS,UAAU,CAACsT,GAAG,CAAC,CAAC,CAAC,EACjBtT,UAAU,CAAC+S,OAAO,CAAC,UAAU,CAAC,CAC/B,CACF;MACDa,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC7T,UAAU,CAACoS,QAAQ,CAAC,CAAC;MACjC/J,aAAa,EAAE,CAAC,EAAE,EAAE,CAACrI,UAAU,CAACoS,QAAQ,CAAC,CAAC;MAC1C0B,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC,EAAE,EAAE,CAACnU,UAAU,CAACoS,QAAQ,CAAC,CAAC;MAC3CgC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,gBAAgB,EAAE,CAAC,EAAE,CAAC;MAAE;MACxBC,eAAe,EAAE,CAAC,EAAE,CAAC;MAAE;MACvBC,gBAAgB,EAAE,CAAC,EAAE,CAAC;MAAE;MACxBC,WAAW,EAAE,CAAC,EAAE,CAAC;MAAE;MACnBC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACzU,UAAU,CAAC2S,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAE;KACtD,CAAC;IAEF;IACA,IAAI,CAACpF,SAAS,GAAG,IAAI,CAACgD,EAAE,CAAC2B,KAAK,CAAC;MAC7BwC,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC1U,UAAU,CAAC2S,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAAE;MAClDgC,aAAa,EAAE,CAAC,EAAE,EAAE3U,UAAU,CAACoS,QAAQ,CAAC;MACxCwC,0BAA0B,EAAE,CAAC,EAAE,EAAE,CAAC5U,UAAU,CAACsT,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDuB,uBAAuB,EAAE,CAAC,EAAE,EAAE,CAAC7U,UAAU,CAACsT,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClDwB,mBAAmB,EAAE,CAAC,EAAE,EAAE,CAAC9U,UAAU,CAACsT,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9CyB,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC/U,UAAU,CAACsT,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC;IAEF;IACA,IAAI,CAACrE,SAAS,GAAG,IAAI,CAACsB,EAAE,CAAC2B,KAAK,CAAC;MAC7B8C,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,oBAAoB,EAAE,CAAC,EAAE;KAC1B,CAAC;IAEF;IACA,IAAI,CAAC7F,SAAS,GAAG,IAAI,CAACiB,EAAE,CAAC2B,KAAK,CAAC;MAC7B;IAAA,CACD,CAAC;EACJ;EAEA;EACAkD,cAAcA,CAAA;IACZ,QAAQ,IAAI,CAAC/E,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAAChO,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACc,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC2D,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACyG,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC0B,SAAS;MACvB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACK,SAAS;MACvB;QACE,OAAO,IAAI,CAACjN,SAAS;IACzB;EACF;EAEA0P,aAAaA,CAAA;IACX,IAAI,CAACtB,eAAe,CAAC4E,YAAY,EAAE,CAACC,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACrE,YAAY,GAAGsE,MAAM,CAACC,OAAO,CAACF,QAAQ,CAACG,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACrU,GAAG,EAAEH,KAAK,CAAC,MAAM;UACvEG,GAAG;UACHH,KAAK,EAAEA;SACR,CAAC,CAAC;QACH,IAAI,CAACiQ,uBAAuB,GAAG,IAAI,CAACF,YAAY,CAAC,CAAC;QAClD0E,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC3E,YAAY,CAAC;MACrD,CAAC;MACD4E,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEC,GAAG,CAAC;MAChD,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACvF,GAAG,CAACwF,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEAC,eAAeA,CAAA;IACb,MAAMhE,YAAY,GAAG,IAAI,CAAC9P,SAAS,CAACG,GAAG,CAAC,cAAc,CAAC,EAAEpB,KAAK;IAE9D,IAAI+Q,YAAY,KAAK,kBAAkB,EAAE;MACvC,IAAI,CAACtP,iBAAiB,GAAG,IAAI,CAACuO,wBAAwB;IACxD,CAAC,MAAM,IAAIe,YAAY,KAAK,iBAAiB,EAAE;MAC7C,IAAI,CAACtP,iBAAiB,GAAG,IAAI,CAACwO,uBAAuB;IACvD,CAAC,MAAM,IAAIc,YAAY,KAAK,SAAS,EAAE;MACrC;MACA,IAAI,CAACtP,iBAAiB,GAAG,IAAI,CAACyO,eAAe;IAC/C,CAAC,MAAM;MACL,IAAI,CAACzO,iBAAiB,GAAG,EAAE;IAC7B;IAEA,IAAI,CAACR,SAAS,CAAC+T,UAAU,CAAC;MAAE9D,IAAI,EAAE;IAAE,CAAE,CAAC;IACvC,IAAI,CAACzB,gBAAgB,GAAG,EAAE;IAE1B,IAAI,CAACH,GAAG,CAACwF,aAAa,EAAE;EAC1B;EAEAlE,UAAUA,CAAA;IACR,IAAI,CAAC/L,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACwK,eAAe,CAAC4F,SAAS,EAAE,CAACf,SAAS,CAAC;MACzCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,EAAE;UAC7B,IAAI,CAAC7R,MAAM,GAAG0R,QAAQ,CAACG,IAAI;QAC7B,CAAC,MAAM;UACLE,OAAO,CAACS,IAAI,CAAC,4BAA4B,CAAC;UAC1C,IAAI,CAACxS,MAAM,GAAG,EAAE;QAClB;MACF,CAAC;MACDiS,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;MAC7C,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAChQ,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACyK,GAAG,CAACwF,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEAjE,SAASA,CAACM,MAAe;IACvB,IAAI,CAAC9B,eAAe,CAAC8F,QAAQ,CAAChE,MAAM,CAAC,CAAC+C,SAAS,CAAC;MAC9CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,EAAE;UAC7B,IAAI,CAACvR,KAAK,GAAGoR,QAAQ,CAACG,IAAI;QAC5B,CAAC,MAAM;UACLE,OAAO,CAACS,IAAI,CAAC,2BAA2B,CAAC;UACzC,IAAI,CAAClS,KAAK,GAAG,EAAE;QACjB;MACF,CAAC;MACD2R,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAAC5R,KAAK,GAAG,EAAE;MACjB,CAAC;MACD6R,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACvF,GAAG,CAACwF,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEAM,YAAYA,CAAChE,MAAe;IAC1B,IAAI,CAAC/B,eAAe,CAACgG,WAAW,CAACjE,MAAM,CAAC,CAAC8C,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,EAAE;UAC7B,IAAI,CAACtP,QAAQ,GAAGmP,QAAQ,CAACG,IAAI;QAC/B,CAAC,MAAM;UACLE,OAAO,CAACS,IAAI,CAAC,+BAA+B,CAAC;UAC7C,IAAI,CAACjQ,QAAQ,GAAG,EAAE;QACpB;MACF,CAAC;MACD0P,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEC,GAAG,CAAC;QAC9C,IAAI,CAAC3P,QAAQ,GAAG,EAAE;MACpB,CAAC;MACD4P,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACvF,GAAG,CAACwF,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEA;EAEA;EACAtT,mBAAmBA,CAAA;IACnB,MAAMuP,YAAY,GAAG,IAAI,CAAC9P,SAAS,CAACG,GAAG,CAAC,cAAc,CAAC,EAAEpB,KAAK;IAE9D,IAAI+Q,YAAY,KAAK,SAAS,EAAE;MAC9B;MACA,OAAO,IAAI,CAACjB,mBAAmB,CAACwF,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACvV,KAAK,KAAK,UAAU,CAAC;IAC/E;IAEA;IACA,OAAO,IAAI,CAAC8P,mBAAmB;EACjC;EAGE;EAEF;EACA0F,eAAeA,CAAA;IACb,MAAMzE,YAAY,GAAG,IAAI,CAAC9P,SAAS,CAACG,GAAG,CAAC,cAAc,CAAC,EAAEpB,KAAK;IAC9D,MAAMkR,IAAI,GAAG,IAAI,CAACjQ,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;IAChDyU,OAAO,CAACC,GAAG,CAAC3D,YAAY,EAAEG,IAAI,CAAC;IAC/BuD,OAAO,CAACC,GAAG,CAAC3D,YAAY,KAAK,kBAAkB,KAAMG,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,gBAAgB,CAAC,CAAE;IAClG;IACA,IAAIH,YAAY,KAAK,kBAAkB,KAAMG,IAAI,KAAK,YAAY,IAAGA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAGA,IAAI,KAAK,OAAO,IAAGA,IAAI,KAAK,UAAU,CAAC,EAAE;MACjL,OAAO,CAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAG,mBAAmB,EAAG,YAAY,EAAE,MAAM,EAAE,eAAe,EAAC,gBAAgB,EAAI,aAAa,EAAE,kBAAkB,EAAK,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAE;IAC7T,CAAC,MACI,IAAIH,YAAY,KAAK,kBAAkB,KAAMG,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,gBAAgB,CAAC,EAAE;MAC/F,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IAC3S,CAAC,MACI,IAAIH,YAAY,KAAK,kBAAkB,KAAMG,IAAI,KAAK,YAAY,IAAGA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,mBAAmB,CAAE,EAAE;MAC1K,OAAO,CAAC,UAAU,EAAG,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAK,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAC,gBAAgB,EAAC,UAAU,EAAE,iBAAiB,EAAE,kBAAkB,EAAG,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAE;IACnT,CAAC,MACI,IAAIH,YAAY,KAAK,kBAAkB,KAAMG,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW,CAAE,EAAE;MACnG,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,iBAAiB,EAAE,aAAa,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACjR,CAAC,MACI,IAAIH,YAAY,KAAK,kBAAkB,KAAMG,IAAI,KAAK,yBAAyB,IAAIA,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,sBAAsB,IAAGA,IAAI,KAAK,iCAAiC,IAAIA,IAAI,KAAK,kBAAkB,IAAIA,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,aAAa,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,kBAAkB,CAAE,EAAE;MAC1V,OAAO,CAAE,YAAY,EAAC,YAAY,EAAC,iBAAiB,EAAE,iBAAiB,EAAC,kBAAkB,EAAC,MAAM,EAAC,aAAa,EAAC,gBAAgB,EAAC,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAE;IAC7R,CAAC,MAEI,IAAIH,YAAY,KAAK,iBAAiB,KAAMG,IAAI,KAAK,YAAY,IAAGA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,YAAY,IAAGA,IAAI,KAAK,SAAS,CAAE,EAAE;MACpK,OAAO,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAG,mBAAmB,EAAG,MAAM,EAAE,eAAe,EAAC,gBAAgB,EAAI,iBAAiB,EAAE,kBAAkB,EAAG,eAAe,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAE;IAC/U,CAAC,MACI,IAAIH,YAAY,KAAK,iBAAiB,KAAMG,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAI,aAAa,IAAIA,IAAI,KAAK,aAAa,CAAC,EAAE;MACnI,OAAO,CAAC,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACvV,CAAC,MAEI,IAAIH,YAAY,KAAK,iBAAiB,KAAMG,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,iBAAiB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,mBAAmB,CAAE,EAAE;MAC7K,OAAO,CAAC,cAAc,EAAE,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAC,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAC,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,eAAe,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,4BAA4B,EAAE,yBAAyB,CAAC;IACjU;IAEE,OAAO,EAAG;EACZ;EAEA;EACAhM,eAAeA,CAACuQ,SAAiB;IAC9B,OAAO,IAAI,CAACD,eAAe,EAAE,CAACE,QAAQ,CAACD,SAAS,CAAC;EACpD;EAGE;EACAnT,UAAUA,CAAC6O,MAAc,EAAEwE,QAAgB;IACzC,IAAI,CAACnG,cAAc,GAAG2B,MAAM;IAC5B,IAAI,CAACjP,gBAAgB,GAAGyT,QAAQ;IAChC,IAAI,CAAC5T,SAAS,CAACiT,UAAU,CAAC;MACxB7D,MAAM,EAAEA;KACT,CAAC;IACF,IAAI,CAACN,SAAS,CAACM,MAAM,CAAC;EACxB;EAEAyE,cAAcA,CAACC,SAAiB;IAC9B,IAAI,CAACpG,gBAAgB,GAAGoG,SAAS;IACjC,IAAI,CAAC5U,SAAS,CAAC+T,UAAU,CAAC;MACxB9D,IAAI,EAAE2E;KACP,CAAC;IAEF;IACA,MAAMC,mBAAmB,GAAG,CAC1B,YAAY,EACZ,UAAU,EACV,SAAS,EACT,YAAY,CACb;IACD,IAAI,CAACA,mBAAmB,CAACJ,QAAQ,CAACG,SAAS,CAAC,EAAE;MAC5C,IAAI,CAACnQ,SAAS,CAACsP,UAAU,CAAC;QACxBxC,UAAU,EAAE;OACb,CAAC;IACJ;IAEA;IACA,MAAMuD,uBAAuB,GAAG,CAC9B,mBAAmB,EACnB,eAAe,EACf,qCAAqC,EACrC,uBAAuB,EACvB,YAAY,CAEb;IACD,IAAI,CAACA,uBAAuB,CAACL,QAAQ,CAACG,SAAS,CAAC,EAAE;MAChD,IAAI,CAACnQ,SAAS,CAACsP,UAAU,CAAC;QACxB7C,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE;OACb,CAAC;IACJ;IAEA;IACA,MAAM4D,qBAAqB,GAAG,CAC5B,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,CACtC;IACD,IAAI,CAACA,qBAAqB,CAACN,QAAQ,CAACG,SAAS,CAAC,EAAE;MAC9C,IAAI,CAACnQ,SAAS,CAACsP,UAAU,CAAC;QACxBlC,QAAQ,EAAE;OACX,CAAC;IACJ;IAEA;IACA,MAAMmD,yBAAyB,GAAG,CAChC,eAAe,EACf,YAAY,EACZ,uBAAuB,EACvB,qCAAqC,CACtC;IACD,IAAI,CAACA,yBAAyB,CAACP,QAAQ,CAACG,SAAS,CAAC,EAAE;MAClD,IAAI,CAACnQ,SAAS,CAACsP,UAAU,CAAC;QACxBpC,kBAAkB,EAAE;OACrB,CAAC;IACJ;IAEA;IACA,MAAMsD,uBAAuB,GAAG,CAAC,mBAAmB,CAAC;IACrD,IAAI,CAACA,uBAAuB,CAACR,QAAQ,CAACG,SAAS,CAAC,EAAE;MAChD,IAAI,CAACnQ,SAAS,CAACsP,UAAU,CAAC;QACxBnC,UAAU,EAAE;OACb,CAAC;IACJ;IAEA;IACA,MAAMsD,4BAA4B,GAAG,CACnC,QAAQ,EACR,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,mBAAmB,EACnB,qCAAqC,CACtC;IACD,IAAI,CAACA,4BAA4B,CAACT,QAAQ,CAACG,SAAS,CAAC,EAAE;MACrD,IAAI,CAACnQ,SAAS,CAACsP,UAAU,CAAC;QACxBtC,eAAe,EAAE;OAClB,CAAC;IACJ;IAEA;IACA,MAAM0D,yBAAyB,GAAG,CAChC,YAAY,EACZ,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,EACrC,sBAAsB,CACvB;IACD,IAAIA,yBAAyB,CAACV,QAAQ,CAACG,SAAS,CAAC,EAAE;MACjD,IAAI,CAACnQ,SAAS,CAACsP,UAAU,CAAC;QACxBrC,gBAAgB,EAAE;OACnB,CAAC;IACJ;IAEA;IACA,MAAM0D,wBAAwB,GAAG,CAC/B,UAAU,EACV,YAAY,EACZ,UAAU,EACV,OAAO,CACR;IACD,IAAI,CAACA,wBAAwB,CAACX,QAAQ,CAACG,SAAS,CAAC,EAAE;MACjD,IAAI,CAACnQ,SAAS,CAACsP,UAAU,CAAC;QACxB5B,WAAW,EAAE;OACd,CAAC;IACJ;EACF;EAEAtQ,UAAUA,CAACsO,MAAc,EAAEkF,QAAgB;IACzC,IAAI,CAACvR,gBAAgB,GAAGuR,QAAQ;IAChC,IAAI,CAACvU,SAAS,CAACiT,UAAU,CAAC;MACxB5D,MAAM,EAAEA;KACT,CAAC;IACF,IAAI,CAACgE,YAAY,CAAChE,MAAM,CAAC;EAC3B;EAEAhO,aAAaA,CAACiO,SAAiB,EAAEkF,WAAmB;IAClD,IAAI,CAACvR,mBAAmB,GAAGuR,WAAW;IACtC,IAAI,CAACxU,SAAS,CAACiT,UAAU,CAAC;MACxB3D,SAAS,EAAEA;KACZ,CAAC;EACJ;EAEA;EACA7L,gBAAgBA,CAACiQ,SAAiB,EAAEzV,KAAa;IAC/C,IAAI,CAAC0F,SAAS,CAACsP,UAAU,CAAC;MACxB,CAACS,SAAS,GAAGzV;KACd,CAAC;EACJ;EAEA;EACAwM,gBAAgBA,CAACiJ,SAAiB,EAAEzV,KAAa;IAC/C,IAAI,CAACmM,SAAS,CAAC6I,UAAU,CAAC;MACxB,CAACS,SAAS,GAAGzV;KACd,CAAC;IAEF;IACA,IAAIyV,SAAS,KAAK,eAAe,EAAE;MACjC,IAAI,CAACe,gBAAgB,EAAE;IACzB;EACF;EAEA;EACAA,gBAAgBA,CAAA;IACd,IAAI,CAACrK,SAAS,CAAC6I,UAAU,CAAC;MACxBtB,mBAAmB,EAAE,IAAI;MACzBC,gBAAgB,EAAE,IAAI;MACtBH,0BAA0B,EAAE,IAAI;MAChCC,uBAAuB,EAAE;KAC1B,CAAC;EACJ;EAEA;EACAzG,oBAAoBA,CAAA;IAClB,MAAMuG,aAAa,GAAG,IAAI,CAACpH,SAAS,CAAC/K,GAAG,CAAC,eAAe,CAAC,EAAEpB,KAAK;IAChE,OACEuT,aAAa,KAAK,MAAM,IACxBA,aAAa,KAAK,+BAA+B;EAErD;EAEA;EACAtG,2BAA2BA,CAAA;IACzB,MAAMsG,aAAa,GAAG,IAAI,CAACpH,SAAS,CAAC/K,GAAG,CAAC,eAAe,CAAC,EAAEpB,KAAK;IAChE,OACEuT,aAAa,KAAK,aAAa,IAC/BA,aAAa,KAAK,+BAA+B;EAErD;EAEA;EACAkD,yBAAyBA,CAAA;IACvB,MAAMC,QAAQ,GAAG,IAAI,CAACzV,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;IAClD,MAAM8V,mBAAmB,GAAG,CAC1B,YAAY,EACZ,UAAU,EACV,SAAS,EACT,YAAY,CACb;IACD,OAAOA,mBAAmB,CAACJ,QAAQ,CAACgB,QAAQ,CAAC;EAC/C;EAEA;EACAC,oBAAoBA,CAAA;IAClB,MAAMD,QAAQ,GAAG,IAAI,CAACzV,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;IAClD,MAAM+V,uBAAuB,GAAG,CAC9B,mBAAmB,EACnB,eAAe,EACf,qCAAqC,EACrC,uBAAuB,EACvB,YAAY,CACb;IACD,OAAOA,uBAAuB,CAACL,QAAQ,CAACgB,QAAQ,CAAC;EACnD;EAEA;EACA1K,iCAAiCA,CAAA;IAC/B,MAAM0K,QAAQ,GAAG,IAAI,CAACzV,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;IAClD,MAAMiW,yBAAyB,GAAG,CAChC,eAAe,EACf,YAAY,EACZ,uBAAuB,EACvB,qCAAqC,CACtC;IACD,OAAOA,yBAAyB,CAACP,QAAQ,CAACgB,QAAQ,CAAC;EACrD;EAEA;EACAE,uBAAuBA,CAAA;IACrB,MAAMF,QAAQ,GAAG,IAAI,CAACzV,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;IAClD,MAAMgW,qBAAqB,GAAG,CAC5B,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,CACtC;IACD,OAAOA,qBAAqB,CAACN,QAAQ,CAACgB,QAAQ,CAAC;EACjD;EAEA;EACAzK,yBAAyBA,CAAA;IACvB,MAAMyK,QAAQ,GAAG,IAAI,CAACzV,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;IAClD,MAAMkW,uBAAuB,GAAG,CAAC,mBAAmB,CAAC;IACrD,OAAOA,uBAAuB,CAACR,QAAQ,CAACgB,QAAQ,CAAC;EACnD;EAEA;EACAG,0BAA0BA,CAAA;IACxB,MAAMH,QAAQ,GAAG,IAAI,CAACzV,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;IAClD,MAAMqW,wBAAwB,GAAG,CAC/B,UAAU,EACV,YAAY,EACZ,UAAU,EACV,OAAO,CACR;IACD,OAAOA,wBAAwB,CAACX,QAAQ,CAACgB,QAAQ,CAAC;EACpD;EAEA;EACAI,8BAA8BA,CAAA;IAC5B,MAAMJ,QAAQ,GAAG,IAAI,CAACzV,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;IAClD,MAAMmW,4BAA4B,GAAG,CACnC,QAAQ,EACR,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,mBAAmB,EACnB,qCAAqC,CACtC;IACD,OAAOA,4BAA4B,CAACT,QAAQ,CAACgB,QAAQ,CAAC;EACxD;EAEA;EACA3K,+BAA+BA,CAAA;IAC7B,MAAM2K,QAAQ,GAAG,IAAI,CAACzV,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;IAClD,MAAMoW,yBAAyB,GAAG,CAChC,YAAY,EACZ,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,EACrC,sBAAsB,CACvB;IACD,OAAO,CAACA,yBAAyB,CAACV,QAAQ,CAACgB,QAAQ,CAAC;EACtD;EAEA;EACA3W,gBAAgBA,CAAC0V,SAAiB,EAAEzV,KAAa;IAC/CyU,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MAAEe,SAAS;MAAEzV;IAAK,CAAE,CAAC;IAE7D,IAAI,CAACiB,SAAS,CAAC+T,UAAU,CAAC;MACxB,CAACS,SAAS,GAAGzV;KACd,CAAC;IAEF;IACA,IAAIyV,SAAS,KAAK,cAAc,EAAE;MAChC,IAAI,CAACV,eAAe,EAAE;IACxB;IAEA;IACA,IAAIU,SAAS,KAAK,MAAM,EAAE;MACxB,IAAI,CAAChG,gBAAgB,GAAGzP,KAAK;MAC7B,IAAI,CAAC4V,cAAc,CAAC5V,KAAK,CAAC,CAAC,CAAC;IAC9B;IAEAyU,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACzT,SAAS,CAACjB,KAAK,CAAC;IAE7D;IACA,IAAI,CAACsP,GAAG,CAACwF,aAAa,EAAE;EAC1B;EAEA;EACAiC,gBAAgBA,CAACtB,SAAiB,EAAEzV,KAAa;IAC/C,IAAI,CAACkO,SAAS,CAAC8G,UAAU,CAAC;MACxB,CAACS,SAAS,GAAGzV;KACd,CAAC;EACJ;EAEAgO,UAAUA,CAACgJ,OAAiB;IAAA,IAAAC,KAAA;IAC1B,IAAI,IAAI,CAAC9I,kBAAkB,EAAE,EAAE;MAE7B;MACA,MAAM+I,YAAY,GAAG,IAAIC,QAAQ,EAAE;MAEnC;MACA9C,MAAM,CAAC+C,IAAI,CAAC,IAAI,CAACnW,SAAS,CAACjB,KAAK,CAAC,CAACqX,OAAO,CAAElX,GAAG,IAAI;QAChD+W,YAAY,CAACI,MAAM,CAACnX,GAAG,EAAE,IAAI,CAACc,SAAS,CAACjB,KAAK,CAACG,GAAG,CAAC,CAAC;MACrD,CAAC,CAAC;MAEF;MACAkU,MAAM,CAAC+C,IAAI,CAAC,IAAI,CAACrV,SAAS,CAAC/B,KAAK,CAAC,CAACqX,OAAO,CAAElX,GAAG,IAAI;QAChD+W,YAAY,CAACI,MAAM,CAACnX,GAAG,EAAE,IAAI,CAAC4B,SAAS,CAAC/B,KAAK,CAACG,GAAG,CAAC,CAAC;MACrD,CAAC,CAAC;MAEF;MACA,MAAMoX,uBAAuB,GAAG,CAAC,gBAAgB,CAAC;MAElD;MACA,MAAMb,QAAQ,GAAG,IAAI,CAACzV,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;MAElD;MACA,MAAM+V,uBAAuB,GAAG,CAC9B,mBAAmB,EACnB,eAAe,EACf,qCAAqC,EACrC,uBAAuB,EACvB,YAAY,CACb;MACD,IAAIA,uBAAuB,CAACL,QAAQ,CAACgB,QAAQ,CAAC,EAAE;QAC9Ca,uBAAuB,CAACC,IAAI,CAAC,YAAY,CAAC;QAC1CD,uBAAuB,CAACC,IAAI,CAAC,cAAc,CAAC;MAC9C;MAEA;MACA,MAAMxB,qBAAqB,GAAG,CAC5B,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,CACtC;MACD,IAAIA,qBAAqB,CAACN,QAAQ,CAACgB,QAAQ,CAAC,EAAE;QAC5Ca,uBAAuB,CAACC,IAAI,CAAC,UAAU,CAAC;MAC1C;MAEA;MACA,MAAMvB,yBAAyB,GAAG,CAChC,eAAe,EACf,YAAY,EACZ,uBAAuB,EACvB,qCAAqC,CACtC;MACD,IAAIA,yBAAyB,CAACP,QAAQ,CAACgB,QAAQ,CAAC,EAAE;QAChDa,uBAAuB,CAACC,IAAI,CAAC,oBAAoB,CAAC;MACpD;MAEA;MACA,MAAMtB,uBAAuB,GAAG,CAAC,mBAAmB,CAAC;MACrD,IAAIA,uBAAuB,CAACR,QAAQ,CAACgB,QAAQ,CAAC,EAAE;QAC9Ca,uBAAuB,CAACC,IAAI,CAAC,YAAY,CAAC;MAC5C;MAEA;MACA,MAAMrB,4BAA4B,GAAG,CACnC,QAAQ,EACR,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,mBAAmB,EACnB,qCAAqC,CACtC;MACD,IAAIA,4BAA4B,CAACT,QAAQ,CAACgB,QAAQ,CAAC,EAAE;QACnDa,uBAAuB,CAACC,IAAI,CAAC,iBAAiB,CAAC;MACjD;MAEA;MACA,MAAMpB,yBAAyB,GAAG,CAChC,YAAY,EACZ,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,qCAAqC,EACrC,sBAAsB,CACvB;MACD,IAAI,CAACA,yBAAyB,CAACV,QAAQ,CAACgB,QAAQ,CAAC,EAAE;QACjDa,uBAAuB,CAACC,IAAI,CAAC,kBAAkB,CAAC;MAClD;MAEA;MACA,MAAM1B,mBAAmB,GAAG,CAC1B,YAAY,EACZ,UAAU,EACV,SAAS,EACT,YAAY,CACb;MACD,IAAIA,mBAAmB,CAACJ,QAAQ,CAACgB,QAAQ,CAAC,EAAE;QAC1Ca,uBAAuB,CAACC,IAAI,CAAC,YAAY,CAAC;MAC5C;MAEA;MACAnD,MAAM,CAAC+C,IAAI,CAAC,IAAI,CAAC1R,SAAS,CAAC1F,KAAK,CAAC,CAACqX,OAAO,CAAElX,GAAG,IAAI;QAChD,IACEA,GAAG,KAAK,kBAAkB,IAC1B,CAACoX,uBAAuB,CAAC7B,QAAQ,CAACvV,GAAG,CAAC,EACtC;UACA+W,YAAY,CAACI,MAAM,CAACnX,GAAG,EAAE,IAAI,CAACuF,SAAS,CAAC1F,KAAK,CAACG,GAAG,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MAEF;MACA,MAAMoT,aAAa,GAAG,IAAI,CAACpH,SAAS,CAAC/K,GAAG,CAAC,eAAe,CAAC,EAAEpB,KAAK;MAEhE;MACAkX,YAAY,CAACI,MAAM,CAAC,eAAe,EAAE/D,aAAa,CAAC;MAEnD;MACA,IAAIA,aAAa,KAAK,MAAM,EAAE;QAC5B;QACA,MAAMG,mBAAmB,GAAG,IAAI,CAACvH,SAAS,CAAC/K,GAAG,CAC5C,qBAAqB,CACtB,EAAEpB,KAAK;QACR,MAAM2T,gBAAgB,GAAG,IAAI,CAACxH,SAAS,CAAC/K,GAAG,CAAC,kBAAkB,CAAC,EAAEpB,KAAK;QAEtE,IAAI0T,mBAAmB,EAAE;UACvBwD,YAAY,CAACI,MAAM,CAAC,qBAAqB,EAAE5D,mBAAmB,CAAC;QACjE;QACA,IAAIC,gBAAgB,EAAE;UACpBuD,YAAY,CAACI,MAAM,CAAC,kBAAkB,EAAE3D,gBAAgB,CAAC;QAC3D;MACF,CAAC,MAAM,IAAIJ,aAAa,KAAK,aAAa,EAAE;QAC1C;QACA,MAAMC,0BAA0B,GAAG,IAAI,CAACrH,SAAS,CAAC/K,GAAG,CACnD,4BAA4B,CAC7B,EAAEpB,KAAK;QACR,MAAMyT,uBAAuB,GAAG,IAAI,CAACtH,SAAS,CAAC/K,GAAG,CAChD,yBAAyB,CAC1B,EAAEpB,KAAK;QAER,IAAIwT,0BAA0B,EAAE;UAC9B0D,YAAY,CAACI,MAAM,CACjB,4BAA4B,EAC5B9D,0BAA0B,CAC3B;QACH;QACA,IAAIC,uBAAuB,EAAE;UAC3ByD,YAAY,CAACI,MAAM,CACjB,yBAAyB,EACzB7D,uBAAuB,CACxB;QACH;MACF,CAAC,MAAM,IAAIF,aAAa,KAAK,+BAA+B,EAAE;QAC5D;QACA,MAAMG,mBAAmB,GAAG,IAAI,CAACvH,SAAS,CAAC/K,GAAG,CAC5C,qBAAqB,CACtB,EAAEpB,KAAK;QACR,MAAM2T,gBAAgB,GAAG,IAAI,CAACxH,SAAS,CAAC/K,GAAG,CAAC,kBAAkB,CAAC,EAAEpB,KAAK;QACtE,MAAMwT,0BAA0B,GAAG,IAAI,CAACrH,SAAS,CAAC/K,GAAG,CACnD,4BAA4B,CAC7B,EAAEpB,KAAK;QACR,MAAMyT,uBAAuB,GAAG,IAAI,CAACtH,SAAS,CAAC/K,GAAG,CAChD,yBAAyB,CAC1B,EAAEpB,KAAK;QAER,IAAI0T,mBAAmB,EAAE;UACvBwD,YAAY,CAACI,MAAM,CAAC,qBAAqB,EAAE5D,mBAAmB,CAAC;QACjE;QACA,IAAIC,gBAAgB,EAAE;UACpBuD,YAAY,CAACI,MAAM,CAAC,kBAAkB,EAAE3D,gBAAgB,CAAC;QAC3D;QACA,IAAIH,0BAA0B,EAAE;UAC9B0D,YAAY,CAACI,MAAM,CACjB,4BAA4B,EAC5B9D,0BAA0B,CAC3B;QACH;QACA,IAAIC,uBAAuB,EAAE;UAC3ByD,YAAY,CAACI,MAAM,CACjB,yBAAyB,EACzB7D,uBAAuB,CACxB;QACH;MACF;MAEA;MACAY,MAAM,CAAC+C,IAAI,CAAC,IAAI,CAAClJ,SAAS,CAAClO,KAAK,CAAC,CAACqX,OAAO,CAAElX,GAAG,IAAI;QAChD,IAAIA,GAAG,KAAK,aAAa,EAAE;UACzB+W,YAAY,CAACI,MAAM,CAACnX,GAAG,EAAE,IAAI,CAAC+N,SAAS,CAAClO,KAAK,CAACG,GAAG,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MAEF;MACA,MAAMsX,iBAAiB,GAAQ,EAAE;MAEjC;MACAF,uBAAuB,CAACF,OAAO,CAAEK,KAAK,IAAI;QACxC,MAAM1X,KAAK,GAAG,IAAI,CAAC0F,SAAS,CAACtE,GAAG,CAACsW,KAAK,CAAC,EAAE1X,KAAK;QAC9C,IAAIA,KAAK,EAAE;UACTyX,iBAAiB,CAACC,KAAK,CAAC,GAAG1X,KAAK;QAClC;MACF,CAAC,CAAC;MAEF;MACA,MAAMqW,wBAAwB,GAAG,CAC/B,UAAU,EACV,YAAY,EACZ,UAAU,EACV,OAAO,CACR;MACD,IAAIA,wBAAwB,CAACX,QAAQ,CAACgB,QAAQ,CAAC,EAAE;QAC/C,MAAMtD,WAAW,GAAG,IAAI,CAAClF,SAAS,CAAC9M,GAAG,CAAC,aAAa,CAAC,EAAEpB,KAAK;QAC5D,IAAIoT,WAAW,EAAE;UACfqE,iBAAiB,CAAC,aAAa,CAAC,GAAGrE,WAAW;QAChD;MACF;MAEA;MACAiB,MAAM,CAAC+C,IAAI,CAACK,iBAAiB,CAAC,CAACJ,OAAO,CAAElX,GAAG,IAAI;QAC7C+W,YAAY,CAACI,MAAM,CACjB,qBAAqBnX,GAAG,GAAG,EAC3BsX,iBAAiB,CAACtX,GAAG,CAAC,CACvB;MACH,CAAC,CAAC;MAEF;MACA,MAAMwX,UAAU,GAAG,CACjB,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,sBAAsB,CACvB;MACDA,UAAU,CAACN,OAAO,CAAEK,KAAK,IAAI;QAC3B,MAAME,KAAK,GAAG,IAAI,CAAC/J,SAAS,CAACzM,GAAG,CAACsW,KAAK,CAAC,EAAE1X,KAAK;QAC9C,IAAI4X,KAAK,IAAIA,KAAK,CAAClW,MAAM,EAAE;UACzB,MAAMmW,UAAU,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACnC,QAAQ,CAACgC,KAAK,CAAC;UAEvD,IAAIG,UAAU,EAAE;YACdD,KAAK,CAACP,OAAO,CAAES,IAAU,IAAI;cAC3BZ,YAAY,CAACI,MAAM,CAAC,GAAGI,KAAK,IAAI,EAAEI,IAAI,CAAC;YACzC,CAAC,CAAC;UACJ,CAAC,MAAM;YACLZ,YAAY,CAACI,MAAM,CAACI,KAAK,EAAEE,KAAK,CAAC,CAAC,CAAC,CAAC;UACtC;QACF;MACF,CAAC,CAAC;MAEF;MACA,MAAMG,cAAc,GAAG,IAAI,CAACrS,SAAS,CAACtE,GAAG,CAAC,kBAAkB,CAAC,EAAEpB,KAAK;MACpE,MAAMgY,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,GAClDA,cAAc,GACd,EAAE;MAEN;MACAC,gBAAgB,CAACX,OAAO,CAAC,CAACc,SAAS,EAAEC,KAAK,KAAI;QAC5ClB,YAAY,CAACI,MAAM,CAAC,oBAAoBc,KAAK,GAAG,EAAED,SAAS,CAAC;MAC9D,CAAC,CAAC;MAEFjB,YAAY,CAACI,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC1H,QAAQ,CAACyI,QAAQ,EAAE,CAAC;MAEzD;MACA,IAAGrB,OAAO,EAAC;QACTE,YAAY,CAACI,MAAM,CAAC,iBAAiB,EAAE,GAAG,CAAC;MAC7C;MAEA;MACA,MAAMgB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC;MACrD,IAAIF,MAAM,EAAE;QACVA,MAAM,CAACG,SAAS,CAACC,GAAG,CAAC,aAAa,CAAC;MACrC;MAEA,IAAI,CAACrJ,eAAe,CAACsJ,cAAc,CAACzB,YAAY,CAAC,CAAChD,SAAS,CAAC;QAC1DC,IAAI;UAAA,IAAAyE,IAAA,GAAAC,iBAAA,CAAE,WAAOzE,QAAQ,EAAI;YACvBK,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEN,QAAQ,CAAC;YACjD,MAAMvV,IAAI,CAACia,IAAI,CAAC,0BAA0B,EAAE,EAAE,EAAE1E,QAAQ,CAAC2E,MAAM,CAAC;YAEhE9B,KAAI,CAAC7H,MAAM,CAAC4J,QAAQ,CAAC,CAAC,2BAA2B,CAAC,EAAE;cAClDC,WAAW,EAAE;gBAAEC,OAAO,EAAE;cAAK;aAC9B,CAAC;UACJ,CAAC;UAAA,gBAPD/E,IAAIA,CAAAgF,EAAA;YAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;UAAA;QAAA,GAOH;QACD1E,KAAK,EAAGC,GAAG,IAAI;UACbH,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEC,GAAG,CAAC;UAC9C/V,IAAI,CAACia,IAAI,CAAClE,GAAG,CAAC0E,OAAO,EAAE,EAAE,EAAE1E,GAAG,CAACmE,MAAM,CAAC;UAEtC;UACA,IAAIT,MAAM,EAAE;YACVA,MAAM,CAACG,SAAS,CAACc,MAAM,CAAC,aAAa,CAAC;UACxC;QACF,CAAC;QACD1E,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACvF,GAAG,CAACwF,aAAa,EAAE;UACxB;UACA,IAAIwD,MAAM,EAAE;YACVA,MAAM,CAACG,SAAS,CAACc,MAAM,CAAC,aAAa,CAAC;UACxC;QACF;OACD,CAAC;IACJ;EACF;EAEAhL,MAAMA,CAAA;IACJ,IAAI,CAACa,MAAM,CAAC4J,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;EACrD;EAEA1L,YAAYA,CAACkM,KAAU,EAAE/D,SAAiB;IACxC,IAAI+D,KAAK,CAACC,MAAM,CAAC7B,KAAK,IAAI4B,KAAK,CAACC,MAAM,CAAC7B,KAAK,CAAClW,MAAM,EAAE;MACnD,MAAMkW,KAAK,GAAGK,KAAK,CAACyB,IAAI,CAACF,KAAK,CAACC,MAAM,CAAC7B,KAAK,CAAC;MAC5C,IAAI,CAAC/J,SAAS,CAACmH,UAAU,CAAC;QACxB,CAACS,SAAS,GAAGmC;OACd,CAAC;MAEFnD,OAAO,CAACC,GAAG,CAAC,GAAGe,SAAS,KAAKmC,KAAK,CAAClW,MAAM,iBAAiB,CAAC;IAC7D;EACF;EAEAwL,YAAYA,CAACuI,SAAiB;IAC5B,MAAMmC,KAAK,GAAG,IAAI,CAAC/J,SAAS,CAACzM,GAAG,CAACqU,SAAS,CAAC,EAAEzV,KAAK;IAClD,OAAO4X,KAAK,IAAIK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAAClW,MAAM,GAAG,CAAC;EACzD;EAEA;EACAyM,kBAAkBA,CAAA;IAChB,MAAMwL,WAAW,GAAG,IAAI,CAAC3F,cAAc,EAAE;IAEzC;IACA,IAAI,IAAI,CAAC/E,WAAW,KAAK,CAAC,EAAE;MAC1B,MAAM8B,YAAY,GAAG,IAAI,CAAC9P,SAAS,CAACG,GAAG,CAAC,cAAc,CAAC,EAAEpB,KAAK;MAC9D,MAAM0W,QAAQ,GAAG,IAAI,CAACzV,SAAS,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEpB,KAAK;MAClD,MAAM4Z,OAAO,GAAG,CAAC,EAAE7I,YAAY,IAAI2F,QAAQ,CAAC;MAC5CjC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;QAAE3D,YAAY;QAAE2F,QAAQ;QAAEkD;MAAO,CAAE,CAAC;MACtE,OAAOA,OAAO;IAChB;IAEA;IACA,IAAI,IAAI,CAAC3K,WAAW,KAAK,CAAC,EAAE;MAC1B,OAAO,IAAI,CAAC4K,gBAAgB,EAAE;IAChC;IAEA,OAAOF,WAAW,CAACvL,KAAK;EAC1B;EAEA;EACAyL,gBAAgBA,CAAA;IACd,MAAMC,IAAI,GAAG,IAAI,CAACpU,SAAS;IAC3B,MAAMqU,YAAY,GAAG,IAAI,CAACvE,eAAe,EAAE;IAE3C;IACA,MAAMwE,cAAc,GAAG,CAAC,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,CAAC;IAGpH,KAAK,MAAMvE,SAAS,IAAIuE,cAAc,EAAE;MACtC,IAAID,YAAY,CAACrE,QAAQ,CAACD,SAAS,CAAC,EAAE;QACpC,MAAMwE,OAAO,GAAGH,IAAI,CAAC1Y,GAAG,CAACqU,SAAS,CAAC;QACnC,IAAI,CAACwE,OAAO,IAAIA,OAAO,CAACxW,OAAO,EAAE;UAC/BgR,OAAO,CAACC,GAAG,CAAC,uCAAuCe,SAAS,EAAE,EAAEwE,OAAO,EAAEjY,MAAM,CAAC;UAChF,OAAO,KAAK;QACd;MACF;IACF;IAEA;IACA,MAAMkY,iBAAiB,GAAG,CAAC,YAAY,EAAE,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,YAAY,EAAE,UAAU,CAAC;IAE9I,KAAK,MAAMzE,SAAS,IAAIyE,iBAAiB,EAAE;MACzC,IAAIH,YAAY,CAACrE,QAAQ,CAACD,SAAS,CAAC,IAAI,IAAI,CAAC0E,0BAA0B,CAAC1E,SAAS,CAAC,EAAE;QAClF,MAAMwE,OAAO,GAAGH,IAAI,CAAC1Y,GAAG,CAACqU,SAAS,CAAC;QACnC,IAAI,CAACwE,OAAO,IAAIA,OAAO,CAACxW,OAAO,EAAE;UAC/BgR,OAAO,CAACC,GAAG,CAAC,mDAAmDe,SAAS,EAAE,EAAEwE,OAAO,EAAEjY,MAAM,CAAC;UAC5F,OAAO,KAAK;QACd;MACF;IACF;IAEAyS,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC,OAAO,IAAI;EACb;EAEA;EACAyF,0BAA0BA,CAAC1E,SAAiB;IAC1C,QAAQA,SAAS;MACf,KAAK,YAAY;QACf,OAAO,IAAI,CAACgB,yBAAyB,EAAE;MACzC,KAAK,aAAa;QAChB,OAAO,IAAI,CAACI,0BAA0B,EAAE;MAC1C,KAAK,iBAAiB;QACpB,OAAO,IAAI,CAACC,8BAA8B,EAAE;MAC9C,KAAK,kBAAkB;QACrB,OAAO,IAAI,CAAC/K,+BAA+B,EAAE;MAC/C,KAAK,oBAAoB;QACvB,OAAO,IAAI,CAACC,iCAAiC,EAAE;MACjD,KAAK,YAAY;QACf,OAAO,IAAI,CAACC,yBAAyB,EAAE;MACzC,KAAK,UAAU;QACb,OAAO,IAAI,CAAC2K,uBAAuB,EAAE;MACvC;QACE,OAAO,KAAK;IAChB;EACF;EAEA;EACAnI,QAAQA,CAAA;IACN,IAAI,IAAI,CAACQ,WAAW,GAAG,IAAI,CAACM,UAAU,EAAE;MACtC,IAAI,CAACN,WAAW,EAAE;IACpB;EACF;EAEA;EACAvP,QAAQA,CAAA;IACN,IAAI,IAAI,CAACuP,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA3N,iBAAiBA,CAACnB,GAAW;IAC3B,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAO,EAAE;IAE9C,OAAOA,GAAG,CACPia,KAAK,CAAC,GAAG,CAAC,CACV5F,GAAG,CAAE6F,IAAI,IACRA,IAAI,CAACC,IAAI,EAAE,GAAGD,IAAI,CAAC,CAAC,CAAC,CAACE,WAAW,EAAE,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAG,EAAE,CACvE,CACAC,IAAI,CAAC,GAAG,CAAC;EACd;EAEA7Q,eAAeA,CAAC7J,KAAa;IAC3B,MAAMoY,KAAK,GAAG,IAAI,CAACvI,mBAAmB,CAAC8K,OAAO,CAAC3a,KAAK,CAAC;IAErD,IAAIoY,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACvI,mBAAmB,CAAC+K,MAAM,CAACxC,KAAK,EAAE,CAAC,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACvI,mBAAmB,CAAC2H,IAAI,CAACxX,KAAK,CAAC;IACtC;IAEA;IACA,IAAI,CAAC0F,SAAS,CAACsP,UAAU,CAAC;MACxB7B,gBAAgB,EAAE,CAAC,GAAG,IAAI,CAACtD,mBAAmB;KAC/C,CAAC;EACJ;EAEA;EACA1F,sBAAsBA,CAACqP,KAAU;IAC/B,IAAIA,KAAK,CAACC,MAAM,CAACoB,OAAO,EAAE;MACxB;MACA,IAAI,CAAChL,mBAAmB,GAAG,IAAI,CAACvF,qBAAqB,CAACkK,GAAG,CAAEsG,CAAC,IAAKA,CAAC,CAAC9a,KAAK,CAAC;IAC3E,CAAC,MAAM;MACL;MACA,IAAI,CAAC6P,mBAAmB,GAAG,EAAE;IAC/B;IAEA,IAAI,CAACnK,SAAS,CAACsP,UAAU,CAAC;MACxB7B,gBAAgB,EAAE,CAAC,GAAG,IAAI,CAACtD,mBAAmB;KAC/C,CAAC;EACJ;EAEA/F,mBAAmBA,CAAC9J,KAAa;IAC/B,OAAO,IAAI,CAAC6P,mBAAmB,CAAC6F,QAAQ,CAAC1V,KAAK,CAAC;EACjD;EAEAqK,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACwF,mBAAmB,CAACnO,MAAM,KAAK,CAAC,EAAE;MACzC,OAAO,EAAE;IACX;IAEA,IAAI,IAAI,CAACmO,mBAAmB,CAACnO,MAAM,KAAK,CAAC,EAAE;MACzC,MAAMyW,SAAS,GAAG,IAAI,CAAC7N,qBAAqB,CAACyQ,IAAI,CAC9CD,CAAC,IAAKA,CAAC,CAAC9a,KAAK,KAAK,IAAI,CAAC6P,mBAAmB,CAAC,CAAC,CAAC,CAC/C;MACD,OAAOsI,SAAS,GAAGA,SAAS,CAAChY,GAAG,GAAG,EAAE;IACvC;IAEA,OAAO,GAAG,IAAI,CAAC0P,mBAAmB,CAACnO,MAAM,uBAAuB;EAClE;EAEA;EACAR,mBAAmBA,CAAClB,KAAa;IAC/B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IACrB,MAAMuV,MAAM,GAAG,IAAI,CAAClU,eAAe,CAAC0Z,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAChb,KAAK,KAAKA,KAAK,CAAC;IACpE,OAAOuV,MAAM,GAAGA,MAAM,CAACpV,GAAG,GAAG,EAAE;EACjC;EAIA;EACA8a,eAAeA,CAACjb,KAAa;IAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IACrB,MAAM0W,QAAQ,GAAG,IAAI,CAACjV,iBAAiB,CAACsZ,IAAI,CAACG,IAAI,IAAIA,IAAI,CAAClb,KAAK,KAAKA,KAAK,CAAC;IAC1E,OAAO0W,QAAQ,GAAGA,QAAQ,CAACvW,GAAG,GAAG,EAAE;EACrC;;qCA51CW+O,oBAAoB,EAAApQ,EAAA,CAAAqc,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvc,EAAA,CAAAqc,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAzc,EAAA,CAAAqc,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA3c,EAAA,CAAAqc,iBAAA,CAAArc,EAAA,CAAA4c,iBAAA;EAAA;;UAApBxM,oBAAoB;IAAAyM,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCtCjCnd,EAAA,CAAAE,cAAA,aAAuB;QACrBF,EAAA,CAAA4B,SAAA,0BAAgE;QAClE5B,EAAA,CAAAI,YAAA,EAAM;QAMAJ,EAJN,CAAAE,cAAA,aAA4B,aACE,aACsD,aAEhD;QAgC5BF,EA/BA,CAAA6B,UAAA,IAAAwb,4CAAA,0BAAwC,IAAAC,4CAAA,0BAMA,IAAAC,4CAAA,0BAMA,IAAAC,4CAAA,0BAMA,KAAAC,6CAAA,0BAMA,KAAAC,6CAAA,0BAOA;QAQtC1d,EADF,CAAAE,cAAA,cAAmE,eAC9B;QAAAF,EAAA,CAAAG,MAAA,IAAsB;QAAAH,EAAA,CAAAI,YAAA,EAAO;QAChEJ,EAAA,CAAAE,cAAA,gBAA8B;QAAAF,EAAA,CAAAG,MAAA,UAAE;QAAAH,EAAA,CAAAI,YAAA,EAAO;QACvCJ,EAAA,CAAAE,cAAA,gBAAyB;QAAAF,EAAA,CAAAG,MAAA,IAAgB;QAC3CH,EAD2C,CAAAI,YAAA,EAAO,EAC5C;QAENJ,EAAA,CAAA6B,UAAA,KAAA8b,oCAAA,kBAA2F;QAI3F3d,EAAA,CAAAE,cAAA,eAA0D;QACxDF,EAAA,CAAA4B,SAAA,eACiE;QAErE5B,EADE,CAAAI,YAAA,EAAM,EACF;QAGNJ,EAAA,CAAAE,cAAA,gBAAuC;QAg9BrCF,EA98BA,CAAA6B,UAAA,KAAA+b,oCAAA,mBAAuD,KAAAC,oCAAA,oBAsFA,KAAAC,oCAAA,oBA4OA,KAAAC,oCAAA,kBAmbA,KAAAC,oCAAA,mBAiFA,KAAAC,oCAAA,mBA0EA,KAAAC,oCAAA,kBA0CqB,KAAAC,oCAAA,kBAYD,KAAAC,oCAAA,kBAQ4B;QAmB/Gpe,EAHM,CAAAI,YAAA,EAAO,EACH,EACF,EACF;;;QAliCcJ,EAAA,CAAAmB,SAAA,EAA0B;QAA1BnB,EAAA,CAAAkC,UAAA,2BAA0B;QAQvBlC,EAAA,CAAAmB,SAAA,GAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAMvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAMvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAMvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAMvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAOvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAQDnQ,EAAA,CAAAmB,SAAA,GAAsB;QAAtBnB,EAAA,CAAAmD,kBAAA,UAAAia,GAAA,CAAAjN,WAAA,KAAsB;QAEhCnQ,EAAA,CAAAmB,SAAA,GAAgB;QAAhBnB,EAAA,CAAAoB,iBAAA,CAAAgc,GAAA,CAAA3M,UAAA,CAAgB;QAGrCzQ,EAAA,CAAAmB,SAAA,EAAqB;QAArBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,KAAqB;QAK+BnQ,EAAA,CAAAmB,SAAA,GAAsD;QAAtDnB,EAAA,CAAAqe,WAAA,UAAAjB,GAAA,CAAAjN,WAAA,GAAAiN,GAAA,CAAA3M,UAAA,aAAsD;QAQ1GzQ,EAAA,CAAAmB,SAAA,GAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAsFvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QA4OvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAmbvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAiFvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QA0EvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QA0CvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAYvBnQ,EAAA,CAAAmB,SAAA,EAAuB;QAAvBnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,OAAuB;QAQvBnQ,EAAA,CAAAmB,SAAA,EAAmD;QAAnDnB,EAAA,CAAAkC,UAAA,SAAAkb,GAAA,CAAAjN,WAAA,QAAAiN,GAAA,CAAAjN,WAAA,KAAAiN,GAAA,CAAA3M,UAAA,CAAmD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}