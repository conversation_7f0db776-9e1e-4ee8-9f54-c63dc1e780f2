{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: false,\n  appVersion: 'v8.2.4',\n  USERDATA_KEY: 'authf649fc9a5f55',\n  isMockEnabled: true,\n  // apiUrl: 'api',\n  apiUrl: 'http://127.0.0.1:8000/api/v1',\n  // apiUrl: 'https://e71a-102-45-209-76.ngrok-free.app/api/v1',\n  appThemeName: 'Metronic',\n  appPurchaseUrl: 'https://1.envato.market/EA4JP',\n  appHTMLIntegration: 'https://preview.keenthemes.com/metronic8/demo1/documentation/base/helpers/flex-layouts.html',\n  appPreviewUrl: 'https://preview.keenthemes.com/metronic8/angular/demo1/',\n  appPreviewAngularUrl: 'https://preview.keenthemes.com/metronic8/angular/demo1',\n  appPreviewDocsUrl: 'https://preview.keenthemes.com/metronic8/angular/docs',\n  appPreviewChangelogUrl: 'https://preview.keenthemes.com/metronic8/angular/docs/changelog',\n  TABLE_LIMIT: 10,\n  TABLE_ORDER_LIMIT: 10,\n  appDemos: {\n    demo1: {\n      title: 'Demo 1',\n      description: 'Default Dashboard',\n      published: true,\n      thumbnail: './assets/media/demos/demo1.png'\n    },\n    demo2: {\n      title: 'Demo 2',\n      description: 'SaaS Application',\n      published: true,\n      thumbnail: './assets/media/demos/demo2.png'\n    },\n    demo3: {\n      title: 'Demo 3',\n      description: 'New Trend',\n      published: true,\n      thumbnail: './assets/media/demos/demo3.png'\n    },\n    demo4: {\n      title: 'Demo 4',\n      description: 'Intranet Application',\n      published: true,\n      thumbnail: './assets/media/demos/demo4.png'\n    },\n    demo5: {\n      title: 'Demo 5',\n      description: 'Support Forum',\n      published: false,\n      thumbnail: './assets/media/demos/demo5.png'\n    },\n    demo6: {\n      title: 'Demo 6',\n      description: 'Admin Backend',\n      published: true,\n      thumbnail: './assets/media/demos/demo6.png'\n    },\n    demo7: {\n      title: 'Demo 7',\n      description: 'CRM Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo7.png'\n    },\n    demo8: {\n      title: 'Demo 8',\n      description: 'Core Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo8.png'\n    },\n    demo9: {\n      title: 'Demo 9',\n      description: 'Fancy Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo9.png'\n    },\n    demo10: {\n      title: 'Demo 10',\n      description: 'Modern Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo10.png'\n    },\n    demo11: {\n      title: 'Demo 11',\n      description: 'Light Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo11.png'\n    },\n    demo12: {\n      title: 'Demo 12',\n      description: 'Reporting System',\n      published: false,\n      thumbnail: './assets/media/demos/demo12.png'\n    },\n    demo13: {\n      title: 'Demo 13',\n      description: 'Classic Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo13.png'\n    },\n    demo14: {\n      title: 'Demo 14',\n      description: 'Creative Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo14.png'\n    },\n    demo15: {\n      title: 'Demo 15',\n      description: 'Minimalistic Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo15.png'\n    },\n    demo16: {\n      title: 'Demo 16',\n      description: 'Modern Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo16.png'\n    },\n    demo17: {\n      title: 'Demo 17',\n      description: 'Backend System',\n      published: false,\n      thumbnail: './assets/media/demos/demo17.png'\n    },\n    demo18: {\n      title: 'Demo 18',\n      description: 'System Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo18.png'\n    },\n    demo19: {\n      title: 'Demo 19',\n      description: 'Light Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo19.png'\n    },\n    demo20: {\n      title: 'Demo 20',\n      description: 'Monochrome Dashboard',\n      published: false,\n      thumbnail: './assets/media/demos/demo20.png'\n    }\n  }\n};\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.", "map": {"version": 3, "names": ["environment", "production", "appVersion", "USERDATA_KEY", "isMockEnabled", "apiUrl", "appThemeName", "appPurchaseUrl", "appHTMLIntegration", "appPreviewUrl", "appPreviewAngularUrl", "appPreviewDocsUrl", "appPreviewChangelogUrl", "TABLE_LIMIT", "TABLE_ORDER_LIMIT", "appDemos", "demo1", "title", "description", "published", "thumbnail", "demo2", "demo3", "demo4", "demo5", "demo6", "demo7", "demo8", "demo9", "demo10", "demo11", "demo12", "demo13", "demo14", "demo15", "demo16", "demo17", "demo18", "demo19", "demo20"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nexport const environment = {\r\n  production: false,\r\n  appVersion: 'v8.2.4',\r\n  USERDATA_KEY: 'authf649fc9a5f55',\r\n  isMockEnabled: true,\r\n  // apiUrl: 'api',\r\n  apiUrl: 'http://127.0.0.1:8000/api/v1',\r\n  // apiUrl: 'https://e71a-102-45-209-76.ngrok-free.app/api/v1',\r\n  appThemeName: 'Metronic',\r\n  appPurchaseUrl: 'https://1.envato.market/EA4JP',\r\n  appHTMLIntegration: 'https://preview.keenthemes.com/metronic8/demo1/documentation/base/helpers/flex-layouts.html',\r\n  appPreviewUrl: 'https://preview.keenthemes.com/metronic8/angular/demo1/',\r\n  appPreviewAngularUrl: 'https://preview.keenthemes.com/metronic8/angular/demo1',\r\n  appPreviewDocsUrl: 'https://preview.keenthemes.com/metronic8/angular/docs',\r\n  appPreviewChangelogUrl: 'https://preview.keenthemes.com/metronic8/angular/docs/changelog',\r\n  TABLE_LIMIT: 10,\r\n  TABLE_ORDER_LIMIT: 10,\r\n\r\n  appDemos: {\r\n    demo1: {\r\n      title: 'Demo 1',\r\n      description: 'Default Dashboard',\r\n      published: true,\r\n      thumbnail: './assets/media/demos/demo1.png',\r\n    },\r\n\r\n    demo2: {\r\n      title: 'Demo 2',\r\n      description: 'SaaS Application',\r\n      published: true,\r\n      thumbnail: './assets/media/demos/demo2.png',\r\n    },\r\n\r\n    demo3: {\r\n      title: 'Demo 3',\r\n      description: 'New Trend',\r\n      published: true,\r\n      thumbnail: './assets/media/demos/demo3.png',\r\n    },\r\n\r\n    demo4: {\r\n      title: 'Demo 4',\r\n      description: 'Intranet Application',\r\n      published: true,\r\n      thumbnail: './assets/media/demos/demo4.png',\r\n    },\r\n\r\n    demo5: {\r\n      title: 'Demo 5',\r\n      description: 'Support Forum',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo5.png',\r\n    },\r\n\r\n    demo6: {\r\n      title: 'Demo 6',\r\n      description: 'Admin Backend',\r\n      published: true,\r\n      thumbnail: './assets/media/demos/demo6.png',\r\n    },\r\n\r\n    demo7: {\r\n      title: 'Demo 7',\r\n      description: 'CRM Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo7.png',\r\n    },\r\n\r\n    demo8: {\r\n      title: 'Demo 8',\r\n      description: 'Core Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo8.png',\r\n    },\r\n\r\n    demo9: {\r\n      title: 'Demo 9',\r\n      description: 'Fancy Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo9.png',\r\n    },\r\n\r\n    demo10: {\r\n      title: 'Demo 10',\r\n      description: 'Modern Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo10.png',\r\n    },\r\n\r\n    demo11: {\r\n      title: 'Demo 11',\r\n      description: 'Light Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo11.png',\r\n    },\r\n\r\n    demo12: {\r\n      title: 'Demo 12',\r\n      description: 'Reporting System',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo12.png',\r\n    },\r\n\r\n    demo13: {\r\n      title: 'Demo 13',\r\n      description: 'Classic Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo13.png',\r\n    },\r\n\r\n    demo14: {\r\n      title: 'Demo 14',\r\n      description: 'Creative Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo14.png',\r\n    },\r\n\r\n    demo15: {\r\n      title: 'Demo 15',\r\n      description: 'Minimalistic Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo15.png',\r\n    },\r\n\r\n    demo16: {\r\n      title: 'Demo 16',\r\n      description: 'Modern Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo16.png',\r\n    },\r\n\r\n    demo17: {\r\n      title: 'Demo 17',\r\n      description: 'Backend System',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo17.png',\r\n    },\r\n\r\n    demo18: {\r\n      title: 'Demo 18',\r\n      description: 'System Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo18.png',\r\n    },\r\n\r\n    demo19: {\r\n      title: 'Demo 19',\r\n      description: 'Light Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo19.png',\r\n    },\r\n\r\n    demo20: {\r\n      title: 'Demo 20',\r\n      description: 'Monochrome Dashboard',\r\n      published: false,\r\n      thumbnail: './assets/media/demos/demo20.png',\r\n    },\r\n  },\r\n};\r\n\r\n/*\r\n * For easier debugging in development mode, you can import the following file\r\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n *\r\n * This import should be commented out in production mode because it will have a negative impact\r\n * on performance if an error is thrown.\r\n */\r\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,QAAQ;EACpBC,YAAY,EAAE,kBAAkB;EAChCC,aAAa,EAAE,IAAI;EACnB;EACAC,MAAM,EAAE,8BAA8B;EACtC;EACAC,YAAY,EAAE,UAAU;EACxBC,cAAc,EAAE,+BAA+B;EAC/CC,kBAAkB,EAAE,6FAA6F;EACjHC,aAAa,EAAE,yDAAyD;EACxEC,oBAAoB,EAAE,wDAAwD;EAC9EC,iBAAiB,EAAE,uDAAuD;EAC1EC,sBAAsB,EAAE,iEAAiE;EACzFC,WAAW,EAAE,EAAE;EACfC,iBAAiB,EAAE,EAAE;EAErBC,QAAQ,EAAE;IACRC,KAAK,EAAE;MACLC,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,mBAAmB;MAChCC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE;KACZ;IAEDC,KAAK,EAAE;MACLJ,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,kBAAkB;MAC/BC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE;KACZ;IAEDE,KAAK,EAAE;MACLL,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,WAAW;MACxBC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE;KACZ;IAEDG,KAAK,EAAE;MACLN,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,sBAAsB;MACnCC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE;KACZ;IAEDI,KAAK,EAAE;MACLP,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,eAAe;MAC5BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDK,KAAK,EAAE;MACLR,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,eAAe;MAC5BC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE;KACZ;IAEDM,KAAK,EAAE;MACLT,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,eAAe;MAC5BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDO,KAAK,EAAE;MACLV,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,gBAAgB;MAC7BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDQ,KAAK,EAAE;MACLX,KAAK,EAAE,QAAQ;MACfC,WAAW,EAAE,iBAAiB;MAC9BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDS,MAAM,EAAE;MACNZ,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,kBAAkB;MAC/BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDU,MAAM,EAAE;MACNb,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,iBAAiB;MAC9BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDW,MAAM,EAAE;MACNd,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,kBAAkB;MAC/BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDY,MAAM,EAAE;MACNf,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,mBAAmB;MAChCC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDa,MAAM,EAAE;MACNhB,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,oBAAoB;MACjCC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDc,MAAM,EAAE;MACNjB,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,wBAAwB;MACrCC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDe,MAAM,EAAE;MACNlB,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,kBAAkB;MAC/BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDgB,MAAM,EAAE;MACNnB,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,gBAAgB;MAC7BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDiB,MAAM,EAAE;MACNpB,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,kBAAkB;MAC/BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDkB,MAAM,EAAE;MACNrB,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,iBAAiB;MAC9BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAEDmB,MAAM,EAAE;MACNtB,KAAK,EAAE,SAAS;MAChBC,WAAW,EAAE,sBAAsB;MACnCC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;;;CAGhB;AAED;;;;;;;AAOA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}